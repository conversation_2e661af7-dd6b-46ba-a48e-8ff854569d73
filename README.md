# PAM API V2

This api will be used to service PAM data to other apps, including Salesforce and Skuid. It utilizes the V2 suffix to disambiguate between itself and the existing API inside the sms repo.

## API Endpoints
https://github.com/CINBCUniversal/pam-api/wiki/API-Endpoints

## Local Setup for using Ruby MRI with Oracle:

### Running pam-api with Docker
#### Step 1: Bundle without Oracle Instant Client
```
bundle install --without=docker
```
#### Step 2: Package Gems
```
bundle package --all
```
#### Step 3: Build Docker container
```
docker-compose build
```
#### Step 4: Run app
```
docker-compose up
```

From then on you can start the app using `docker-compose up`, by default the app runs on port 3000, this can be changed in `docker-compose.yml`

If you want to run rails commands (migrations, rails console etc.) use the following:
```
docker-compose run web COMMAND
```
Example running rails console:
```
docker-compose run web rails c
```

### Troubleshooting Bundler Errors
```
Could not find concurrent-ruby-1.1.5 in any of the sources
Run `bundle install` to install missing gems.
```

If you run into the above error or similar while trying to bundle please try the following:
1) In `.bundle/config`, remove the line: `BUNDLE_CACHE_ALL: "true"`
2) Re-run Steps 1-4 above:
```
bundle install --without=docker
bundle package --all
docker-compose build
docker-compose up
```

#### Using pry with Docker:

After inserting `binding.pry` at a breakpoint in your code, open a new terminal window or tab and type `docker attach pam-api_app`.  

Once you begin typing you should see the rails console appear as normal.

## Local Database Setup

See [Local DB Setup with Docker](https://github.com/CINBCUniversal/pam-api/wiki/Local-DB-setup-with-Docker)

## Run Linting/Specs

The default rake task delegates to running all linting/spec tasks:

```
bundle exec rake
```
## Seed Data from DEV

1. open the file `pam.rake` and look for the `PAM_SEED_TABLES` list
2. add or remove the tables based on what you wish to import
3. run the rake task `db:seed:pam['live']`


## Troubleshooting

If you run into any issues with initial migrations, please ensure the following settings are added to your `config/application.rb`

```
# Do not use the plural form of table names per NBCU IT conventions
config.active_record.pluralize_table_names = false

# Use <table_name>_id for primary keys as well
config.active_record.primary_key_prefix_type = :table_name_with_underscore
```

## Deploying a Feature Branch

Sometimes you may want to test a feature in a deployed environment before merging to develop:

1) Push your feature work:
```bash
git push origin `version-tagging`
```

2) Open a pull request (if you don't intend on merging, place the "DO NOT MERGE" label on the PR).

3) Visit the [PCF DEVUSH admin interface](https://apps.devsysush.inbcu.com/) to ensure there are no other feature instances under the **Software-Eng-CC-dev** space, as we have a quota on the total number of instances we're allowed.

4) Comment `deploy now` on your pull request to trigger a deployment.

5) Visit the [PCF DEVUSH admin interface](https://apps.devsysush.inbcu.com/) again and navigate to the **Software-Eng-CC-dev** space to see your new app/route! This route will be based on your branch name, and will take everything after the last trailing `/`, so for instance the branch:

```
chore/this-is-a-chore
```
should deploy to:
```
this-is-a-chore.devush.inbcu.com
```
