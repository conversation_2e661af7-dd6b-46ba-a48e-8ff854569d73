# frozen_string_literal: true

module Api
  # DealsController
  class DealsController < ApplicationController
    include Publishable

    before_action :deals, only: :publish

    def index
      render json: servicer(index_params).index.page(params[:page] || 1).per(params[:per_page] || 50)
    end

    def budget_totals
      render json: servicer(index_params).budget_totals
    end

    def status_counts
      render json: servicer(index_params).status_counts
    end

    def show
      render json: servicer(show_params).show
    end

    def create
      @deal = Deal.create!(create_params)

      render json: @deal, status: :created, include: [budgets: { include: [:stealth_mode_budget] }]
    end

    # Uses Pam Deal create logic
    def batch_create
      @deals =
        ActiveRecord::Base.transaction do
          assigned_parent_deal = create_parent_deal(batch_parent_deal_create_params[:deals].first)
          batch_create_params[:deals].map do |deal_params|
            deal_params[:sfdc_intg_vertical_id] = -1 if deal_params[:sfdc_intg_vertical_id].blank?
            deal = Deal.create!(deal_params)
            deal.update!(parent_deal: assigned_parent_deal) if assigned_parent_deal
          end
        end

      render json: @deals, status: :created
    end

    # Uses AgNewDealCreator logic
    def batch_new
      @new_deals = []
      batch_new_params[:deals].each do |deal|
        andc = AgNewDealCreator.new(deal)
        @new_deals << andc.create
      end
      render json: @new_deals
    end

    def publish
      deal_changed = DealChanged.new(@deals, publish_params[:attributes].to_h)
      Rails.logger.info("SENDING DEAL UPDATES TO AG for deals: #{deal_changed.ag_deals}")
      Pubsub::Publisher.deal_headers(deal_changed.ag_deals.to_json)
      Rails.logger.info("SENDING DEAL DELETES TO AG for deals: #{deal_changed.marked_for_deletion}")
      Pubsub::Publisher.deal_delete(deal_changed.marked_for_deletion.to_json)

      head :ok
    end

    def destroy
      Deal.find(params.require(:id)).destroy!
      head :ok
    rescue ActiveRecord::RecordNotDestroyed => e
      raise Error::ApiError::ConflictError, e.message
    end

    def update
      deal.update!(update_params)

      render json: deal
    end

    def batch_update
      @updated_deals = []
      batch_update_params[:deals].each do |deal_params|
        u_deal = Deal.find(deal_params[:deal_id])
        if u_deal
          u_deal.update!(deal_params)
          @updated_deals << u_deal
        end
      end

      render json: @updated_deals
    end

    def linked_deals
      linked_deal_ids = Deal.linked_deals(params.require(:deal_id)).select(:deal_id)

      render json: servicer(
        deal_id: linked_deal_ids,
        budget_year_id: params.require(:budget_year_id),
        marketplace_id: params.require(:marketplace_id)
      ).deal_link_info
    end

    private

    def servicer(filters = {})
      ApiDealService.new(@current_user, filters)
    end

    def deals
      @deals ||= begin
        deal_ids = publish_params[:deal_ids]
        # split ids into groups of 1000
        slices = deal_ids&.each_slice(1000).to_a
        # use first slice to create the base `where` clause
        base = Deal.where(deal_id: slices.shift)
        # add an `or` cluase for each additional slice
        slices.reduce(base) { |acc, slice| acc.or(Deal.where(deal_id: slice)) }
      end
    end

    def deal
      @deal ||= Deal.find(params.require(:id))
    end

    def show_params
      params.require(:id)
      params.permit(:id, :budget_year_id)
    end

    def index_params
      params.permit(:budget_year_id, :marketplace_id, :property_id, :agency_id,
                    :advertiser_id, :status_id, :deal_tag_id, :vertical_id,
                    :partnership_id, :sales_type_id, :app_user_id, :deal_type,
                    :sort_opt, :deal_id, :content_id, :registration_type_id,
                    :measurement_type_id, :selling_vertical_id, :send_to_salesforce,
                    :page, :per_page, :pillar_id, :placeholder, :planner_id,
                    :currency_id, :sfdc_intg_vertical_id, :parent_deal_id,
                    :parent_deal_type_id, :above_the_line_id, :advertiser_brand_id,
                    :account_manager_id)
    end

    def create_params
      params.permit(permitted_create_params)
    end

    def batch_create_params
      params.require(:deals)
      params.permit(deals: permitted_create_params)
    end

    def batch_parent_deal_create_params
      params.require(:deals)
      params.permit(deals: %i[
                      agency_id
                      advertiser_id
                      marketplace_id
                      parent_deal_id
                      parent_deal_name
                      parent_deal_type_id
                    ])
    end

    def permitted_create_params
      permitted_params_for(Deal) +
        [budgets_attributes: budget_permitted_params +
                             [stealth_mode_budget_attributes: stealth_mode_budget_permitted_params]]
    end

    def batch_new_params
      params
        .permit(
          deals: [
            :object_id,
            :budget_year,
            :agency_id,
            :advertiser_id,
            :demographic_id,
            :marketplace_id,
            :placeholder,
            :rating_stream_id,
            :registration_type_id,
            :measurement_type_id,
            :planner_id,
            :currency_id,
            :sfdc_intg_vertical_id,
            :parent_deal_id,
            properties: %i[property_id deal_name]
          ]
        )
    end

    def update_params
      params.permit(permitted_params_for(Deal) +
        [budgets_attributes: budget_permitted_params.push(:id)])
    end

    def batch_update_params
      params.permit(deals: permitted_params_for(Deal) +
        [:deal_id, budgets_attributes: budget_permitted_params.push(:id)])
    end

    def publish_params
      params
        .permit(
          deal_ids: [],
          attributes: publish_attributes
        )
    end

    def budget_permitted_params
      permitted_params_for(Budget) + Budget::CALCULATED_ATTRIBUTES
    end

    def stealth_mode_budget_permitted_params
      permitted_params_for(StealthModeBudget)
    end

    def publish_attributes
      [
        DealChanged::DEAL_DETAIL_PARAMS,
        DealChanged::ADDITIONAL_DEAL_DETAIL_PARAMS,
        DealChanged::DEAL_CATEGORY_PARAMS
      ].flatten
    end

    # no need for entitlement check since deals have already been updated on PAM side
    def verify_access
      true
    end

    def create_parent_deal(parent_deal_params)
      return unless parent_deal_params[:parent_deal_id].to_s == '-1'

      ParentDeal.create(name: parent_deal_params[:parent_deal_name],
                        parent_deal_type_id: parent_deal_params[:parent_deal_type_id],
                        advertiser_id: parent_deal_params[:advertiser_id],
                        agency_id: parent_deal_params[:agency_id],
                        marketplace_id: parent_deal_params[:marketplace_id])
    end
  end
end
