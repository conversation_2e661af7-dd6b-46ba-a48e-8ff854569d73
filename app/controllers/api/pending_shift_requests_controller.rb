# frozen_string_literal: true

module Api
  class PendingShiftRequestsController < ApplicationController
    PERMITTED_CHANGE_STATUSES = %w[approved rejected].freeze

    def index
      render json: servicer.show
    end

    def update
      @shift_request = shift_request
      unless pending?
        raise Error::ApiError::UnprocessableEntityError,
              'Only Pending AE Approval requests are supported at the moment.'
      end

      case update_params[:status]
      when 'approved' then servicer.approve
      when 'rejected' then servicer.reject
      end

      render json: { message: 'Shift request status updated successfully' }, status: :ok
    end

    private

    def update_params
      raise Error::ApiError::BadRequestError, "Missing the required 'status' parameter." unless params[:status]

      unless PERMITTED_CHANGE_STATUSES.include?(params[:status])
        raise Error::ApiError::BadRequestError,
              invalid_status_error_message
      end

      case params[:status]
      when 'approved' then strong_require_and_permit(:id, :status)
      when 'rejected' then strong_require_and_permit(:id, :status, :comment)
      end
    end

    def pending?
      @shift_request.shift_request_status == ShiftRequestStatus.pending_ae_approval
    end

    def shift_request
      ShiftRequest.find(params[:id])
    end

    def servicer
      opts = default_request_method == 'PUT' ? { shift_request: @shift_request, comment: update_params[:comment] } : {}
      PendingShiftRequestService.new(@current_user, opts)
    end

    def invalid_status_error_message
      "'#{params[:status]}' status change is not allowed. Try #{PERMITTED_CHANGE_STATUSES.map do |s|
                                                                  "'#{s}'"
                                                                end.join(' or ')}."
    end
  end
end
