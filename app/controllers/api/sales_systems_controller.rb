# frozen_string_literal: true

module Api
  class SalesSystemsController < ApplicationController
    def index
      render json: formatted_index
    end

    private

    def formatted_index
      sales_systems.map do |sales_system|
        {
          key: 'sales_system_id',
          name: sales_system.deal_name,
          selected: false,
          type: 'SalesSystem',
          value: sales_system.sales_system_id
        }
      end
    end

    def sales_systems
      ExternalDeal
        .where(index_params)
        .select(:sales_system_id, 'max(deal_name) as deal_name')
        .group(:sales_system_id, :budget_year_id)
    end

    def index_params
      params.permit(:advertiser_id, :property_id, :marketplace_id, :quarter_id, :budget_year_id)
    end
  end
end
