# frozen_string_literal: true

module Api
  module StealthMode
    class BudgetsController < ApplicationController
      OPTIMIZED_LINEAR_PROPERTY_NAMES = ['Optimized Linear - Advanced', 'Optimized Linear - Demo'].freeze

      # POST /api/stealth_mode/budgets/batch_update?agency_id=:ID&budget_year_id=:ID&marketplace_id=:ID
      # Update both stealth budgets and budgets
      def batch_update
        budgets_params[:budgets].each do |budget|
          Api::StealthMode::StealthModeBudgetService
            .new(budget:,
                 stealth_enabled: stealth_mode_enabled,
                 update_by_stealth_status:)
            .update
        end

        head :ok
      end

      # POST /api/stealth_mode/budgets/sync_to_salesforce?budget_ids=[:ID]
      # set a timestamp on sf_sync_at to specify that the budget should be synced to salesforce,
      # also change sf_sync_status to 'pending'
      def flag_sync_to_salesforce
        if params[:budget_ids]
          budget_ids = params[:budget_ids].split(',')
          Budget.flag_sync_to_salesforce(budget_ids)
        end

        head :ok
      end

      # POST /api/stealth_mode/budgets/flag_send_to_salesforce?budget_ids=[:ID]
      # Flags provided budgets as sendable to salesforce if they have cy dollars
      # Optimized Linear should always be sent quarterly (PAM-763)
      def flag_send_to_salesforce
        if params[:budget_ids]
          budget_ids = params[:budget_ids].split(',')
          budget_ids_with_links = Budget.linked_sendable_to_salesforce_budgets(budget_ids).pluck(:budget_id)
          all_budget_ids = (budget_ids + budget_ids_with_links).uniq

          # Process each budget individually
          all_budget_ids.each do |budget_id|
            budget = Budget.find(budget_id)

            should_send_quarterly = params[:send_quarterly] == 'true'
            send_zero_dollars = params[:send_zero_dollars] == 'true'
            optimized_linear_property = OPTIMIZED_LINEAR_PROPERTY_NAMES.include?(budget.deal.property.property_name)

            # send_quarterly is not compatible with send_zero_dollars, because nothing to split.
            if optimized_linear_property
              should_send_quarterly = true
              send_zero_dollars = false
            end

            # Flag this individual budget
            Budget.flag_sendable_to_salesforce([budget_id],
                                               should_send_quarterly,
                                               params[:send_prior_year],
                                               send_zero_dollars)
          end
        end
        head :ok
      end

      def flag_update_in_salesforce
        if params[:budget_ids]
          budget_ids = params[:budget_ids].split(',')
          budget_ids_with_links = Budget.linked_sendable_to_salesforce_budgets(budget_ids).pluck(:budget_id)
          Budget.where(budget_id: budget_ids + budget_ids_with_links)
                .update_all(updated_at: DateTime.current.strftime('%Y-%m-%d %H:%M:%S'))
          # TODO: We need to determine how we will flag updates
          # Budget.find(budget_ids + budget_ids_with_links).update(sf_update_opportunity_at: Time.zone.now)
        end

        head :ok
      end

      private

      def agency
        @agency ||= BaseAgency.find(budget_params[:agency_id])
      end

      def budget_year
        @budget_year ||= BudgetYear.find(budget_params[:budget_year_id])
      end

      def stealth_mode_enabled
        @stealth_mode_enabled ||=
          agency
          .self_and_ancestors
          .order(Arel.sql("decode(agency_type, 'Agency',1, 'ParentAgency',2, 'GrandParentAgency',3)"))
          .find do |agency|
            stealth_status = AgencyMarketplaceYear.find_by(marketplace_id: budget_params[:marketplace_id],
                                                           budget_year:,
                                                           agency:)&.stealth_enabled?
            break stealth_status unless stealth_status.nil?
          end || false
      end

      def update_by_stealth_status
        params[:update_when_stealth].present? && params[:update_when_stealth] == 'true'
      end

      def budget_params
        params.permit(permitted_params_for(Budget))
      end

      def budgets_params
        params.permit(
          budgets:
            Api::StealthMode::StealthModeBudgetService::REQUIRED_KEYS +
            Api::StealthMode::StealthModeBudgetService::DEAL_KEYS
        )
      end
    end
  end
end
