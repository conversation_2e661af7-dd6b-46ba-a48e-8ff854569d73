# frozen_string_literal: true

module Api
  module StealthMode
    class AgenciesController < ApplicationController
      include Publishable

      before_action :verify_access, except: :publish

      # POST /api/stealth_mode/agencies/:agency_id/toggle_stealth_mode?marketplace_id=:id&budget_year_id=:id
      def toggle_stealth_mode
        agency.toggle_stealth(params.require(:marketplace_id), budget_year)

        head :ok
      rescue PamClient::Concerns::AgencyStealthConcern::StealthAgencyTypeError => e
        raise Error::ApiError::UnauthorizedError, e.message
      end

      # POST /api/stealth_mode/agencies/:agency_id/toggle_incognito_mode?marketplace_id=:id&budget_year_id=:id
      def toggle_incognito_mode
        agency_marketplace_year.toggle_incognito

        head :ok
      end

      # POST /api/stealth_mode/agencies/:agency_id/send_data_to_ag?marketplace_id=:marketplace_id
      def send_data_to_ag
        Pubsub::Publisher.deal(agency_gateway_data.to_json)
        Pubsub::Publisher.comments(agency_gateway_comments)
        update_status_for_submitted_comments

        head :ok
      end

      def publish
        Pubsub::GenericPublisher.Publish(:agencies, BaseAgency.all)

        head :ok
      end

      # POST /api/stealth_mode/agencies/:agency_id/upload?marketplace_id=:marketplace_id&budget_year_id=:budget_year_id
      def upload
        upload = Upload::Parsers::Csv.new(params[:file], headers: true).parse
        data = StealthUpload::FileUpload.new(input: upload,
                                             parent_agency_id: params[:agency_id],
                                             marketplace_id: params[:marketplace_id],
                                             budget_year_id: params[:budget_year_id],
                                             current_user: @current_user)
        data.perform if data.valid?
        render json: data.result
      rescue InvalidFileTypeError
        head :bad_request
      rescue StandardError => e
        Rails.logger.error e.backtrace[0..15].join("\n\t")
        Rails.logger.error "#{e.class}: #{e.message}"
        head :unprocessable_entity
      end

      private

      def agency_gateway_data
        @agency_gateway_data ||=
          AgencyGatewayView.data(parent_agency_id: params[:agency_id],
                                 marketplace_id: if params[:marketplace_id].blank?
                                                   Marketplace.current_default.id
                                                 else
                                                   params[:marketplace_id]
                                                 end)
      end

      def agency_gateway_comments
        AgencyDealComment.comments_json(budget_ids)
      end

      def budget_ids
        @budget_ids ||= agency_gateway_data.map(&:current_budget_id)
      end

      def update_status_for_submitted_comments
        AgencyDealComment.mark_submitted_for_ag(budget_ids)
      end

      def verify_access
        head :unauthorized unless stealth_entitlement? && any_user_portal_agencies?
      end

      def stealth_entitlement?
        @current_user.has_entitlement?(Entitlement.find_by_key(:portal_mgmt_stealth_mode))
      end

      def any_user_portal_agencies?
        agency_ids = agency.agency_id
        parent_agency_id = agency.parent_agency_id
        agency_ids << parent_agency_id if parent_agency_id != -1

        @current_user.user_portal_agencies.where(agency_id: agency_ids).any?
      end

      def agency
        @agency ||= BaseAgency.find(params[:agency_id])
      end

      def budget_year
        @budget_year ||= BudgetYear.find(params.require(:budget_year_id))
      end

      def agency_marketplace_year
        @agency_marketplace_year ||=
          AgencyMarketplaceYear.find_by(agency:,
                                        marketplace_id: params[:marketplace_id],
                                        budget_year:)
      end
    end
  end
end
