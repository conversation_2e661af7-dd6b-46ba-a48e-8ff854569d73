# frozen_string_literal: true

module Api
  class DealLinksController < ApplicationController
    # associated={true|false} flag is to toggle between
    # when you want to get list of deal links associated with a deal
    # vs when you want to get a list of suggested deal_links for a deal

    # 1. api/deal_links?deal_id=:id&associated=true (default value)
    # => to get list of deal_links associated to a deal

    # 2. api/deal_links?associated=false&deal_id=:id&agency_id=:id&advertiser_id=:id
    # => associated=false has three options:
    #   a) pass `deal_id`: exclude links that are already associated to that deal, using agency and
    #     advertiser from deal (optionally overwritten-able)
    #   b) pass `agency_id` and `advertiser_id`: filter by specified agency, advertiser and
    #     optionally by demographic, bae/cae, rating stream and marketplace. optional filters
    #     are used to filter deal links for link types that are sendable to salesforce
    def index
      data = DealLink

      data = if boolean_eval(params[:associated] || true) # 1
               data
                 .joins(:deal_link_associations)
                 .where('deal_link_asc.deal_id = ?', params.require(:deal_id))
                 .select('deal_link.*, deal_link_asc.deal_link_asc_id')
             elsif deal_id
               data.where(advertiser_id:,
                          agency_id:,
                          deal_link_type: DealLinkType.where(send_to_salesforce: false))
                   .or(salesforce_links) # union deal links sendable to salesforce
                   .where.not('deal_link.deal_link_id in (?)', associated_deal_link_ids) # 2a
             else # 2b
               data.where(advertiser_id: params.require(:advertiser_id),
                          agency_id: params.require(:agency_id),
                          deal_link_type: DealLinkType.where(send_to_salesforce: false))
                   .or(salesforce_links)
                   .joins(:deal_link_type)
                   .select('deal_link.*, deal_link_type.send_to_salesforce') # union deal links sendable to salesforce
             end

      render json: data.order(:display_order)
    end

    def create
      deal_link = DealLink.create!(create_params)

      render json: deal_link, status: :created
    end

    def batch_create
      @deal_links =
        ActiveRecord::Base.transaction do
          batch_create_params[:deal_links].map do |deal_link_params|
            DealLink.create!(deal_link_params)
          end
        end

      render json: @deal_links, status: :created
    end

    def update
      deal_link.update!(params.permit(:deal_link_name))
      render json: deal_link
    end

    def batch_update
      ActiveRecord::Base.transaction do
        params.require(:deal_links).each do |link|
          DealLink.find(link.require(:id))
                  .update!(link.permit(:deal_link_name))
        end
      end

      render json: { message: 'Deal links successfully updated.' }, status: :ok
    end

    private

    def associated_deal_link_ids
      return unless deal_id

      DealLinkAssociation.where(deal_id:).select(:deal_link_id)
    end

    def advertiser_id
      params[:advertiser_id] || deal.advertiser_id
    end

    def agency_id
      params[:agency_id] || deal.agency_id
    end

    def deal
      @deal ||= Deal.find(deal_id)
    end

    def deal_id
      @deal_id ||= params[:deal_id]
    end

    def deal_link
      @deal_link ||= DealLink.find(params.require(:id))
    end

    def constructive_attributes
      %w[advertiser_id agency_id deal_link_name deal_link_type_id demographic_id app_user_id cae_app_user_id
         rating_stream_id marketplace_id]
    end

    def permitted_create_params
      permitted_params_for(DealLink)
    end

    def create_params
      params.require(constructive_attributes)
      params.permit(permitted_create_params)
    end

    def batch_create_params
      params.require(:deal_links)
      params.permit(deal_links: permitted_create_params)
    end

    def salesforce_links
      DealLink.unscoped.where(advertiser_id: params[:advertiser_id],
                              agency_id: params[:agency_id],
                              demographic_id: params[:demographic_id],
                              app_user_id: params[:app_user_id],
                              cae_app_user_id: params[:cae_app_user_id],
                              rating_stream_id: params[:rating_stream_id],
                              marketplace_id: params[:marketplace_id],
                              sfdc_intg_vertical_id: SfdcIntgVertical
                                .with_associated_salesforce_properties(
                                  params[:sfdc_intg_vertical_id]
                                ),
                              deal_link_type: DealLinkType.where(send_to_salesforce: true))
    end
  end
end
