# frozen_string_literal: true

module Api
  # ParentDealsController
  class ParentDealsController < ApplicationController
    def index
      render json: servicer(index_params).index
    end

    def create
      parent_deal = ParentDeal.create!(create_params)

      render json: parent_deal, status: :created
    end

    def update
      parent_deal = ParentDeal.find(params.require(:id)).update!(update_params)

      render json: parent_deal
    end

    def move
      MoveDealService.new(move_params[:deal_ids], move_params[:destination_parent_deal_id]).move

      head :ok
    end

    def index_params
      params.permit(:budget_year_id, :marketplace_id, :property_id, :agency_id,
                    :advertiser_id, :parent_deal_id, :parent_deal_type_id,
                    :page, :per_page)
    end

    private

    def servicer(filters = {})
      ApiParentDealService.new(@current_user, filters)
    end

    def create_params
      params.permit(permitted_params_for(ParentDeal) + [deal_ids: []])
    end

    def move_params
      params.permit(:destination_parent_deal_id, :move_linked, deal_ids: [])
    end

    def update_params
      params.permit(permitted_params_for(ParentDeal))
    end
  end
end
