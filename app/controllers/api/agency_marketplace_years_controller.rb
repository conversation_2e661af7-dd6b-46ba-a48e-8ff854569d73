# frozen_string_literal: true

module Api
  class AgencyMarketplaceYearsController < ApplicationController
    def create
      render json: AgencyMarketplaceYear.create!(create_params), status: :created
    end

    def update
      agency_marketplace_year.update!(update_params)
      render json: agency_marketplace_year
    end

    def index
      render json: AgencyMarketplaceYear.where(index_params)
    end

    private

    def agency_marketplace_year
      @agency_marketplace_year ||= AgencyMarketplaceYear.find(params.require(:id))
    end

    def create_params
      params.require(constructive_attributes)
      params.permit(permitted_params_for(AgencyMarketplaceYear))
    end

    def update_params
      params.permit(permitted_params_for(AgencyMarketplaceYear) - constructive_attributes)
    end

    def index_params
      create_params
    end

    # attributes required to construct an agency_marketplace_year
    def constructive_attributes
      %w[agency_id marketplace_id budget_year_id]
    end
  end
end
