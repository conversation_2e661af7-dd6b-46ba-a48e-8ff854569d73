# frozen_string_literal: true

# JSON Endpoing for fetching dropdown_values
module Api
  class DropdownValuesController < ApplicationController
    def index
      render json: DropdownResource.new(index_params).dropdown_values
    rescue DropdownResource::InvalidTypeError => e
      render json: { error: e.class.name.demodulize, message: e.message }, status: :bad_request
    end

    private

    def index_params
      params.except(:smuser).permit!
    end
  end
end
