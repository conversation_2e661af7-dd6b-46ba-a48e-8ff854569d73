# frozen_string_literal: true

module Api
  class DealShiftsController < ApplicationController
    include DealShiftMailerHelper

    skip_before_action :authenticate, except: :create
    before_action :set_current_user_by_guid, except: :create

    rescue_from DealShift::ActionError do |error|
      render json: { error: error.class.to_s, message: error.message, data: deal_shift }, status: :forbidden
    end

    rescue_from DealShift::MissingPortalsError do |error|
      mailer_service.notify_missing_portals(error_message: error.message)
      render json: { error: error.class.to_s, message: error.message, data: @deal_shift }, status: :unprocessable_entity
    end

    def create
      new_deal_shift.check_portals if new_deal_shift.respond_to?(:check_portals)
      @deal_shift.save!
      notify_deal_shift_created unless deal_shift.parent

      render 'deal_shifts/show', formats: :json, status: :created
    end

    def show
      deal_shift
      render 'deal_shifts/show', formats: :json
    end

    def approve
      deal_shift.approve!
      # Only send updates to AG for agency or marketplace changes
      if deal_shift.is_a?(AgencyDealShift) || deal_shift.is_a?(MarketplaceDealShift)
        Pubsub::Publisher.deal_headers(deal_shift.deal_changed.ag_deals.to_json)
      end
      notify_deal_shift_approved
      notify_children_shift_created if deal_shift.children

      render 'deal_shifts/show', formats: :json
    end

    def reject
      deal_shift.reject!
      notify_deal_shift_rejected

      render 'deal_shifts/show', formats: :json
    end

    private

    def new_deal_shift
      @deal_shift = deal_shift_class.new(
        deal_id: params.require(:deal_id),
        shift_to: params.require(:shift_to),
        budget_year_id: params.require(:budget_year_id),
        deal_shift_option_id: params[:deal_shift_option_id] || DealShiftOption.single_deal.id,
        requested_by_id: @current_user.id
      )

      allowed_nested_params.each_key do |param|
        next unless params[param]

        @deal_shift.send(:"#{param}=", params.permit(allowed_nested_params).to_h[param])
      end
      @deal_shift
    end

    def deal_shift
      @deal_shift ||= deal_shift_guid.deal_shift
    end

    def set_current_user_by_guid
      PamClient::User.current = deal_shift_guid.user
    end

    def deal_shift_guid
      @deal_shift_guid ||= begin
        unless (guid = DealShiftGuid.find_by(guid: params[:deal_shift_guid] || params[:guid]))
          raise Error::ApiError::BadRequestError, 'Invalid deal_shift_guid'
        end

        guid
      end
    end

    def allowed_nested_params
      HashWithIndifferentAccess.new(
        deal_shift_comments_attributes: %i[message app_user_id],
        children_attributes: %i[deal_id budget_year_id shift_to shift_type]
      )
    end

    def deal_shift_class
      params.require(:shift_type).constantize
    end
  end
end
