# frozen_string_literal: true

module Api
  class OpportunitiesController < ApplicationController
    # GET api/opportunities
    # Returns JSON array of Opportunities where count as projected finish is active.
    #
    def index
      @my_opportunities = OpportunityPacingDetailView
                          .linear
                          .for_buying_ae(@current_user)
                          .count_as_projected_finish
      render json: @my_opportunities
    end

    # POST api/opportunities
    # Creates a new opportunity
    #
    def create
      pacing_budget_detail = PacingBudgetDetail.find(create_params[:pacing_budget_detail_id])
      quarter_id = pacing_budget_detail&.quarter_id
      pacing_budget_id = pacing_budget_detail&.pacing_budget_id

      create_hash = {
        user: @current_user,
        opportunity_name: create_params[:opportunity_name],
        marketplace_id: create_params[:marketplace_id],
        opportunity_status_id: create_params[:opportunity_status_id],
        sales_system_id: create_params[:sales_system_id],
        demographic_id: create_params[:demographic_id],
        pacing_budget_id:,
        not_returning_reason_id: create_params[:not_returning_reason_id],
        not_returning_custom_reason: create_params[:not_returning_custom_reason],
        opportunity_comment: create_params[:opportunity_comment]
      }

      details_create_hash = {
        quarter_id:,
        opportunity_dollars: create_params[:opportunity_dollars],
        flight_start: create_params[:flight_start],
        flight_end: create_params[:flight_end]
      }

      ActiveRecord::Base.transaction do
        opportunity = Opportunity.create!(create_hash.compact)
        OpportunityDetail.create!(details_create_hash.merge(opportunity_id: opportunity.id))
      end

      render json: { message: 'Opportunity successfully created.' }, status: :created
    end

    # PUT api/opportunities/:id
    # Updates an existing opportunity
    #
    def update
      @opportunity = Opportunity.find(params[:id])
      @opportunity_detail = @opportunity.opportunity_details.find(update_params[:opportunity_detail_id])

      update_hash = {
        opportunity_status_id: update_params[:opportunity_status_id],
        sales_system_id: update_params[:sales_system_id],
        not_returning_reason_id: update_params[:not_returning_reason_id],
        not_returning_custom_reason: update_params[:not_returning_custom_reason]
      }

      ActiveRecord::Base.transaction do
        @opportunity.update!(update_hash.compact)
        @opportunity_detail&.update!({ opportunity_dollars: update_params[:opportunity_dollars] }.compact)
      end

      head :ok
    end

    private

    UPDATE_PARAMS = %i[
      opportunity_detail_id
      opportunity_status_id
      opportunity_dollars
      sales_system_id
      not_returning_reason_id
      not_returning_custom_reason
    ].freeze

    CREATE_PARAMS = %i[
      opportunity_name
      opportunity_dollars
      marketplace_id
      pacing_budget_detail_id
      opportunity_status_id
      sales_system_id
      not_returning_reason_id
      not_returning_custom_reason
      demographic_id
      opportunity_comment
      flight_start
      flight_end
    ].freeze

    CREATE_PARAMS_REQUIRED = %i[
      opportunity_name
      opportunity_dollars
      marketplace_id
      pacing_budget_detail_id
      opportunity_status_id
    ].freeze

    def update_params
      params.require(:opportunity_detail_id)
      params.permit(UPDATE_PARAMS)
    end

    def create_params
      params.require(CREATE_PARAMS_REQUIRED)
      params.permit(CREATE_PARAMS)
    end
  end
end
