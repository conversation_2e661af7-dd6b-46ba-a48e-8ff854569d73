# frozen_string_literal: true

module Api
  class FinanceModelsController < ApplicationController
    def index
      render json: index_data
    end

    private

    def index_data
      ds = FinanceModel
      if (name = index_params[:name] || index_params[:finance_model_name])
        ds = ds.where(finance_model_name: parse_array_params(name))
      end
      ds.order(:display_order)
    end

    def index_params
      params.permit(:name, :finance_model_name)
    end
  end
end
