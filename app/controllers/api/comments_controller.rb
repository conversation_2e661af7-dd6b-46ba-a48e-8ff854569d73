# frozen_string_literal: true

module Api
  class CommentsController < ApplicationController
    def index
      render json: comments
    end

    def update
      comment.update!(
        message: params.require(:message),
        app_user_id: @current_user.id
      )
      render json: comment
    end

    def batch_update
      ActiveRecord::Base.transaction do
        params.require(:comments).each do |comment|
          Comment.find(comment.require(:id))
                 .update!(comment.permit(updatable_attributes))
        end
      end

      render json: { message: 'Comments successfully updated.' }, status: :ok
    end

    def create
      comment = model.create!(create_params.merge(app_user_id: @current_user.id, comment_type_id:))
      render json: comment, status: :created
    end

    def destroy
      comment.destroy!
      head :ok
    end

    private

    def comments
      dataset = model.joins('INNER JOIN app_user ON app_user.app_user_id = commentary.app_user_id')
                     .left_joins(:comment_type, :quarter)
                     .where(comment_params)
                     .select(:commentary_id, :comment_type_name, :source_id, :app_user_id,
                             :comment_type_id, :budget_year_id, :unread,
                             :message, :first_name, :last_name, :quarter_id,
                             'quarter.quarter_name', 'quarter.quarter as quarter_value',
                             :created_at, :updated_at)
                     .order('commentary.created_at')

      dataset = dataset.where(comment_type_id:) if params[:comment_type_id] || params[:comment_type]
      dataset
    end

    def comment
      @comment ||= Comment.find(params.require(:id))
    end

    def comment_type_id
      params[:comment_type_id] || comment_type&.id
    end

    def comment_type
      return unless params[:comment_type]

      CommentType.find_by_name(params[:comment_type].titleize)
    end

    def source_id_alias
      "#{source_model}_id"
    end

    def source_model
      model.reflections.find { |_k, v| v.options[:foreign_key] == 'source_id' }[0]
    end

    def model
      "#{type}_comment".classify.constantize
    end

    def type
      params.require(:commentary_type)
    end

    def create_params
      params.require([source_id_alias, :message])
      comment_params
    end

    def updatable_attributes
      permitted_params_for(Comment) - ['commentary_type']
    end

    def comment_params
      params.permit(permitted_params_for(Comment) + [source_id_alias] - ['commentary_type'])
    end
  end
end
