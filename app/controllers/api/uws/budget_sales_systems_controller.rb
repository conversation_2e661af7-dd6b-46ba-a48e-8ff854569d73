# frozen_string_literal: true

module Api
  module Uws
    class BudgetSalesSystemsController < ApplicationController
      skip_before_action :authenticate, :set_current_user

      def digital
        ::Api::Salesforce::SalesSystemRetrieverService.new.perform

        head :ok
      end

      def linear
        ::Api::Uws::SalesSystemRetrieverService.new.perform

        head :ok
      end

      def linear_health_check
        conn = Athens::Connection.new(database: ENV['UWS_DATABASE_NAME'])
        query = conn.execute("show tables in #{ENV['UWS_DATABASE_NAME']}", work_group: ENV['UWS_WORKGROUP'])
        query.wait

        render plain: "Query State: #{query.state}, Reason: #{query.state_reason},
                       Query Execution ID: #{query.query_execution_id},
                       Query Results: #{query.to_h}"
      rescue StandardError => e
        render plain: "Error executing query #{query&.query_execution_id}: #{e.message}"
      end
    end
  end
end
