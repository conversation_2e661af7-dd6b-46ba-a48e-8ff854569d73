# frozen_string_literal: true

module Api
  class ImagesController < ApplicationController
    skip_before_action :authenticate, :set_current_user, only: %w[show]

    def show
      respond_to do |format|
        format.jpg do
          filename = params.require(:name)
          obj = bucket_obj(filename)

          begin
            image = obj.get.body.read
          rescue Aws::S3::Errors::NoSuchKey
            obj = bucket_obj(default_image_file_name)
            begin
              image = obj.get.body.read
            rescue Aws::S3::Errors::NoSuch<PERSON>ey
              raise Error::ApiError::BadRequestError,
                    "No image found in bucket with name #{filename} or #{default_image_file_name}"
            end
          end

          render plain: image
        end
      end
    end

    private

    def bucket_obj(name)
      s3_bucket.object(name)
    end

    def default_image_file_name
      'default_logo.jpg'
    end
  end
end
