# frozen_string_literal: true

module Api
  class CalendarYearsController < ApplicationController
    def index
      @calendar_years = index_data
      render json: @calendar_years
    end

    private

    def index_params
      params.permit(:count, :show_in_finance, :show_in_pacing)
    end

    def index_data
      params = index_params

      ds = CalendarYear
           .select(:calendar_year_id, :calendar_year, :default_calendar_year)
           .order(:calendar_year)

      if params[:count]
        current_year = CalendarYear.current_calendar_year.calendar_year
        ds = ds.where(calendar_year: (current_year - index_params[:count].to_i + 1)..current_year)
      end

      ds = ds.where(show_in_finance: true) if boolean_eval(params[:show_in_finance])
      ds = ds.where(show_in_pacing: true) if boolean_eval(params[:show_in_pacing])

      ds
    end
  end
end
