# frozen_string_literal: true

module Api
  class PatSponsorshipsController < ApplicationController
    def index
      pat_sponsorships = PatSponsorship
                         .joins(:budget)
                         .where('budget.budget_year_id': BudgetYear.current_year.id)
                         .select(:pat_sponsorship_id, :pitch_id, :pitch_name, :budget_id, 'budget.deal_id')
                         .order(pat_sponsorship_id: :desc)

      pat_sponsorships = pat_sponsorships.where('budget.deal_id': index_params[:deal_id]) if index_params[:deal_id]
      pat_sponsorships = pat_sponsorships.where(budget_id: index_params[:budget_id]) if index_params[:budget_id]

      render json: pat_sponsorships
    end

    def create
      PatSponsorship.create!(create_params)
      render json: { message: 'Pat Sponsorship successfully created.' }, status: :created
    end

    def destroy
      PatSponsorship.destroy(params[:id])
      render json: { message: 'PatSponsorship successfully deleted.' }, status: :ok
    end

    private

    def index_params
      params.permit(:deal_id, :budget_id)
    end

    def create_params
      strong_require_and_permit(:budget_id, :pitch_id, :pitch_name)
    end
  end
end
