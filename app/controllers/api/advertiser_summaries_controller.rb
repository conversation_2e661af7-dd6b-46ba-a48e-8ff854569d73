# frozen_string_literal: true

module Api
  # Controller for Advertiser Summaries
  class AdvertiserSummariesController < ApplicationController
    include AdvertiserSummariesControllerHelper

    def index
      render json: index_data
    end

    def save
      advertiser_summary
      if valid_summary_attributes?
        update_projections
        head :ok
      else
        head :unprocessable_entity
      end
    end

    def info
      render json: %i[client_partnership_category client_partnership_svp target_classification].each_with_object(
        default_category_id: advertiser.default_category_id,
        default_category_name: advertiser.default_category.name
      ) { |type, memo|
                     memo[type] = advertiser.classification_for(AdvertiserClassificationType.send(type))&.name
                   }
    end

    def quintiles
      render json: PortfolioQuintileMview.left_joins(:base_agency, :marketplace)
                                         .where(advertiser_id: advertiser.id,
                                                budget_year_id: budget_year.prior_budget_year_id,
                                                marketplace_id: default_marketplace_ids)
                                         .select('portfolio_quintile_mview.*, agency_name, marketplace_name')
    end

    # this shared method is to service both selling_verticals & pillars tabs on adv_summary
    # since these tabs are required to show all properties under each selling_vertical/pillar,
    # the data is actually grouped by property and not selling_vertical/pillar
    def selling_verticals
      render json: advertiser.summaries_by(:property, filter_params)
    end

    alias pillars selling_verticals

    def property_types
      render json: advertiser.summaries_by(:property_type, filter_params)
    end

    def quintile_chart
      render json: quintile_chart_data
    end

    def marketplaces
      render json: marketplaces_data
    end

    private

    def advertiser_summary
      @advertiser_summary =
        AdvertiserSummary
        .where(summary_attributes)
        .first_or_create
    end

    def update_projections
      @advertiser_summary
        .update(client_partnership_projection:
        summary_params[:client_partnership_projection])
    end

    def summary_attributes
      {
        advertiser:,
        budget_year:
      }
    end

    def advertiser
      @advertiser ||= Advertiser.find(params.require(:advertiser_id))
    end

    def budget_year
      summary_params[:budget_year_id] ? BudgetYear.find(summary_params[:budget_year_id]) : BudgetYear.current_year
    end

    def valid_summary_attributes?
      advertiser.present? && budget_year.present?
    end

    def filter_params
      {
        budget_year_id: budget_year.id,
        selling_vertical_id: parse_array_params(params[:selling_vertical_id]),
        pillar_id: parse_array_params(params[:pillar_id])
      }
    end

    def summary_params
      params.permit(:advertiser_id, :budget_year_id, :client_partnership_projection)
    end
  end
end
