# frozen_string_literal: true

module Api
  module Salesforce
    class DealsController < ApplicationController
      def index
        year = BudgetYear.find_by(budget_year_id: params[:budget_year_id]) || BudgetYear.current_year
        deal = Deal.find_by!(deal_id: params[:deal_id])
        budget = Budget.find_by!(deal_id: deal.deal_id, budget_year_id: year.budget_year_id)
                       .as_json.slice('deal_id', 'budget_id', 'send_to_salesforce', 'sf_deal_id', 'sf_opportunity_id')
        render json: budget
      rescue # rubocop:disable Style/RescueStandardError
        render json: { error: 'not-found' }.to_json, status: 404
      end
    end
  end
end
