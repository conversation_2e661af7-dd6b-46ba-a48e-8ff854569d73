# frozen_string_literal: true

module Api
  class BudgetManagementScenariosController < ApplicationController
    def index
      @budget_management_scenarios = BudgetManagementScenario.current_year.for_user(@current_user,
                                                                                    index_params[:buying_ae_only])
      render 'budget_management_scenarios/index', formats: :json
    end

    def index_params
      params.permit(:buying_ae_only)
    end
  end
end
