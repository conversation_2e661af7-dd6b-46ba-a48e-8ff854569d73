# frozen_string_literal: true

module Api
  class UsersController < ApplicationController
    def index
      query = params.permit(:query)[:query]
      render json: query ? User.searched_by(query) : []
    end

    def show
      user = with_includes(User).where(app_user_id: user_id)
      render json: formatted(user)
    end

    def update
      @user = User.find(user_id)
      @user.update!(update_params)
      notify_team_member_change
    end

    private

    def update_params
      params.permit(permitted_params_for(User))
    end

    def user_id
      params.require(:id)
    end

    def with_includes(user)
      user.includes(:location,
                    :department,
                    :app_user_title,
                    :manager,
                    team_members: %i[location department app_user_title])
    end

    def formatted(user)
      user.as_json(methods: %i[full_name department_name app_user_title_name location_name manager_name],
                   include: { team_members: {
                     methods: %i[full_name department_name app_user_title_name location_name manager_name]
                   } })
    end

    def notify_team_member_change
      return unless @user.manager_id_previous_change

      TeamMemberMailer
        .with(user: @user, manager_id_change: @user.manager_id_previous_change)
        .notify_team_member_change
        .deliver_later
    end
  end
end
