# frozen_string_literal: true

module Api
  class AgencyOfRecordsController < ApplicationController
    def update
      params.require(:agency_of_records)
      ActiveRecord::Base.transaction do
        params[:agency_of_records].each do |params|
          update_record_with(update_params: params)
        end
      end
      render json: { message: 'AOR records successfully updated.' }, status: :ok
    end

    private

    def update_record_with(update_params:)
      update_params.require(REQUIRED_UPDATE_PARAMS)
      permitted_params = update_params.permit(*REQUIRED_UPDATE_PARAMS, :aor, :end_date)
      mapped_params = mapped_params_from(permitted_params)

      aor = existing_by_sfdc_id(mapped_params) || existing_or_create_by_pam_values(mapped_params)
      aor.update!(mapped_params)
    end

    def mapped_params_from(params)
      AgencyOfRecordServices::Mapper.new(params).process
    end

    def existing_by_sfdc_id(params)
      AgencyOfRecord.find_by(sfdc_id: params[:sfdc_id])
    end

    def existing_or_create_by_pam_values(params)
      AgencyOfRecord
        .where(advertiser_id: params[:advertiser_id],
               agency_id: params[:agency_id],
               property_type_id: params[:property_type_id],
               sfdc_id: nil)
        .first_or_create
    end

    REQUIRED_UPDATE_PARAMS = %i[
      sfdc_id
      agency_name
      sfdc_agency_id
      advertiser_name
      sfdc_advertiser_id
      sfdc_default_category_id
      sfdc_property_type_id
      sfdc_location_id
    ].freeze
  end
end
