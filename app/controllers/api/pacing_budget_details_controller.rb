# frozen_string_literal: true

module Api
  class PacingBudgetDetailsController < ApplicationController
    def index
      render json: select_distinct(pacing_budget_details)
        .as_json(
          methods: %i[advertiser_name property_name agency_name buying_ae_name client_ae_name],
          except: :pacing_budget_detail_id
        )
    end

    private

    def pacing_budget_details
      @pacing_budget_details ||= filter(PacingBudgetDetail.joins(:property))
    end

    def select_distinct(query)
      query.select(
        :pacing_budget_id,
        :agency_id,
        :advertiser_id,
        :property_id,
        :bae_app_user_id,
        :cae_app_user_id
      ).distinct
    end

    # default the calendar year to current, and then add filters for property/property_type/selling_vertical
    # and return if those salesforce ids have been found via the mappings. omit the filter if no mapping
    def filter(query)
      query = query.where(advertiser_id: pam_advertiser_ids, calendar_year: CalendarYear.current_calendar_year)
      return query.where(property_id: pam_property_ids) if pam_property_ids.any?
      return query.where(property: { property_type_id: pam_property_type_ids }) if pam_property_type_ids.any?
      return query.where(property: { selling_vertical_id: pam_selling_vertical_ids }) if pam_selling_vertical_ids.any?

      query
    end

    def pam_advertiser_ids
      @pam_advertiser_ids ||= pam_ids_for(:advertiser)
    end

    def pam_property_ids
      @pam_property_ids ||= pam_ids_for(:property)
    end

    def pam_property_type_ids
      @pam_property_type_ids ||= pam_ids_for(:property_type)
    end

    def pam_selling_vertical_ids
      @pam_selling_vertical_ids ||= pam_ids_for(:selling_vertical)
    end

    def pam_ids_for(type)
      system_key_associations.where(
        external_system_key: params.require("sfdc_#{type}_id"), external_key_type_id: ExternalKeyType.send(type)&.id
      ).map(&:pam_key)
    end

    def system_key_associations
      @system_key_associations ||=
        SystemKeyAssociation
        .joins(:external_system, :external_key_type)
        .where(external_system: { external_system_name: 'SFDC' })
        .where(system_key_asc_type: [SystemKeyAssociationType.inbound, SystemKeyAssociationType.combined])
    end
  end
end
