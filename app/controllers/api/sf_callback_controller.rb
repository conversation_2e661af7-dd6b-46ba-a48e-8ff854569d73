# frozen_string_literal: true

module Api
  class SfCallbackController < ApplicationController
    # To support of updating status after integration program synced with salesforce.
    # PAM_657
    def update_budget_sync_status
      budget_id = budget_params[:budget_id]
      budget = Budget.find(budget_id)
      budget.update_columns(sf_sync_status: budget_params[:sf_sync_status])
      head :ok
    end

    # To support syncing of 80 wml opportunities back to budget so that sync is disabled.
    # PAM_719
    def update_budget_sync_status_by_deal_ids
      # Splitting the deal_ids string into an array of individual IDs
      deal_ids = budget_params[:deal_ids].split(',')

      # Retrieve the current budget year ID
      budget_year_id = BudgetYear.current_year_id

      # Iterate over each deal_id to find the corresponding budget and update it
      deal_ids.each do |deal_id|
        budget = Budget.find_by(deal_id:, budget_year_id:)

        # Update the sf_sync_status for the found budget
        budget&.update_columns(sf_sync_status: budget_params[:sf_sync_status])
      end

      # Return HTTP status :ok once updates are done
      head :ok
    end

    def update_budgets
      budget_ids = budgets_params[:budget_ids].split(',')
      budgets = Budget.where(budget_id: budget_ids)
      budgets.each do |budget|
        budget.update_columns(sf_deal_id: budgets_params[:sf_deal_id],
                              sf_opportunity_id: budgets_params[:sf_opportunity_id])
      end
      head :ok
    end

    def update_special_event_details
      special_event_details_ids =
        special_event_details_params[:special_event_details_ids].split(',')
      special_event_details = PamClient::SpecialEventDealDetail.where(
        special_event_deal_detail_id: special_event_details_ids
      )

      special_event_details.each do |special_event_detail|
        update_data = {}

        if special_event_details_params[:sf_deal_id].present?
          update_data[:sf_deal_id] =
            special_event_details_params[:sf_deal_id]
        end
        if special_event_details_params[:sf_opportunity_id].present?
          update_data[:sf_opportunity_id] =
            special_event_details_params[:sf_opportunity_id]
        end
        if special_event_details_params[:sf_sync_status].present?
          update_data[:sf_sync_status] =
            special_event_details_params[:sf_sync_status]
        end

        special_event_detail.update_columns(update_data) unless update_data.empty?
      end

      head :ok
    end

    def update_parent_deal
      parent_deal = ParentDeal.find_by!(parent_deal_id: parent_deal_params[:parent_deal_id])
      parent_deal.update_columns(sf_deal_id: params[:sf_deal_id])
      head :ok
    end

    private

    def special_event_details_params
      params.permit(:special_event_details_ids, :sf_deal_id, :sf_opportunity_id, :sf_sync_status)
    end

    def budget_params
      params.permit(:budget_id, :sf_sync_status, :deal_ids)
    end

    def budgets_params
      params.permit(:budget_ids, :sf_deal_id, :sf_opportunity_id)
    end

    def parent_deal_params
      params.permit(:parent_deal_id, :sf_deal_id)
    end
  end
end
