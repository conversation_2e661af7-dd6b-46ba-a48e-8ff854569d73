# frozen_string_literal: true

module Api
  class ExternalDealsController < ApplicationController
    def index
      render json: filter_dataset(data).select(*symbol_columns, *string_columns)
    end

    private

    def symbol_columns
      %i[
        property_id
        property_name
        sales_system_id
        external_deal_id
        deal_name
        vertical_id
        vertical_name
        quarter_name
        marketplace_id
        marketplace_name
        bae_split
        cae_split
      ]
    end

    def string_columns
      [
        'external_deal_detail.quarter_id',
        'pd_ed_bridge.pacing_budget_id',
        'external_deal_detail.sales_dollars',
        'quarter.calendar_year_id',

        'bae.app_user_id as bae_app_user_id',
        "bae.first_name || ' ' || bae.last_name bae_app_user_name",
        'external_bae.app_user_id as external_bae_app_user_id',
        "external_bae.first_name || ' ' || external_bae.last_name external_bae_app_user_name",

        'cae.app_user_id as cae_app_user_id',
        "cae.first_name || ' ' || cae.last_name cae_app_user_name",
        'external_cae.app_user_id as external_cae_app_user_id',
        "external_cae.first_name || ' ' || external_cae.last_name external_cae_app_user_name",

        'advertiser.advertiser_id',
        'advertiser.advertiser_name',
        'external_advertiser.advertiser_id as external_advertiser_id',
        'external_advertiser.advertiser_name as external_advertiser_name',

        'agency.agency_id',
        'agency.agency_name',
        'external_agency.agency_id as external_agency_id',
        'external_agency.agency_name as external_agency_name'
      ]
    end

    # Base dataset from ExternalDeal joined to the ExternalDealDetail,
    # as well as the associated pacing record data using the pd_ed_bridge table.
    #
    def data
      ExternalDeal
        .where(active: true)
        .joins('join external_deal_detail on external_deal_detail.external_deal_id = external_deal.external_deal_id')
        .joins('join quarter on quarter.quarter_id = external_deal_detail.quarter_id')
        .joins('join agency external_agency on external_agency.agency_id = external_deal.agency_id')
        .joins('join advertiser external_advertiser ' \
               'on external_advertiser.advertiser_id = external_deal.advertiser_id ' \
               "and external_advertiser.advertiser_type in ('Advertiser')")
        .joins('join property on property.property_id = external_deal.property_id')
        .joins('join vertical on vertical.vertical_id = external_deal.vertical_id')
        .joins('join marketplace on marketplace.marketplace_id = external_deal.marketplace_id')
        .joins('join pd_ed_bridge on external_deal.sales_system_id = pd_ed_bridge.sales_system_id ' \
               'and external_deal_detail.quarter_id = pd_ed_bridge.quarter_id ' \
               'and external_deal.property_id = pd_ed_bridge.property_id ' \
               'and external_deal.vertical_id = pd_ed_bridge.vertical_id ' \
               'and external_deal.sales_type_id = pd_ed_bridge.sales_type_id')
        .joins('join pacing_budget_detail on ' \
               'pd_ed_bridge.pacing_budget_detail_id = pacing_budget_detail.pacing_budget_detail_id ' \
               'and quarter.calendar_year_id = pacing_budget_detail.calendar_year_id ' \
               'and quarter.quarter_id = pacing_budget_detail.quarter_id')
        .joins('join advertiser on pacing_budget_detail.advertiser_id = advertiser.advertiser_id')
        .joins('join agency on pacing_budget_detail.agency_id = agency.agency_id')
        .joins('left join ae_split on ae_split.property_id = external_deal.property_id')
        .joins('left join app_user bae on bae.app_user_id = pd_ed_bridge.bae_app_user_id')
        .joins('left join app_user cae on cae.app_user_id = pd_ed_bridge.cae_app_user_id')
        .joins('left join app_user external_bae on external_bae.app_user_id = external_deal_detail.bae_app_user_id')
        .joins('left join app_user external_cae on external_cae.app_user_id = external_deal_detail.cae_app_user_id')
        .where(external_deal_detail: {
                 business_type_id: [BusinessType.current_contracts&.id, BusinessType.working&.id], active: true
               })
    end

    def filter_dataset(dataset)
      unless params[:calendar_year_id]
        dataset = dataset.where('quarter.calendar_year_id = ?', CalendarYear.current_calendar_year&.id)
      end

      (params.keys & filterable_attributes.values.flatten).each do |attribute|
        dataset = dataset.where("#{table_for(attribute)}.#{attribute} in (?)", parse_array_params(params[attribute]))
      end

      dataset
    end

    def table_for(attribute)
      filterable_attributes.find { |_k, v| v.include?(attribute) }.first
    end

    # { table_name: [*columns] }
    def filterable_attributes
      @filterable_attributes ||= {
        external_deal: %w[
          property_id
          sales_system_id
          vertical_id
          marketplace_id
        ],
        pacing_budget_detail: %w[pacing_budget_id advertiser_id agency_id],
        pd_ed_bridge: %w[bae_app_user_id cae_app_user_id],
        quarter: %w[quarter_id calendar_year_id]
      }
    end

    class NoAssignmentsError < Error::ApiError::UnauthorizedError; end
  end
end
