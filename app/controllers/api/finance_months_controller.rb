# frozen_string_literal: true

module Api
  class FinanceMonthsController < ApplicationController
    def index
      render json: index_data
    end

    private

    def index_data
      ds = FinanceMonth
      ds = ds.where(default_month: boolean_eval(index_params[:default_month])) if index_params[:default_month]
      ds = ds.where(historical_month: boolean_eval(index_params[:historical_month])) if index_params[:historical_month]
      ds = ds.twelve_months if !boolean_eval(index_params[:include_full_year]) && index_params[:include_full_year]
      ds.order(:display_order)
    end

    def index_params
      params.permit(:default_month, :historical_month, :include_full_year)
    end
  end
end
