# frozen_string_literal: true

module Api
  class FinanceUploadsController < ApplicationController
    def index
      render json: index_data
    end

    private

    def index_data
      ds = FinanceUpload.by_user(@current_user)
      ds = ds.where(status: index_params[:status]) if index_params[:status]
      ds = ds.limit(index_params[:limit]) if index_params[:limit]
      ds.order('UPDATED_AT DESC')
    end

    def index_params
      params.permit(:limit, :status)
    end
  end
end
