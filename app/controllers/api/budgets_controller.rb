# frozen_string_literal: true

module Api
  class BudgetsController < ApplicationController
    include BudgetChangeNotification
    include BudgetsControllerHelper

    def show
      render json: include_additional_data? ? budget_with_additional_data : budget
    end

    def index
      render json: include_additional_data? ? budgets_with_additional_data : budgets
    end

    def update
      budget.assign_attributes(update_params)

      if touch?
        budget.save!
      else
        ActiveRecord::Base.no_touching do
          budget.save!(touch: false)
        end
      end

      render json: include_additional_data? ? budget_with_additional_data : budget
    end

    def create
      @budget = Budget.create!(create_params)

      render json: budget_with_additional_data(budget.id), status: :created
    end

    def batch_update
      ActiveRecord::Base.transaction do
        params.require(:budgets).each do |budget|
          Budget.find(budget.require(:budget_id))
                .update!(budget.permit(updatable_attributes))
        end
      end

      render json: { message: 'Budgets successfully updated.' }, status: :ok
    end

    private

    def budget
      @budget ||= Budget.find(params.require(:id))
    end

    def budgets
      @budgets ||= Budget
                   .joins(:budget_year)
                   .where(deal_id: params.require(:deal_id))
                   .order('budget_year.budget_year_name')
    end

    def show_params
      params.permit(:include_additional_data)
    end

    def update_params
      params.permit(updatable_attributes + budget_sales_systems_attributes)
    end

    def create_params
      params.require(constructive_attributes)
      params.permit(permitted_params_for(Budget) + Budget::CALCULATED_ATTRIBUTES)
    end

    def updatable_attributes
      permitted_params_for(Budget) - constructive_attributes + Budget::CALCULATED_ATTRIBUTES
    end

    # attributes required to construct a budget
    def constructive_attributes
      %w[budget_year_id deal_id]
    end

    def include_additional_data?
      boolean_eval(params[:include_additional_data])
    end

    def budget_sales_systems_attributes
      [
        budget_sales_systems_attributes: %i[id sales_system_id _destroy]
      ]
    end

    def touch?
      return false if params[:touch].present? && params[:touch] == 'false'

      true
    end
  end
end
