# frozen_string_literal: true

module Api
  class DealLinkAssociationsController < ApplicationController
    def create
      @deal_link_association = DealLinkAssociation.create!(
        deal_id: params.require(:deal_id),
        deal_link_id: params.require(:deal_link_id)
      )
      render json: return_deals? ? updated_deals : deal_link_association, status: :created
    end

    def batch_create
      @deal_link_associations =
        ActiveRecord::Base.transaction do
          batch_create_params[:deal_link_associations].map do |deal_link_asc_params|
            DealLinkAssociation.create!(deal_link_asc_params)
          end
        end

      render json: @deal_link_associations, status: :created
    end

    def destroy
      deal_link_association.destroy!
      return_deals? ? (render json: updated_deals) : (head :ok)
    end

    private

    def return_deals?
      boolean_eval(params[:return_deals])
    end

    def deal_link_association
      @deal_link_association ||= DealLinkAssociation.find(params.require(:id))
    end

    def updated_deals
      ApiDeal.where(
        deal_id: DealLinkAssociation.where(deal_link_id: deal_link_association.deal_link_id).select(:deal_id),
        budget_year_id: params.require(:budget_year_id)
      ).or(ApiDeal.where(deal_id: deal_link_association.deal_id, budget_year_id: params.require(:budget_year_id)))
    end

    def batch_create_params
      params.require(:deal_link_associations)
      params.permit(deal_link_associations: %i[deal_id deal_link_id])
    end
  end
end
