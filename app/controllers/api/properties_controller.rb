# frozen_string_literal: true

module Api
  class PropertiesController < ApplicationController
    include Publishable

    def publish
      Pubsub::Publisher.prop_sync(Property.publishable)

      head :ok
    end

    def image
      property = Property.find(params[:id])

      raise Error::ApiError::UnprocessableEntityError, "Property Not Found by id: #{property_id}" unless property

      bucket = s3_bucket
      obj = bucket.object(property.image_file_name)

      begin
        image = obj.get.body.read
      rescue Aws::S3::Errors::NoSuchKey
        image = fetch_default_image_from(bucket:)
      end

      render plain: image
    end

    private

    def fetch_default_image_from(bucket:)
      obj = bucket.object(Property.default_image_file_name)
      begin
        obj.get.body.read
      rescue Aws::S3::Errors::NoSuchKey
        raise Error::ApiError::BadRequestError, "No image found in bucket with name #{Property.default_image_file_name}"
      end
    end
  end
end
