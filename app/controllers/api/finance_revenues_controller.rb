# frozen_string_literal: true

module Api
  class FinanceRevenuesController < ApplicationController
    before_action :initialize_values, only: [:index]
    before_action :preferred_month_year_for_user, only: [:index]

    def index
      @properties = Property.order(:property_name).all_finance_models
      @current_est_mod_name = FinanceModel.monthly_type_model.finance_model_name
      current_month = UserPreference.default_current_month
      @property_summaries = FinanceRevenue.property_list_page_data(@properties, @preferred_year, current_month)

      revenue_values = CdwFinanceRevenueVw.where(property_id: params[:property_id],
                                                 calendar_year_id: @calendar_year_id)
      render json: revenue_values
    end

    def rating_impressions
      qtrs = FinanceQuarter.where(name: params[:qtr])
      if qtrs.empty?
        render json: []
        return
      end

      render json: FinanceRatingImpression.where(finance_quarter: qtrs)
    end

    fin_actions = %i[metric_types model_types months models quarters revenue_types property_type]
    fin_actions.each do |action|
      define_method action.to_s do
        clazz = Object.const_get("Finance#{action.to_s.singularize.camelcase}", false)
        render json: clazz.unscope(:where) # unscope = return vals w/ -1 id
      end
    end

    # Return JSON of FinanceHeaders data. Accepts request
    # parameters `qtr` (of form e.g. 'Q1-16') and `fin_mon` (of form e.g.
    # 'May') request parameters.
    #
    # @return JSON of FinanceHeaders.
    def headers
      fqtrs = FinanceQuarter.where(name: params[:qtr]).includes(:calendar_year)
      cys = fqtrs.collect(&:calendar_year).uniq
      fin_data = FinanceHeader.where(calendar_year: cys)

      if params[:fin_mon] && !params[:fin_mon].empty?
        fmons = FinanceMonth.where(name: params[:fin_mon])
        fin_data = fin_data.where(finance_month: fmons)
      end

      render json: fin_data
    end

    # Return JSON of Finance<X> data, where X is one of Revnue, Cpm, Unit, or MakeGood.
    #
    # @return [Array<JSONObject>]
    %i[revenue cpm units make_good].each do |action|
      define_method action.to_s do
        if params[:prop_id].nil? || params[:prop_id].empty?
          render json: []
          return
        end

        years = params[:qtr].collect { |qtr| "20#{qtr[3, 4]}".to_i }.uniq
        cal_years = CalendarYear.where(calendar_year: years)

        clazz = Object.const_get("Finance#{action.to_s.singularize.camelcase}", false)
        result = clazz
                 .joins(:finance_header)
                 .where(finance_header: { property_id: params[:prop_id] })
                 .where(finance_header: { calendar_year_id: cal_years })

        render json: result
      end
    end

    # Return JSON of PamClient::Alocation data.
    def allocations
      render json: PamClient::Allocation.all
    end

    private

    def assign_month_calendar
      @current_year_finance_models = FinanceModel.current_year_type_models
      @calendar_years = CalendarYear.finance_model_calendar_years
      @finance_months = FinanceMonth.twelve_months
    end

    def preferred_month_year_for_user
      @calendar_year_id = CalendarYear.find_by_calendar_year(params[:calendar_year]).id
      calendar_year = params[:calendar_year] if params[:calendar_year]
      year = calendar_year.nil? ? UserPreference.get_current_finance_calendar_year : CalendarYear.find_by_calendar_year(calendar_year) # rubocop:disable Layout/LineLength
      @preferred_year = year
      @preferred_month = UserPreference.default_current_month
    end

    def calendar_year_preference(calendar_year)
      CalendarYear.find_by_calendar_year(calendar_year).id
    end

    def finance_month_preference(finance_month)
      FinanceMonth.find_by_finance_month_name(finance_month).id
    end

    def initialize_values
      assign_month_calendar
      preferred_month_year_for_user
    end
  end
end
