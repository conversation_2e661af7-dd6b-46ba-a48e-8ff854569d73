# frozen_string_literal: true

module Api
  # API controller for splits-related functionality
  class SplitsController < ApplicationController
    include Publishable

    def locks_and_splits
      budgets = Budget.where(budget_id: params[:budget_ids].split(','))
      return unless budgets.any?

      locks_and_splits = budgets.to_h do |budget|
        cache_key = "#{LocksAndSplitsRefreshJob::SPLITS_CACHE_KEY_BASE}#{budget.id}"
        Rails.cache.fetch(cache_key) do
          [budget.id, PamClient::SplitterInterface.new(budget, budget.prior_year_budget).get]
        end
      end

      render json: locks_and_splits
    end

    def refresh_splits
      LocksAndSplitsRefreshJob.perform_now

      head :ok
    end

    def publish
      splits = PamClient::PropertyBudgetSplit.all
      Pubsub::GenericPublisher.Publish(:property_budget_splits, splits)

      head :ok
    end
  end
end
