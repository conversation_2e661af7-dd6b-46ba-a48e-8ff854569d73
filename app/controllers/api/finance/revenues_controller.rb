# frozen_string_literal: true

module Api
  module Finance
    class RevenuesController < BaseController
      def index
        render json: getter(index_params).process
      end

      private

      def getter(params)
        property = Property.find(params[:property_id])

        @getter ||= (property.digital? ? RevenueServices::DigitalGetter : RevenueServices::LinearGetter)
                    .new(property,
                         FinanceMonth.find(params[:finance_month_id]),
                         CalendarYear.find(params[:calendar_year_id]),
                         getter_opts)
      end

      def updater
        @updater ||= RevenueServices::Updater.new(@current_user)
      end
    end
  end
end
