# frozen_string_literal: true

module Api
  module Finance
    class PropertiesController < ApplicationController
      def index
        render json: index_data
      end

      private

      def index_params
        strong_require_and_permit(:calendar_year_id)
      end

      def index_data
        Property.for_finance_user(@current_user)
                .joins(:property_type)
                .joins("left join (
                          #{FinanceHeader.most_recent_by_property(index_params['calendar_year_id'].to_i).to_sql}
                        ) FINANCE_HEADER
                        on FINANCE_HEADER.PROPERTY_ID = PROPERTY.PROPERTY_ID
                        left join APP_USER on APP_USER.APP_USER_ID = FINANCE_HEADER.APP_USER_ID")
                .select("
                    PROPERTY.PROPERTY_ID,
                    PROPERTY_NAME,
                    PROPERTY_TYPE_NAME,
                    IMAGE_FILE_NAME,
                    FINANCE_HEADER.UPDATED_AT as last_updated_at,
                    (case
                    when (FINANCE_HEADER.UPDATED_AT is null or APP_USER.FIRST_NAME is null) then null
                     else (APP_USER.FIRST_NAME || ' ' || APP_USER.LAST_NAME) end) as last_updated_by
                  ")
                .where(include_finance_model: true, active: true)
                .order(:property_name)
      end
    end
  end
end
