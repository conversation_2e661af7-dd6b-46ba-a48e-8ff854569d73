# frozen_string_literal: true

module Api
  module Finance
    class BaseController < ApplicationController
      def index
        @property = Property.find(index_params[:property_id])
        raise Error::ApiError::BadRequestError, 'Not applicable to digital properties.' if @property.digital?

        @finance_month = FinanceMonth.find(index_params[:finance_month_id])
        @calendar_year = CalendarYear.find(index_params[:calendar_year_id])

        render json: getter.process
      end

      def bulk_update
        updater.process(update_params.as_json.map(&:symbolize_keys))
        render json: { message: 'Updated successfully.' }, status: :ok
      end

      private

      def index_params
        params.require(%i[calendar_year_id finance_month_id property_id])
        params.permit(:calendar_year_id, :finance_month_id, :property_id, :finance_model_id, :format_response)
      end

      def update_params
        params.require(:data)
      end

      def getter
        raise 'Needs to be declared by inheriting controller'
      end

      def updater
        raise 'Needs to be declared by inheriting controller'
      end

      def getter_opts
        opts = {}
        opts[:format_response] = index_params[:format_response] if index_params[:format_response]
        opts[:finance_model_id] = index_params[:finance_model_id] if index_params[:finance_model_id]
        opts
      end
    end
  end
end
