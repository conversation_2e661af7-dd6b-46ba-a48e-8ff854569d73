# frozen_string_literal: true

# POST finance/upload allows users to upload an spreadsheet of finance data
# they'd like to create/update instead having to go through the UI tabs.
# The actual acts of value validations as well as updating the records
# in the db are performed via the upload service, which if succeeds, endpoint
# will return the result in json or csv format user can then download and examine.
# The POST endpoint also saves a record of these uploads in json blobs stored in
# FINANCE_UPLOAD table, which can be fetched from GET api/finance_uploads

module Api
  module Finance
    class UploadController < ApplicationController
      include XlsxUploadHelper

      def create
        params = create_params
        calendar_year = CalendarYear.find(params[:calendar_year_id])
        finance_month = FinanceMonth.find(params[:finance_month_id])

        # exclude all sheets whose names start with '_' and also a sheet named Mozart Reports
        input = parse_file(params[:data].tempfile) do |sheets|
          sheets - sheets.select { |sheet| sheet.first == '_' || sheet == 'Mozart Reports' }
        end
        result = UploadServices::Uploader.new(@current_user, calendar_year, finance_month).process_file(input)

        FinanceUpload.create!(user: @current_user, input: create_params.to_hash.merge('data' => input).to_json,
                              output: result.result.to_json, status: result.status)

        if create_params[:response_format] == 'xlsx'
          @output = result.result
          render xlsx: output_filename(result.status,
                                       finance_month,
                                       calendar_year),
                 template: 'finance/upload_output',
                 formats: [:xlsx],
                 status: result.status
        else
          render json: result.result, status: result.status
        end
      end

      private

      def create_params
        params.require(%i[data finance_month_id calendar_year_id])
        params.permit(:data, :finance_month_id, :calendar_year_id, :response_format)
      end

      def output_filename(status, finance_month, calendar_year)
        now = Time.now.strftime('%Y%m%d %H%M%S')
        "#{'Failed_' unless status == :ok}Finance_Upload_#{finance_month.name}_#{calendar_year.calendar_year}_#{now}"
      end
    end
  end
end
