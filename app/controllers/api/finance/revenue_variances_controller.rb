# frozen_string_literal: true

module Api
  module Finance
    class RevenueVariancesController < BaseController
      private

      def index_params
        strong_require_and_permit(:calendar_year_id, :finance_month_id, :property_id)
      end

      def getter
        @getter ||= RevenueVarianceServices::Getter.new(@property, @finance_month, @calendar_year)
      end

      def updater
        @updater ||= RevenueVarianceServices::Updater.new(@current_user)
      end
    end
  end
end
