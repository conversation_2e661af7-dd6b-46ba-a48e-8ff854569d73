# frozen_string_literal: true

module Api
  class PortalTeamsController < ApplicationController
    include Sortable

    def index
      @portal_teams = PortalTeam
                      .includes(:agency, :budget_year, :marketplace, :portal_type)
                      .where(index_params)
                      .system_key_joins
                      .select(%(portal_team.*,
          agency_asc.external_system_key as agency_sfdc_key,
          budget_year_asc.external_system_key as budget_year_sfdc_key,
          marketplace_asc.external_system_key as marketplace_sfdc_key))

      @portal_teams = @portal_teams.active_users if boolean_eval(params[:filter_active_users])
      @portal_teams = sort_collection(@portal_teams)

      render 'portal_teams/index', formats: :json
    end

    def update
      portal_team.update!(update_params)
      PortalTeamsService.new(portal_team).set_assignment_and_role(stealth, 'update')
      render json: portal_team
    end

    def create
      @portal_team = PortalTeam.create!(create_params)
      PortalTeamsService.new(portal_team).set_assignment_and_role(stealth, 'create')
      render json: portal_team, status: :created
    end

    private

    def index_params
      params.permit(*constructive_attributes, :active)
    end

    def update_params
      params.permit(permitted_params_for(PortalTeam) - constructive_attributes)
    end

    def create_params
      params.require(constructive_attributes)
      params.permit(permitted_params_for(PortalTeam))
    end

    def portal_team
      @portal_team ||= PortalTeam.find(params.require(:id))
    end

    # attributes required to construct a portal_team
    def constructive_attributes
      %w[budget_year_id marketplace_id agency_id]
    end

    def stealth
      boolean_eval(params[:edit_stealth_mode])
    end
  end
end
