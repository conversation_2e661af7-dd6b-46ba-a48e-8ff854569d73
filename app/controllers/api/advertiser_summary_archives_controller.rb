# frozen_string_literal: true

module Api
  # Controller for Advertiser Summary Archives
  class AdvertiserSummaryArchivesController < ApplicationController
    # create a snapshot of advertiser_summary table and
    # all the additional data returned by GET advertiser_summaries#index
    def create
      ActiveRecord::Base.transaction do
        @header_id = AdvertiserSummaryArchiveHeader.create!(budget_year_id:).id
        detail_attrs = AdvertiserSummaryArchiveDetail.columns.map(&:name)

        Advertiser.summaries_by(:advertiser, budget_year_id:).all.each do |row|
          AdvertiserSummaryArchiveDetail.create!(
            row.serializable_hash.slice(*detail_attrs).merge(adv_summary_archive_header_id: @header_id)
          )
        end
      end

      render json: base_archive_header.where(adv_summary_archive_header_id: @header_id).first,
             status: :created
    end

    def index
      render json: base_archive_header
        .where(budget_year_id:)
        .order('adv_summary_archive_header.created_at desc')
    end

    private

    def base_archive_header
      AdvertiserSummaryArchiveHeader.joins(:budget_year).left_joins(:last_updated_by)
                                    .select("
          adv_summary_archive_header.*,
          first_name || ' ' || last_name as last_updated_by_user,
          budget_year_name")
    end

    def budget_year_id
      params[:budget_year_id] || BudgetYear.current_year.id
    end
  end
end
