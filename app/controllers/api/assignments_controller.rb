# frozen_string_literal: true

module Api
  class AssignmentsController < ApplicationController
    def index
      assignments = base_with_includes.by_user(user_id)
                                      .by_assignment_type(params[:type])

      render json: formatted(assignments)
    end

    def create
      @assignment = Assignment.create!(create_params)
      render json: @assignment, status: :created
    end

    def update
      @assignment = Assignment.find(params[:id])
      @assignment.update!(update_params)
      render json: @assignment
    end

    def destroy
      Assignment.find(params[:id]).destroy!
      head :ok
    end

    private

    def user_id
      params.require(:user_id)
    end

    def base_with_includes
      Assignment.includes(
        :property,
        :agency,
        :advertiser,
        :property_type,
        :category,
        :deal_tag,
        :division,
        :subdivision,
        :selling_vertical,
        :partnership
      )
    end

    def formatted(assignments)
      assignments.as_json(
        methods: %i[
          property_name
          agency_name
          advertiser_name
          property_type_name
          category_name
          deal_tag_name
          division_name
          subdivision_name
          selling_vertical_name
          partnership_name
        ]
      )
    end

    def create_params
      params.merge(app_user_id: user_id)
            .permit(permitted_params_for(Assignment))
    end

    def update_params
      params.permit(permitted_params_for(Assignment))
    end
  end
end
