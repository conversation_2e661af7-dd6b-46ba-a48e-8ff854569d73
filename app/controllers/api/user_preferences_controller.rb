# frozen_string_literal: true

module Api
  class UserPreferencesController < ApplicationController
    def index
      params = index_params

      @user_preferences = @current_user.user_preferences
      if params[:preference_type]
        @user_preferences = @user_preferences
                            .by_type(
                              parse_array_params(params[:preference_type]).map { |type| type.humanize.titleize }
                            )
      end
      render json: mapped_names(@user_preferences.all)
    end

    def update
      params = update_params

      preference_type_id = UserPreferenceType.find_by(
        user_preference_type_name: params[:preference_type].titleize(keep_id_suffix: true)
      )&.id

      if (preference = UserPreference.find_by(app_user_id: @current_user.id,
                                              user_preference_type_id: preference_type_id))
        preference.update!(state: params[:state])
        render json: { message: 'Preference was successfully updated.' }, status: :ok
      else
        UserPreference.create!(app_user_id: @current_user.id, user_preference_type_id: preference_type_id,
                               state: params[:state])
        render json: { message: 'New preference was successfully added.' }, status: :created
      end
    end

    private

    def index_params
      params.permit(:preference_type)
    end

    def update_params
      strong_require_and_permit(:preference_type, :state)
    end

    def mapped_names(user_preferences)
      user_preferences.map do |preference|
        hash = preference.attributes
        hash['preference_type'] = preference.user_preference_type_name.parameterize.underscore.upcase
        hash
      end
    end
  end
end
