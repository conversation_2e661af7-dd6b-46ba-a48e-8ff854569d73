# frozen_string_literal: true

module Api
  class PortfolioMonetizationsController < ApplicationController
    include XlsxUploadHelper

    class UploadError < Error::ApiError::BadRequestError; end

    def upload
      ActiveRecord::Base.transaction do
        PortfolioMonetization.where(budget_year_id:).delete_all

        input[:sheet_data].each do |row|
          PortfolioMonetization.create!(format_row(row))
        end
      end

      head :ok
    end

    private

    def input
      # find a sheet named EXECUTIVE SUMMARY or if that doesnt exist, get first sheet
      parsed = parse_file(params[:data].tempfile) do |sheets|
        Array(sheets.find { |sheet| sheet == 'EXECUTIVE SUMMARY' } || sheets[0])
      end.first

      unless (required_fields - parsed[:sheet_headers]).empty?
        raise UploadError,
              "Missing one of the required fields: #{required_fields.map(&:to_s).map(&:titleize).join(', ')}"
      end

      parsed
    end

    def format_row(row)
      {
        deal_id: row[:deal],
        registration: row[:pm_registrations],
        projection: row[:pm_projections],
        budget_year_id:
      }
    end

    def required_fields
      %i[deal pm_registrations pm_projections]
    end

    def budget_year_id
      @budget_year_id ||= params[:budget_year_id] || BudgetYear.current_year.id
    end
  end
end
