# frozen_string_literal: true

module Api
  # This controller's only purpose is to provide dropdown values for forms
  # in a standardized output format that can be re-used across various forms
  class FieldsController < ApplicationController
    PAGES = %w[
      deals
      new_deals
      new_parent_deals
      special_events
      pacing
    ].freeze

    # GET: /api/fields/
    # @return HTTP JSON response
    # ```json
    # {
    #   'label': String,
    #   'options': Array[{
    #     'id': String,
    #     'name': String,
    #     'default': Boolean?
    #   }]
    # }
    # ```
    def index
      check_page
      render json: send(params[:page]).value_object
    rescue PamClient::Error::InvalidOptionsError => e
      render json: { label: "InvalidOptionsError #{e.message}", options: [] }
    end

    private

    def check_page
      page_param = params[:page]
      return if PAGES.include?(page_param)

      msg = "Invalid 'page' option: '#{page_param}'"
      raise PamClient::Error::InvalidOptionsError, msg
    end

    # @name deals
    # @description
    #   Provides dropdown field values instance for the deal-headers create & edit pages
    #   Called dynamically using `params[:page]`
    # @return RegistrationDropdownValues
    def deals
      RegistrationDropdownValues.new(deal_params)
    end

    # @name new_deals
    # @description
    #   Provides dropdown field values instance for the deal-headers create & edit pages
    #   Called dynamically using `params[:page]`
    # @return NewRegistrationDropdownValues
    def new_deals
      NewRegistrationDropdownValues.new(new_deal_params)
    end

    # @name new_parent_deals
    # @description
    #   Provides dropdown field values instance for the parent-deal-headers create & edit pages
    #   Called dynamically using `params[:page]`
    # @return NewParentDealDropdownValues
    def new_parent_deals
      NewParentDealDropdownValues.new(new_parent_deal_params)
    end

    # @name special_events
    # @description
    #   Provides dropdown field values instance for the special_event_deals create & edit pages
    #   Called dynamically using `params[:page]`
    # @return SpecialEventsDropdownValues
    def special_events
      SpecialEventsDropdownValues.new(special_events_params)
    end

    # @name pacing
    # @description
    #   Provides dropdown field values instance for the pacing create & edit pages
    #   Called dynamically using `params[:page]`
    # @return PacingDropdownValues
    def pacing
      PacingDropdownValues.new(deal_params)
    end

    def filter_params
      params.permit(
        %i[page type property_id special_event_id agency_id advertiser_id
           marketplace_id stealth_enabled selected_id] + select2_params
      )
    end

    # @name deal_params
    # @description
    #   Filters params for deals page
    # @return [Hash]
    def deal_params
      filter_params
        .permit(%i[type property_id agency_id advertiser_id])
        .merge(user_id: @current_user.id)
    end

    # @name new_deal_params
    # @description
    #   Filters params for new_deals page
    # @return [Hash]
    def new_deal_params
      filter_params
        .permit(%i[type property_id agency_id advertiser_id term stealth_enabled] + select2_params)
        .merge(user_id: @current_user.id)
    end

    # @name new_parent_deal_params
    # @description
    #   Filters params for new_parent_deals page
    # @return [Hash]
    def new_parent_deal_params
      filter_params
        .permit(%i[type agency_id advertiser_id marketplace_id term] + select2_params)
        .merge(user_id: @current_user.id)
    end

    # @name special_events_params
    # @description
    #   Filters params for special_events page
    # @return [Hash]
    def special_events_params
      filter_params
        .permit(%i[type special_event_id agency_id advertiser_id selected_id])
        .merge(user_id: @current_user.id)
    end

    def select2_params
      %i[term _type q]
    end
  end
end
