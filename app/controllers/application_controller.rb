# frozen_string_literal: true

require './lib/error/error_handler.rb'

class ApplicationController < ActionController::API
  include ActionController::MimeResponds
  include Error::ErrorHandler

  before_action :authenticate, :cleanse_params, :set_current_user

  protected

  def default_request_method
    request.request_method # "GET", "PUT", etc...
  end

  def permitted_params
    # permitting all params and let V1 handle validation
    params.permit!.except(:action, :controller, :smuser)
  end

  def strong_require_and_permit(*fields)
    params.require([fields])
    params.permit(fields)
  end

  def cleanse_params
    params.reject! { |_, v| v.blank? }
    params.each { |k, v| params[k] = nil if v.to_s.casecmp('none').zero? }
  end

  def authenticate
    @current_user ||= user_by_sso_id
    return if @current_user&.active && !@current_user.external_user

    raise Error::ApiError::UnauthorizedError,
          'Unauthorized user'
  end

  def sso_id
    params['smuser']
  end

  def user_by_sso_id
    User.find_by_sso_id(sso_id)
  end

  def set_current_user
    PamClient::User.current = @current_user
  end

  def permitted_params_for(model)
    model.column_names - unpermitted_params_for(model)
  end

  def unpermitted_params_for(model)
    %W[
      #{model.primary_key}
      created_at
      updated_at
      last_updated_by_id
    ]
  end

  def parse_array_params(string)
    return unless string

    string.split(',')
  end

  def boolean_eval(value)
    ActiveModel::Type::Boolean.new.cast(value)
  end

  def s3_bucket
    local_config = { endpoint: ENV['MINIO_ENDPOINT'], force_path_style: true } if Rails.env.development?
    s3 = Aws::S3::Resource.new({ region: 'us-east-1' }.merge(local_config || {}))
    s3.bucket(ENV['PAM_ASSETS_S3_BUCKET_NAME'])
  end
end
