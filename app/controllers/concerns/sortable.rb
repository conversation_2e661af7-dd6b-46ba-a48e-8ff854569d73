# frozen_string_literal: true

module Sortable
  extend ActiveSupport::Concern

  # Sorts collection by column_names in 'sort' param. items in this comma-separated
  # list an be any attribute or delegated attribute on the model of the collection.
  # if column name is prefixed with a '-', it will sort that column in reverse.
  #
  #   ref: https://jsonapi.org/format/#fetching-sorting
  #   example: ?sort=portal_type_display_order,-user_name
  #
  def sort_collection(collection)
    return collection unless sort_columns

    collection.sort do |a, b|
      result = (0...sort_columns.length).each do |i|
        column = sort_columns[i]

        if desc?(column)
          column = column[1..]
          reverse = -1
        else
          reverse = 1
        end

        break (a.try(column) <=> b.try(column)) * reverse unless a.try(column) == b.try(column)
      end
      result.is_a?(Range) ? 0 : result
    end
  end

  private

  def sort_columns
    @sort_columns ||= params[:sort]&.split(',')
  end

  def desc?(column_name)
    column_name.slice(0) == '-'
  end
end
