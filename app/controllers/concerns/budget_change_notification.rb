# frozen_string_literal: true

module BudgetChangeNotification
  extend ActiveSupport::Concern

  included do
    after_action :send_email_notification, only: :update
  end

  def send_email_notification
    return unless changed_to_prompt_zero_status? &&
                  (had_values_and_locked_for?(:actual) || had_values_and_locked_for?(:projected))

    BudgetChangeMailer.with(budget:).notify_prompt_zero.deliver_now
  end

  def changed_to_prompt_zero_status?
    status_id = budget.previous_changes['status_id']&.last
    return false unless status_id

    Status.find(status_id).prompt_zero
  end

  def had_values_and_locked_for?(value_type)
    locked_for?(value_type) && had_values_for?(value_type)
  end

  def locked_for?(value_type)
    deal = budget.deal

    locked = AgencyPropertyLock.find_by(
      budget_year_id: budget.budget_year_id,
      marketplace_id: deal.marketplace_id,
      property_id: deal.property_id,
      agency_id: deal.agency_id
    )&.send(value_type)
    return locked unless locked.nil?

    true
  end

  def had_values_for?(value_type)
    %i[prequarter quarter1 quarter2 quarter3 quarter4 postquarter]
      .map { |quarter| "#{value_type}_#{quarter}_amount" }.any? do |attribute|
        budget.previous_changes.fetch(attribute, [budget.send(attribute)]).first.positive?
      end
  end
end
