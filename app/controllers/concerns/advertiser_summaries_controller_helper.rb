# frozen_string_literal: true

module AdvertiserSummariesControllerHelper
  private

  # rubocop:disable Layout/LineLength
  def add_filters(dataset)
    return dataset if advertiser_filters.empty?

    base = dataset.superclass.name.demodulize == 'BaseAdvertiser' ? dataset : dataset.joins(:advertiser)
    base = base.where('advertiser.advertiser_id' => parse_array_params(params[:advertiser_id])) if params[:advertiser_id]
    base = base.where('advertiser.default_category_id' => parse_array_params(params[:default_category_id])) if params[:default_category_id]

    %i[client_partnership_category client_partnership_svp target_classification].each do |classification_type|
      next unless (values = parse_array_params(params["#{classification_type}_id"]))

      base = base.where('advertiser.advertiser_id in (?)', AdvertiserClassificationAssociation
        .joins(:advertiser_classification)
        .where('adv_classification_type_id = ?', AdvertiserClassificationType.send(classification_type).id)
        .where('adv_classification.adv_classification_id in (?)', values)
        .select('advertiser_id'))
    end

    base
  end
  # rubocop:enable Layout/LineLength

  def index_data
    # maps salesforce ids to the advertisers.
    # external_key_type_id: 1 denotes 'Advertiser'
    # system_key_asc_type_id: 1,2 denotes 'OUTBOUND' and 'COMBINED' mappings
    add_filters(Advertiser.summaries_by(:advertiser, filter_params))
      .joins("left join (
          #{SystemKeyAssociation.salesforce_mappings(models: %w[advertiser], external_key_type_ids: %w[1]).to_sql}
        ) saleforce_mappings
        on saleforce_mappings.pam_key = advertiser.advertiser_id and system_key_asc_type_id in (1, 2)")
      .select('external_system_key as sfdc_advertiser_id')
  end

  def quintile_chart_data
    PortfolioQuintile
      .from(cy_projections_by_advertiser_agency_mp, :cy_projections)
      .joins("full outer join (#{quintiles_by_advertiser_agency_mp.to_sql}) quintiles on
        quintiles.advertiser_id = cy_projections.advertiser_id
          and quintiles.marketplace_id = cy_projections.marketplace_id
          and quintiles.agency_grandparent_id = cy_projections.grand_parent_agency_id")
      .select('
        nvl(quintiles.quintile, 0) as quintile,
        sum(quintiles.registration) as py_actual,
        sum(cy_projections.projected_amount) as cy_projected')
      .group('nvl(quintiles.quintile, 0)')
  end

  def quintiles_by_advertiser_agency_mp
    base = PortfolioQuintile
           .where(budget_year_id: budget_year.prior_budget_year_id,
                  marketplace_id: default_marketplace_ids)

    add_filters(base)
  end

  def cy_projections_by_advertiser_agency_mp
    base = Deal
           .left_joins(:budgets)
           .joins(agency: [parent_agency: [:grand_parent_agency]])
           .where('budget.budget_year_id = ?', budget_year.id)
           .where(marketplace_id: default_marketplace_ids)
           .group('
        deal.advertiser_id, deal.marketplace_id,
        nvl2(grand_parent_agency_agency.agency_id, parent_agency_agency.agency_id, agency.agency_id)')
           .select('
          deal.advertiser_id,
          deal.marketplace_id,
          nvl2(
            grand_parent_agency_agency.agency_id, parent_agency_agency.agency_id, agency.agency_id
          ) as grand_parent_agency_id,
          sum(
            projected_prequarter_amount +
            projected_quarter1_amount +
            projected_quarter2_amount +
            projected_quarter3_amount +
            projected_quarter4_amount +
            projected_postquarter_amount) as projected_amount')

    add_filters(base)
  end

  def marketplaces_data
    py_actual = budgets_by_marketplace(:actual, budget_year.prior_budget_year_id)
    cy_projected = budgets_by_marketplace(:projected, budget_year.id)
    cy_actual = budgets_by_marketplace(:actual, budget_year.id)

    Marketplace
      .where(marketplace_id: default_marketplace_ids)
      .joins("left join (#{py_actual.to_sql}) py_actual on py_actual.marketplace_id = marketplace.marketplace_id")
      .joins("left join (#{cy_projected.to_sql}) cy_projected on cy_projected.marketplace_id = marketplace.marketplace_id") # rubocop:disable Layout/LineLength
      .joins("left join (#{cy_actual.to_sql}) cy_actual on cy_actual.marketplace_id = marketplace.marketplace_id")
      .select("
        #{advertiser.id} as advertiser_id,
        marketplace.marketplace_id,
        marketplace.marketplace_name,
        nvl(py_actual.actual_amount, 0) as py_actual,
        nvl(cy_projected.projected_amount, 0) as cy_projected,
        nvl(cy_actual.actual_amount, 0) as cy_actual")
  end

  def default_marketplace_ids
    AdvertiserSummary.default_marketplaces.map(&:id)
  end

  def budgets_by_marketplace(amount_type, budget_year_id)
    Budget.totals_by(:marketplace, amount_type, filter_params.merge(
                                                  budget_year_id:,
                                                  advertiser_id: advertiser.id
                                                ))
  end

  # filters to narrow down list of advertisers
  def advertiser_filters
    params.permit(:advertiser_id,
                  :default_category_id,
                  :client_partnership_category_id,
                  :client_partnership_svp_id,
                  :target_classification_id)
  end
end
