# frozen_string_literal: true

# Helper module for parsing Excel uploads (Finance, Portfolio Monetization)
module XlsxUploadHelper
  extend ActiveSupport::Concern

  require 'roo'
  require 'caxlsx_rails'

  included do
    def parse_file(filename)
      file = Roo::Excelx.new(filename)
      sheets = file.sheets
      sheets = yield(sheets) if block_given?

      sheets.map do |sheet_name|
        Rails.logger.info("Parsing #{sheet_name} sheet...")

        sheet_data = file.sheet(sheet_name).parse(headers: true, clean: true).map do |row|
          transform_row(row)
        end

        sheet_data.shift # remove header row

        {
          sheet_name:,
          sheet_headers: sheet_data.first.keys << :errors,
          sheet_data:
        }
      end
    end

    def transform_row(row)
      row.keys.compact.each_with_object({}) do |column, memo|
        memo[column.to_s.downcase.parameterize(separator: '_').to_sym] = row[column]
      end
    end
  end
end
