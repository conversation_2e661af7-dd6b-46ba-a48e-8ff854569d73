# frozen_string_literal: true

module BudgetsControllerHelper
  extend ActiveSupport::Concern

  private

  def budget_with_additional_data(budget_id = params.require(:id))
    ApiBudget.find(budget_id)
             .serializable_hash
             .merge(sales_systems:)
             .merge(splits_and_lock_statuses)
  end

  def budgets_with_additional_data
    budgets.map do |record|
      @budget = record
      budget_with_additional_data(budget.id)
    end
  end

  def splits_and_lock_statuses
    PamClient::SplitterInterface.new(budget, budget.prior_year_budget).get
  end

  def sales_systems
    return unless budget

    external_deals = ExternalDeal
                     .where(budget_year_id: budget.budget_year_id)
                     .where.not(deal_name: 'NULL')
                     .select('distinct sales_system_id, deal_name, budget_year_id')

    BudgetSalesSystem.where(budget_id: budget.id)
                     .joins("left join (#{external_deals.to_sql}) external_deal
        on budget_sales_system.sales_system_id = external_deal.sales_system_id")
                     .select('budget_sales_system.*, external_deal.deal_name')
  end
end
