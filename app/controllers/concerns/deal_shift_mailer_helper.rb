# frozen_string_literal: true

module DealShiftMailerHelper
  private

  def notify_deal_shift_created
    mailer_service.notify_old_agency_shift_created if deal_shift.is_a?(AgencyDealShift)
    mailer_service.notify_requestee_shift_created
  end

  def notify_deal_shift_approved
    mailer_service.notify_old_agency_shift_updated if deal_shift.is_a?(AgencyDealShift)
    mailer_service.notify_requester_shift_approved
  end

  def notify_deal_shift_rejected
    mailer_service.notify_old_agency_shift_updated if deal_shift.is_a?(AgencyDealShift)
    mailer_service.notify_requester_shift_rejected
  end

  def notify_children_shift_created
    deal_shift.children.each do |child|
      Api::DealShiftMailerService.new(deal_shift: child).notify_requestee_shift_created
    end
  end

  def mailer_service
    @mailer_service ||= Api::DealShiftMailerService.new(deal_shift:, current_user: @current_user)
  end
end
