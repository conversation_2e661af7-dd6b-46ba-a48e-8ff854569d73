# frozen_string_literal: true

module Api
  class DealShiftMailerService
    attr_reader :deal_shift, :current_user

    def initialize(deal_shift:, current_user: nil)
      @deal_shift = deal_shift
      @current_user = current_user
    end

    def notify_old_agency_shift_updated
      old_agency_users.each do |user|
        trigger_mailer(:notify_old_agency_shift_updated, user:)
      end
    end

    def notify_old_agency_shift_created
      old_agency_users.each do |user|
        trigger_mailer(:notify_old_agency_shift_created, user:)
      end
    end

    def notify_requestee_shift_created
      requestees.each do |user|
        trigger_mailer(:notify_requestee_shift_created, user:)
      end
    end

    def remind_requestee_shift_created
      requestees.each do |user|
        trigger_mailer(:remind_requestee_shift_created, user:)
      end
    end

    def notify_requester_shift_approved
      trigger_mailer(:notify_requester_shift_approved)
    end

    def notify_requester_shift_rejected
      trigger_mailer(:notify_requester_shift_rejected)
    end

    def notify_requester_shift_auto_rejected
      trigger_mailer(:notify_requester_shift_auto_rejected)
    end

    def notify_missing_portals(error_message:)
      trigger_mailer(:notify_missing_portals, user: current_user, error_message:)
    end

    private

    def requestees
      if %w[Agency Marketplace].include?(deal_shift.shift_type.chomp('DealShift'))
        new_agency_users
      elsif %w[BuyingAe ClientAe BuyingClientAe].include?(deal_shift.shift_type.chomp('DealShift'))
        [deal_shift.shift_to_ae]
      elsif %w[Planner].include?(deal_shift.shift_type.chomp('DealShift'))
        [deal_shift.shift_to_planner]
      end
    end

    def new_agency_users
      agency_family = deal_shift.base_agency.self_and_ancestors
      User.where(app_user_id: portal_user_ids(agency_family))
    end

    def old_agency_users
      base_agency = BaseAgency.find(deal_shift.deal.agency_id)
      agency_family = base_agency.self_and_ancestors
      User.where(app_user_id: portal_user_ids(agency_family))
    end

    def portal_user_ids(agency_family)
      PortalTeam
        .by(agencies: agency_family, budget_year: deal_shift.budget_year)
        .agency_unlock_active_users_with_email
        .select(:app_user_id)
    end

    def trigger_mailer(method, opts = {})
      DealShiftMailer
        .with(deal_shift.deal_shift_id ? opts.merge(deal_shift:) : opts)
        .send(method)
        .deliver_later
    end
  end
end
