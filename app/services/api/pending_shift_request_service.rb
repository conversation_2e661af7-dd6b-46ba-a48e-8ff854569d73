# frozen_string_literal: true

module Api
  class PendingShiftRequestService
    def initialize(user, opts = {})
      @current_user = user
      @shift_request = opts[:shift_request]
      @comment = opts[:comment]
    end

    def show
      aggregated_dataset.all
    end

    def approve
      ActiveRecord::Base.transaction do
        if @current_user.has_entitlement?(:sic_approve_reject)
          @shift_request.shift_request_status = ShiftRequestStatus.sic_approved
        elsif @current_user.account_executive
          @shift_request.shift_request_status = ShiftRequestStatus.ae_approved
        else
          raise Error::ApiError::ForbiddenError, 'User not allowed to approve a shift'
        end

        @shift_request.save!
        ShiftRequestHistory.approve_shift_request!(@shift_request, @current_user, @comment)
        send_status_change_email
      end
    end

    def reject
      ActiveRecord::Base.transaction do
        if @current_user.has_entitlement?(:sic_approve_reject)
          @shift_request.shift_request_status = ShiftRequestStatus.sic_rejected
        elsif @current_user.account_executive
          @shift_request.shift_request_status = ShiftRequestStatus.ae_rejected
        else
          raise Error::ApiError::ForbiddenError, 'User not allowed to reject a shift'
        end

        @shift_request.save!
        ShiftRequestHistory.reject_shift_request!(@shift_request, @current_user, @comment)
      end
    end

    private

    def send_status_change_email
      response = SmsApiClient.redirect('post', "/shift_requests/#{@shift_request.id}/send_status_change_emails")
      raise Error::ApiError::FailedDependencyError, 'Sending email notification failed' unless response.code.to_i == 200
    end

    def aggregated_dataset
      ShiftRequest
        .from(base_dataset)
        .select("
          shift_request_id,
          max(label) label,
          max(property_name) property_name,
          max(agency_name) agency_name,
          max(advertiser_name) advertiser_name,
          max(from_ae) from_ae,
          max(to_ae) to_ae,
          max(from_agency) from_agency,
          max(to_agency) to_agency,
          max(quarter_name) effective,
          max(shift_created_by_name) created_by,
          max(updated_at) updated_at,
          listagg(shift_request_type_name, ', ') within group (order by shift_request_type_name) shift_type")
        .group('shift_request_id')
    end

    def base_dataset
      ShiftRequestBudgetView
        .by_shift_to_ae(@current_user)
        .joins(:shift_request_type, shift_request: [:quarter])
        .left_joins(:from_agency, :to_agency, :from_user, :to_user)
        .where(shift_request_status_id: ShiftRequestStatus.pending_ae_approval.id)
        .select(
          :shift_request_id, :label, :updated_at, :agency_name, :property_name, :quarter_name,
          :advertiser_name, :shift_created_by_name, :shift_request_type_name
        )
        .select("
          app_user.first_name || ' ' || app_user.last_name as from_ae,
          to_user_shift_request_budget_view.first_name || ' ' || to_user_shift_request_budget_view.last_name as to_ae,
          agency.agency_name as from_agency,
          to_agency_shift_request_budget_view.agency_name as to_agency")
        .distinct
    end
  end
end
