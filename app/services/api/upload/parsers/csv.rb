# frozen_string_literal: true

module Api
  module Upload
    module Parsers
      class Csv
        def initialize(file, opts = {})
          @file = file
          @opts = opts
          validate_file
        end

        def parse
          rows = CSV.parse(File.read(@file.path), **@opts.merge(headers: true)).map(&:to_h)
          strip_excel_chars(rows)
        end

        private

        def validate_file
          raise InvalidFileTypeError unless File.extname(@file.path) == '.csv'
        end

        # Strip out FEFF characters sometimes added by Excel when it saves a CSV
        #
        # @see https://www.freecodecamp.org/news/a-quick-tale-about-feff-the-invisible-character-cd25cd4630e7/
        #
        # @param rows [Array<Hash>]
        def strip_excel_chars(rows)
          rows.map do |row|
            row.transform_keys { |k| k.delete("\xEF\xBB\xBF") }
          end
        end
      end
    end
  end

  class InvalidFileTypeError < StandardError; end
end
