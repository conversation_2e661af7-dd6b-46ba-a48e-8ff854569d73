# frozen_string_literal: true

module Api
  module Finance
    module RatingImpressionServices
      class Getter < Api::Finance::Getter
        def initialize(property, finance_month, calendar_year, opts = {})
          super
        end

        private

        def by_year_dataset
          # This query populates data for all the tables whose columns are by year.
          # There's only a few of these under LRP.

          base_dataset
            .where("FINANCE_MODEL_NAME = 'LRP' and FINANCE_MONTH_NAME = 'Full-Year'")
            .where('CALENDAR_YEAR.CALENDAR_YEAR': @calendar_year.calendar_year)
            .where('CALENDAR_YEAR_FINANCE_QUARTER.CALENDAR_YEAR': compact_year_range(@calendar_year))
            .select("
              listagg(FINANCE_RATING_IMPRESSION_ID, ',')
                within group (order by FINANCE_RATING_IMPRESSION_ID) as FINANCE_RATING_IMPRESSION_IDS,
              listagg(FINANCE_QUARTER.FINANCE_QUARTER_ID, ',')
                within group (order by FINANCE_QUARTER.FINANCE_QUARTER_ID) as FINANCE_QUARTER_IDS,
              CALENDAR_YEAR_FINANCE_QUARTER.CALENDAR_YEAR as FQ_CALENDAR_YEAR,
              DEMO_DAYPART_DEFAULT.DEMO_DAYPART_DEFAULT_ID,
              max(FINANCE_RATING_IMPRESSION_VIEW.FINANCE_HEADER_ID) as FINANCE_HEADER_ID,
              max(FINANCE_RATING_IMPRESSION_VIEW.CALENDAR_YEAR_ID) as CALENDAR_YEAR_ID,
              max(FINANCE_RATING_IMPRESSION_VIEW.DAYPART_ID) as DAYPART_ID,
              max(FINANCE_RATING_IMPRESSION_VIEW.DEMOGRAPHIC_ID) as DEMOGRAPHIC_ID,
              max(FINANCE_RATING_IMPRESSION_VIEW.FINANCE_MODEL_ID) as FINANCE_MODEL_ID,
              max(FINANCE_RATING_IMPRESSION_VIEW.FINANCE_MONTH_ID) as FINANCE_MONTH_ID,
              max(FINANCE_RATING_IMPRESSION_VIEW.PROPERTY_ID) as PROPERTY_ID,
              max(FINANCE_MONTH.FINANCE_MONTH_NAME) as FINANCE_MONTH_NAME,
              max(FINANCE_MODEL.FINANCE_MODEL_NAME) as FINANCE_MODEL_NAME,
              max((DEMOGRAPHIC_NAME || ' - ' || DAYPART_NAME)) as DEMO_DAYPART,
              avg(RATING_IMPRESSION) as RATING_IMPRESSION,
              max(LOCKED) as LOCKED
            ")
            .group('CALENDAR_YEAR_FINANCE_QUARTER.CALENDAR_YEAR, DEMO_DAYPART_DEFAULT.DEMO_DAYPART_DEFAULT_ID')
            .order('max(DEMOGRAPHIC.DISPLAY_ORDER), max(DAYPART.DAYPART_NAME), FQ_CALENDAR_YEAR')
        end

        def by_quarter_dataset
          # This query populates data for all the tables whose columns are by quarter.
          # Most tables you see on the UI is of this type.

          base_dataset
            .where("(FINANCE_MODEL_NAME in
                ('Current Estimate', 'Budget') and CALENDAR_YEAR.CALENDAR_YEAR = #{@calendar_year.calendar_year})
              or (FINANCE_MODEL_NAME = 'Actuals' and CALENDAR_YEAR.CALENDAR_YEAR
                   between #{@calendar_year.calendar_year - 3} and #{@calendar_year.calendar_year - 1})
              or (FINANCE_MODEL_NAME = 'LRP'
                and CALENDAR_YEAR.CALENDAR_YEAR = #{@calendar_year.calendar_year}
                and CALENDAR_YEAR_FINANCE_QUARTER.CALENDAR_YEAR
                   between #{@calendar_year.calendar_year} and #{@calendar_year.calendar_year + 1}
            )")
            .select("
              FINANCE_RATING_IMPRESSION_VIEW.*,
              CALENDAR_YEAR_FINANCE_QUARTER.CALENDAR_YEAR as FQ_CALENDAR_YEAR,
              FINANCE_MONTH_NAME,
              FINANCE_MODEL_NAME,
              (DEMOGRAPHIC_NAME || ' - ' || DAYPART_NAME) as DEMO_DAYPART,
              FINANCE_QUARTER_NAME
            ")
            .order('FINANCE_MODEL.DISPLAY_ORDER,
                   DEMOGRAPHIC.DISPLAY_ORDER,
                   DAYPART.DAYPART_NAME,
                   FQ_CALENDAR_YEAR,
                   FINANCE_QUARTER.FINANCE_QUARTER')
        end

        def base_dataset
          ds = FinanceRatingImpressionView
               .joins(:daypart,
                      :demographic,
                      :finance_model,
                      :finance_month,
                      :calendar_year,
                      :demo_daypart_default,
                      finance_quarter: [:calendar_year])
               .where("(FINANCE_MONTH_NAME = '#{@finance_month.name}' and FINANCE_MODEL_NAME = 'Current Estimate')
              or (FINANCE_MONTH_NAME = 'Full-Year' and FINANCE_MODEL_NAME != 'Current Estimate')")
               .where(property_id: @property.id)

          ds = ds.where('FINANCE_MODEL.FINANCE_MODEL_ID = ?', @finance_model_id) if @finance_model_id
          ds
        end

        def unique_additional_fields
          [:demo_daypart]
        end

        def base_model
          @base_model ||= FinanceRatingImpression
        end
      end
    end
  end
end
