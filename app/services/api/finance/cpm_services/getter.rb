# frozen_string_literal: true

module Api
  module Finance
    module CpmServices
      class Getter < Api::Finance::Getter
        def initialize(property, finance_month, calendar_year, opts = {})
          super
        end

        private

        def format_group_by_finance_model(dataset)
          dataset.group_by { |row| row[:finance_model_name] }.map do |finance_model, sub_dataset|
            {
              finance_model_name: finance_model,
              locked: locked?(finance_model),
              data: format_group_by_demo_daypart(sub_dataset, finance_model)
            }
          end
        end

        def format_group_by_demo_daypart(dataset, finance_model)
          dataset.group_by { |row| row[:demo_daypart] }.map do |demo_daypart, sub_dataset|
            dataset = format_group_by_calendar_year(sub_dataset)
            dataset << format_compact(compact_dataset_by_demo_daypart[demo_daypart]) if finance_model == 'LRP'

            {
              demo_daypart:,
              data: dataset
            }
          end
        end

        def compact_dataset_by_demo_daypart
          @compact_dataset_by_demo_daypart ||= to_hash(by_year_dataset).group_by { |row| row[:demo_daypart] }
        end

        def by_year_dataset
          # This query populates data for all the tables whose columns are by year.
          # There's only a few of these under LRP.

          base_dataset
            .where("FINANCE_MODEL_NAME = 'LRP' and FINANCE_MONTH_NAME = 'Full-Year'")
            .where('CALENDAR_YEAR.CALENDAR_YEAR': @calendar_year.calendar_year)
            .where('CALENDAR_YEAR_FINANCE_QUARTER.CALENDAR_YEAR': compact_year_range(@calendar_year))
            .select("
              listagg(FINANCE_CPM_ID, ',') within group (order by FINANCE_CPM_ID) as FINANCE_CPM_IDS,
              listagg(FINANCE_QUARTER.FINANCE_QUARTER_ID, ',')
                 within group (order by FINANCE_QUARTER.FINANCE_QUARTER_ID) as FINANCE_QUARTER_IDS,
              CALENDAR_YEAR_FINANCE_QUARTER.CALENDAR_YEAR as FQ_CALENDAR_YEAR,
              ALLOCATION.ALLOCATION_ID,
              DEMO_DAYPART_DEFAULT.DEMO_DAYPART_DEFAULT_ID,
              max(FINANCE_CPM_VIEW.FINANCE_HEADER_ID) as FINANCE_HEADER_ID,
              max(FINANCE_CPM_VIEW.CALENDAR_YEAR_ID) as CALENDAR_YEAR_ID,
              max(FINANCE_CPM_VIEW.DAYPART_ID) as DAYPART_ID,
              max(FINANCE_CPM_VIEW.DEMOGRAPHIC_ID) as DEMOGRAPHIC_ID,
              max(FINANCE_CPM_VIEW.FINANCE_MODEL_ID) as FINANCE_MODEL_ID,
              max(FINANCE_CPM_VIEW.FINANCE_MONTH_ID) as FINANCE_MONTH_ID,
              max(FINANCE_CPM_VIEW.PROPERTY_ID) as PROPERTY_ID,
              max(FINANCE_MONTH.FINANCE_MONTH_NAME) as FINANCE_MONTH_NAME,
              max(FINANCE_MODEL.FINANCE_MODEL_NAME) as FINANCE_MODEL_NAME,
              max((DEMOGRAPHIC_NAME || ' - ' || DAYPART_NAME)) as DEMO_DAYPART,
              max(ALLOCATION.ALLOCATION_NAME) as ALLOCATION_NAME,
              max(MODEL_ALLOCATION_DESCRIPTION) as MODEL_ALLOCATION_DESCRIPTION,
              max(AlLOCATION.SHOW_DESCRIPTION) as SHOW_DESCRIPTION,
              avg(CPM_DOLLARS) as CPM_DOLLARS,
              max(LOCKED) as LOCKED,
              max(VALUE_TYPE) as VALUE_TYPE
            ")
            .group('CALENDAR_YEAR_FINANCE_QUARTER.CALENDAR_YEAR,
                     ALLOCATION.ALLOCATION_ID,
                    DEMO_DAYPART_DEFAULT.DEMO_DAYPART_DEFAULT_ID')
            .order('max(MODEL_ALLOCATION.DISPLAY_ORDER), FQ_CALENDAR_YEAR')
        end

        def by_quarter_dataset
          # This query populates data for all the tables whose columns are by quarter.
          # Most tables you see on the UI is of this type.

          base_dataset
            .where("(FINANCE_MODEL_NAME in ('Current Estimate', 'Budget')
                     and CALENDAR_YEAR.CALENDAR_YEAR = #{@calendar_year.calendar_year})
              or (FINANCE_MODEL_NAME = 'Actuals'
                 and CALENDAR_YEAR.CALENDAR_YEAR
                 between #{@calendar_year.calendar_year - 3} and #{@calendar_year.calendar_year - 1})
              or (FINANCE_MODEL_NAME = 'LRP'
                and CALENDAR_YEAR.CALENDAR_YEAR = #{@calendar_year.calendar_year}
                and CALENDAR_YEAR_FINANCE_QUARTER.CALENDAR_YEAR
                  between #{@calendar_year.calendar_year} and #{@calendar_year.calendar_year + 1}
            )")
            .select("
              FINANCE_CPM_VIEW.*,
              CALENDAR_YEAR_FINANCE_QUARTER.CALENDAR_YEAR as FQ_CALENDAR_YEAR,
              FINANCE_MONTH_NAME,
              FINANCE_MODEL_NAME,
              (DEMOGRAPHIC_NAME || ' - ' || DAYPART_NAME) as DEMO_DAYPART,
              ALLOCATION_NAME,
              FINANCE_QUARTER_NAME,
              SHOW_DESCRIPTION,
              VALUE_TYPE
            ")
            .order('FINANCE_MODEL.DISPLAY_ORDER,
                    DEMO_DAYPART_DEFAULT.DISPLAY_ORDER,
                    MODEL_ALLOCATION.DISPLAY_ORDER,
                    FQ_CALENDAR_YEAR,
                    FINANCE_QUARTER.FINANCE_QUARTER')
        end

        def base_dataset
          ds = FinanceCpmView
               .joins(:allocation,
                      :model_allocation,
                      :daypart,
                      :demographic,
                      :finance_model,
                      :finance_month,
                      :calendar_year,
                      :demo_daypart_default,
                      finance_quarter: [:calendar_year])
               .where("(FINANCE_MONTH_NAME = '#{@finance_month.name}' and FINANCE_MODEL_NAME = 'Current Estimate')
              or (FINANCE_MONTH_NAME = 'Full-Year' and FINANCE_MODEL_NAME != 'Current Estimate')")
               .where(property_id: @property.id)

          ds = ds.where('FINANCE_MODEL.FINANCE_MODEL_ID = ?', @finance_model_id) if @finance_model_id
          ds
        end

        def base_model
          @base_model ||= FinanceCpm
        end
      end
    end
  end
end
