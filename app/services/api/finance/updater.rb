# frozen_string_literal: true

# Abstract class to be used by individual finance_metric_type's services

module Api
  module Finance
    class Updater
      attr_reader :current_user

      def initialize(user)
        Rails.logger.info("Initializing #{self.class}")
        @current_user = user
      end

      def process(data)
        ActiveRecord::Base.transaction do
          data.each do |record|
            # Endpoint determines whether an update or insert is needed on each individual data row
            update_or_create_by_record(record)
          end
        end
      end

      def editable_attributes
        self.class::EDITABLE_ATTRIBUTES + [:quarter_value]
      end

      def header_composite_pks
        @header_composite_pks ||= "#{(@header_model || FinanceHeader)}View".constantize.primary_keys.map(&:to_sym)
      end

      def base_model_composite_pks
        @base_model_composite_pks ||= "#{base_model.name}View".constantize.primary_keys.map(&:to_sym)
      end

      def base_model_pk
        @base_model_pk ||= base_model.primary_key.to_sym
      end

      def base_model
        raise 'Must be declared by inheriting class'
      end

      private

      def update_or_create_by_record(record)
        if record[base_model_pk] ||= find_existing_by_composite_keys(record)&.id
          update_existing_record(record)
        else
          create_new_record(record)
        end
      end

      def find_existing_by_composite_keys(record)
        if missing_required_fields?(record)
          raise Error::ApiError::BadRequestError.new("Missing #{base_model_pk} or #{base_model.name}'s composite primary keys.", # rubocop:disable Layout/LineLength
                                                     record)
        end

        record[header_model_pk] ||= send(:"find_or_create_#{header_model.model_name.name.demodulize.underscore}",
                                         record).id
        base_model.find_by(record.slice(*find_attributes))
      end

      def create_new_record(record)
        record = record.slice(*create_attributes)
        Rails.logger.info("Creating #{record}")
        base_model.create!(record)
      end

      def update_existing_record(data)
        record = base_model.find(data[base_model_pk])
        raise Error::ApiError::UnprocessableEntityError.new("Bad #{base_model_pk}", data) unless record

        Rails.logger.info("Updating #{data}")
        record.update!(data.slice(*editable_attributes))

        # touch finance_header only for those models that have association with finance_header
        begin
          record.finance_header.touch_with_user(current_user)
        rescue StandardError
          nil
        end
      end

      def find_or_create_finance_header(record, opts = {})
        record = record.merge(opts).except(:finance_header_id) unless opts.empty?

        finance_header =
          if record[:finance_header_id]
            FinanceHeader.find(record[:finance_header_id])
          else
            FinanceHeader.where(
              property_id: record[:property_id],
              finance_month_id: record[:finance_month_id],
              finance_model_id: record[:finance_model_id],
              calendar_year_id: record[:calendar_year_id]
            ).first_or_create!
          end

        finance_header.touch_with_user(current_user)
        finance_header
      end

      def missing_required_fields?(record)
        return false if record[base_model_pk] || (base_model_composite_pks - record.keys).empty?

        true
      end

      def create_attributes
        # attributes required to create a new record
        editable_attributes + find_attributes
      end

      def find_attributes
        # attributes required to find record
        (base_model_composite_pks - header_composite_pks) << header_model_pk
      end

      def header_model_pk
        @header_model_pk ||= header_model.primary_key.to_sym
      end

      def header_model
        @header_model ||= FinanceHeader
      end
    end
  end
end
