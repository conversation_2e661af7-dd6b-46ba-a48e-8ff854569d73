# frozen_string_literal: true

module Api
  module Finance
    module RevenueVarianceServices
      # FinanceRevenueVariance is where AEs provide explanation to differences
      # between 2 previously declared projections.

      # For example, 2018 revenue projection done in May 2018 is different from
      # a newer projection done in Aug 2018 for property A. These 2 projections data
      # are displayed in the Summary section of the page.

      # An AE will then fill out the explanation for the discrepancies in different
      # breakdowns: Price, Performance & Volume, as seen in the Details section.

      # With that said, Summary & Detail data are completely independent from one another,
      # therefore fetched by 2 different services: DetailGetter && SummaryGetter
      # before being combined as the endpoint's response

      class Getter < Api::Finance::Getter
        def initialize(property, finance_month, calendar_year)
          super
          @details = DetailGetter.new(@property, @finance_month, @calendar_year).process
          @summaries = SummaryGetter.new(@property, @finance_month, @calendar_year).process
        end

        def process
          format_response
        end

        private

        def format_response
          {
            property_id: property.id,
            property_name: property.name,
            finance_month_id: finance_month.id,
            finance_month_name: finance_month.finance_month_name,
            calendar_year_id: calendar_year.id,
            calendar_year: calendar_year.calendar_year,
            data: combine_summaries_and_details
          }
        end

        def combine_summaries_and_details
          @details.map do |detail|
            {
              summary: {
                from: get_summary(detail[:revenue_variance_mapping_id], :from),
                to: get_summary(detail[:revenue_variance_mapping_id], :to)
              },
              details: detail
            }
          end
        end

        def get_summary(revenue_variance_mapping_id, from_or_to)
          @summaries[from_or_to].find { |summary| summary[:revenue_variance_mapping_id] == revenue_variance_mapping_id }
        end
      end
    end
  end
end
