# frozen_string_literal: true

module Api
  module Finance
    module RevenueVarianceServices
      class Updater < Api::Finance::Updater
        EDITABLE_ATTRIBUTES = %i[variance_dollars model_allocation_description].freeze

        def initialize(user)
          super
          @header_model = FinanceVarianceHeader
          @base_model_composite_pks = base_model_composite_pks - [:revenue_variance_mapping_id]
        end

        def base_model
          @base_model ||= FinanceVarianceDetail
        end

        private

        def find_or_create_finance_variance_header(record)
          FinanceVarianceHeader.where(
            from_finance_header_id: record[:from_finance_header_id] || find_or_create_from_to_finance_header(record,
                                                                                                             :from).id,
            to_finance_header_id: record[:to_finance_header_id] || find_or_create_from_to_finance_header(record, :to).id
          ).first_or_create!
        end

        def find_or_create_from_to_finance_header(record, from_or_to)
          record = record.merge(
            property_id: record[:"#{from_or_to}_property_id"],
            calendar_year_id: record[:"#{from_or_to}_calendar_year_id"],
            finance_month_id: record[:"#{from_or_to}_finance_month_id"],
            finance_model_id: record[:"#{from_or_to}_finance_model_id"]
          )

          find_or_create_finance_header(record)
        end

        def header_model
          @header_model ||= FinanceVarianceHeader
        end
      end
    end
  end
end
