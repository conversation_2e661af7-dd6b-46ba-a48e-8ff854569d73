# frozen_string_literal: true

module Api
  module Finance
    module RevenueVarianceServices
      class DetailGetter < Api::Finance::Get<PERSON>
        def process
          format_group_by_variance_mapping(to_hash(base_dataset))
        end

        private

        def format_group_by_variance_mapping(dataset)
          dataset.group_by { |row| row[:revenue_variance_mapping_id] }.map do |_variance_mapping_id, sub_dataset|
            sub_dataset.first
                       .slice(:revenue_variance_mapping_id)
                       .merge(data: format_group_by_variance_type(sub_dataset))
          end
        end

        def format_group_by_variance_type(dataset)
          dataset.group_by { |row| row[:variance_type_name] }.map do |variance_type, sub_dataset|
            {
              variance_type_name: variance_type,
              data: format_group_by_quarter(sub_dataset)
            }
          end
        end

        def format_column(dataset)
          returned_fields = FinanceVarianceDetailView.columns.map(&:name).map(&:to_sym) + %i[
            variance_type_allocation_name show_description
          ] - [:active]
          dataset.map do |row|
            row
              .slice(*returned_fields)
              .merge(from_finance_header_id:
                       row[:from_finance_header_id].present? ? row[:from_finance_header_id].to_i : nil,
                     to_finance_header_id:
                        row[:to_finance_header_id].present? ? row[:to_finance_header_id].to_i : nil)
          end
        end

        def base_dataset
          FinanceVarianceDetailView
            .joins(:revenue_variance_mapping, :finance_quarter, variance_type_allocation: [:variance_type])
            .where('ASSOCIATED_YEAR_ID = ? and ASSOCIATED_MONTH_ID = ? and FROM_PROPERTY_ID = ? and TO_PROPERTY_ID = ?',
                   @calendar_year.id, @finance_month.id, @property.id, @property.id)
            .select("
              FINANCE_VARIANCE_DETAIL_VIEW.*,
              FINANCE_QUARTER.FINANCE_QUARTER_NAME,
              VARIANCE_TYPE.VARIANCE_TYPE_NAME,
              VARIANCE_TYPE_ALLOCATION.VARIANCE_TYPE_ALLOCATION_NAME,
              VARIANCE_TYPE_ALLOCATION.SHOW_DESCRIPTION
            ")
            .order('REVENUE_VARIANCE_MAPPING.DISPLAY_ORDER,
                   VARIANCE_TYPE.DISPLAY_ORDER,
                   VARIANCE_TYPE_ALLOCATION.DISPLAY_ORDER,
                   FINANCE_QUARTER.FINANCE_QUARTER')
        end
      end
    end
  end
end
