# frozen_string_literal: true

module Api
  module Finance
    module RevenueVarianceServices
      class SummaryGetter < Api::Finance::Getter
        def process
          {
            from: format_group_by_mapping(to_hash(summary_dataset('from'))),
            to: format_group_by_mapping(to_hash(summary_dataset('to')))
          }
        end

        private

        def format_group_by_mapping(dataset)
          dataset.group_by do |row|
            row[:revenue_variance_mapping_id]
          end.map do |_revenue_variance_mapping_id, sub_dataset|
            sub_dataset.first
                       .slice(:revenue_variance_mapping_id, :year, :finance_month_name, :finance_model_name)
                       .merge(data: format_column(sub_dataset))
          end
        end

        def format_column(dataset)
          dataset.map do |row|
            # because net_dollars is a calculated field, depending on whether the
            # calculated result is a decimal or whole number,
            # Rails json returns it as a string or integer, causing difficulties for FE to parse.
            # Therefore forcing formatting results as string to ensure consistency.
            row[:net_dollars] = row[:net_dollars].to_s
            row.slice(:finance_quarter_name, :net_dollars)
          end
        end

        # rubocop:disable Layout/LineLength
        def summary_dataset(from_or_to)
          FinanceRevenueView
            .joins(:calendar_year, :finance_model, :finance_month, finance_quarter: [:calendar_year])
            .joins("join REVENUE_VARIANCE_MAPPING on
              REVENUE_VARIANCE_MAPPING.#{from_or_to}_YEAR_ID = FINANCE_REVENUE_VIEW.CALENDAR_YEAR_ID and
              REVENUE_VARIANCE_MAPPING.#{from_or_to}_FINANCE_MONTH_ID = FINANCE_REVENUE_VIEW.FINANCE_MONTH_ID and
              REVENUE_VARIANCE_MAPPING.#{from_or_to}_MODEL_ID = FINANCE_REVENUE_VIEW.FINANCE_MODEL_ID and
              REVENUE_VARIANCE_MAPPING.ASSOCIATED_YEAR_ID = #{@calendar_year.id} and
              REVENUE_VARIANCE_MAPPING.ASSOCIATED_MONTH_ID = #{@finance_month.id}
            ")
            .joins("
              join (SELECT fq1.*, fq2.FINANCE_QUARTER_ID as NEXT_FINANCE_QUARTER_ID FROM (SELECT FINANCE_QUARTER.*, row_number()
                    over (partition by null order by QUARTER_ID)
                    as ROW_NUMBER FROM FINANCE_QUARTER WHERE (finance_quarter.finance_quarter_id > -1)) fq1 join (SELECT FINANCE_QUARTER.*, row_number()
                    over (partition by null order by QUARTER_ID)
                    as ROW_NUMBER FROM FINANCE_QUARTER WHERE (finance_quarter.finance_quarter_id > -1)) fq2
               on fq1.ROW_NUMBER = fq2.ROW_NUMBER - 1) FQ on FINANCE_REVENUE_VIEW.FINANCE_QUARTER_ID = FQ.FINANCE_QUARTER_ID
              join FINANCE_REVENUE_VIEW POST_SWING_REV on FINANCE_REVENUE_VIEW.PROPERTY_ID = POST_SWING_REV.PROPERTY_ID
                and FINANCE_REVENUE_VIEW.FINANCE_MODEL_ID = POST_SWING_REV.FINANCE_MODEL_ID
                and FINANCE_REVENUE_VIEW.CALENDAR_YEAR_ID = POST_SWING_REV.CALENDAR_YEAR_ID
                and FINANCE_REVENUE_VIEW.FINANCE_MONTH_ID = POST_SWING_REV.FINANCE_MONTH_ID
                and FINANCE_REVENUE_VIEW.ALLOCATION_ID = POST_SWING_REV.ALLOCATION_ID
                and FINANCE_REVENUE_VIEW.FINANCE_REVENUE_TYPE_ID = POST_SWING_REV.FINANCE_REVENUE_TYPE_ID
                and FQ.NEXT_FINANCE_QUARTER_ID = POST_SWING_REV.FINANCE_QUARTER_ID
            ")
            .where('FINANCE_REVENUE_VIEW.PROPERTY_ID': @property.id)
            .where("
              FINANCE_MODEL_NAME != 'LRP' or
              (FINANCE_MODEL_NAME = 'LRP' and (
                (coalesce(LRP_#{from_or_to}_YEAR, 'LRP 1st YEAR') = 'LRP 1st YEAR' and CALENDAR_YEAR_FINANCE_QUARTER.CALENDAR_YEAR = CALENDAR_YEAR.CALENDAR_YEAR) or
                (coalesce(LRP_#{from_or_to}_YEAR, 'LRP 1st YEAR') = 'LRP 2nd YEAR' and CALENDAR_YEAR_FINANCE_QUARTER.CALENDAR_YEAR = CALENDAR_YEAR.CALENDAR_YEAR + 1)
              ))
            ")
            .select('
              REVENUE_VARIANCE_MAPPING_ID,
              FINANCE_QUARTER.FINANCE_QUARTER_ID,
              max(CALENDAR_YEAR_FINANCE_QUARTER.CALENDAR_YEAR) as YEAR,
              max(FINANCE_MONTH_NAME) as FINANCE_MONTH_NAME,
              max(FINANCE_QUARTER.FINANCE_QUARTER_NAME) as FINANCE_QUARTER_NAME,
              max(FINANCE_MODEL_NAME) as FINANCE_MODEL_NAME,
              coalesce(sum((FINANCE_REVENUE_VIEW.QUARTER_DOLLARS - FINANCE_REVENUE_VIEW.SWING_DOLLARS + POST_SWING_REV.SWING_DOLLARS) * 0.85), 0) as NET_DOLLARS
            ') # to calculate a quarter's net dollars: (quarter_dollars - pre_swing_quarter_dollars + post_swing_quarter_dollars ) * 85%
            .group('REVENUE_VARIANCE_MAPPING_ID, FINANCE_QUARTER.FINANCE_QUARTER_ID')
            .order('YEAR, max(FINANCE_QUARTER.FINANCE_QUARTER)')
        end
        # rubocop:enable Layout/LineLength
      end
    end
  end
end
