# frozen_string_literal: true

module Api
  module Finance
    module UploadServices
      module UploaderHelper
        include Validations

        def format_row_for_update(row)
          # removing empty cells from row to not confuse with update to nil
          row.delete_if { |_k, v| v.nil? || v == '' }
        end

        def updater_by_metric_type(row)
          validate_value_by_column(row, :finance_metric_type)

          # initializing an updater instance for each finance_metric_type and fetch accordingly when needed
          finance_metric_type = row[:finance_metric_type].parameterize(separator: '_').sub('_and', '')
          updater_name = "@#{finance_metric_type}_updater"
          unless instance_variable_defined?(updater_name)
            instance_variable_set(updater_name,
                                  "Api::Finance::#{finance_metric_type.classify}Services::Updater"
                                    .constantize
                                    .new(@user))
          end
          instance_variable_get(updater_name)
        rescue NameError => e
          Rails.logger.debug(e)
          raise Api::Finance::UploadValidationHelper::InvalidValueError,
                "#{finance_metric_type} is not a valid finance_metric_type"
        end

        def revenue_variance_metric_type
          @revenue_variance_metric_type ||= FinanceMetricType.find_by_name('Revenue Variance')
        end

        def validation_columns(updater)
          {
            value_columns: value_columns_by_metric_type(updater),
            attribute_columns: attribute_columns_by_metric_type(updater)
          }
        end

        def value_columns_by_metric_type(updater)
          # data columns such as cpm_dollars, unit, etc...
          updater.editable_attributes.map(&:to_sym)
        end

        def attribute_columns_by_metric_type(updater)
          # validate all columns required to locate a finance record by metric_type.
          columns = updater.base_model_composite_pks.map { |id| id.to_s.gsub('_id', '').to_sym }
          columns << :variance_type if updater.instance_of?(Api::Finance::RevenueVarianceServices::Updater)
          columns
        end

        def nothing_to_update?(row, updater)
          validation_columns(updater)[:value_columns].each do |column|
            return false unless row[column].to_s.empty?
          end

          true
        end

        def deep_copy(object)
          # like .clone but allowing independent mutation of cloned object
          Marshal.load(Marshal.dump(object))
        end
      end
    end
  end
end
