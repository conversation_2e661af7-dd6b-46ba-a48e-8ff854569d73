# frozen_string_literal: true

# This is a service to validate and execute updates of finance data
# requested by user typically in form of a bulk upload.
# For each and every single row, the service will first perform
# various validation steps checking to see if user-provided values
# are valid and sufficient in order to narrow down the exact finance record.
# If yes, it will then attempt to actually update said record,
# which might throw further ActiveRecord::ValidationError(s).
# The service will roll back all transactions if an error occurs at any point
# since the users would want to reupload the exact same file
# once they correct all the reported errors.

# In terms of implementation, since users wanted to have all/any finance_metric_types
# on the same excel sheet and in no particular order (aka they can change it however
# they want), combining with the fact each finance_metric_type require a slightly
# different set of validations + updating procedure (because the required fields are
# different), uploader has to perform both validation + updating on a per-row basis by
# first identifying that row's finance_metric_type then invoking the corresponding
# validations + updater. Speaking of updater, uploader utilizes existing updaters
# for each finance_metric_type (that were initially created for their individual endpoint)
# by simply adding some additional fields required by each updater to each row before
# passing it on for update.

module Api
  module Finance
    module UploadServices
      class Uploader
        include UploaderHelper

        attr_reader :status, :result

        def initialize(user, calendar_year, finance_month)
          Rails.logger.info("Initializing #{self.class}")
          @user = user
          @calendar_year = calendar_year
          @finance_month = finance_month
        end

        def process_file(data)
          @status = :ok
          @result = []

          ActiveRecord::Base.transaction do
            data.compact.each do |sheet|
              process_sheet(sheet)
            end

            raise ActiveRecord::Rollback if @status == :bad_request
          end

          self
        end

        private

        def process_sheet(data)
          data[:sheet_data].each_with_index do |row, index|
            Rails.logger.info('*' * 100)
            Rails.logger.info("Begin processing row #{index + 1}: #{row}")
            process_row(row)
          rescue UploadServices::Errors::ValidationError,
                 ActiveRecord::ActiveRecordError,
                 Error::ApiError::UnprocessableEntityError => e
            Rails.logger.info(e.message)
            (row[:errors] << e.message) && (row[:error_types] << e.class.name.demodulize) && next
          ensure
            if row[:errors].empty?
              row[:errors] = nil
            else
              row[:errors] = row[:errors].join(', ').gsub('Validation failed: ', '')
              @status = :bad_request
            end
          end

          @result << data
        end

        def process_row(row)
          # adding dropdown values (finance_month, calendar_year) to each row
          row.merge!(
            errors: [],
            error_types: [],
            finance_month_id: @finance_month.id,
            calendar_year_id: @calendar_year.id,
            finance_month: @finance_month.name,
            calendar_year: @calendar_year.calendar_year
          )

          updater = updater_by_metric_type(row)
          Rails.logger.info('Nothing to update. Skipping row...') && return if nothing_to_update?(row, updater)

          validate_row(row, validation_columns(updater))
          update_row(row, updater)
        end

        def update_row(row, updater)
          return unless row[:errors].empty?

          Rails.logger.info('Begin updating...')
          updater.process([format_row_for_update(row.clone)])
        end

        def validate_row(row, validation_columns)
          Rails.logger.info("Begin validating columns: #{validation_columns.values.join(', ')} " \
                            "for finance_metric_type: #{row[:finance_metric_type]}")
          # all finance_metrics except for revenue_variance have the same data structure
          # (finance_header's columns + some metric-specific columns)
          # therefore undergo the same steps of validation while revenue_variance needs to be handled separately
          if row[:finance_metric_type_id] == revenue_variance_metric_type.id
            validate_variance_row(row, validation_columns)
          else
            validate_non_variance_row(row, validation_columns)
          end
        end
      end
    end
  end
end
