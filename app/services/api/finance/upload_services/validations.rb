# frozen_string_literal: true

module Api
  module Finance
    module UploadServices
      module Validations
        include Errors
        include ValidationsHelper

        # All required columns go through 2 validations: 1) not missing 2) valid aka exists in DB.
        # A select few also require further validations as listed below:
        ADDITIONAL_VALIDATIONS_BY_COLUMN = { property: %i[finance_property user_assignment],
                                             finance_model: [:not_locked_model],
                                             finance_month: [:valid_month_by_model],
                                             calendar_year: [:finance_calendar_year],
                                             finance_quarter: [:valid_quarter_by_metric_and_model] }.freeze

        # master validator all finance_metric_types except for revenue_variance
        def validate_non_variance_row(row, validation_columns)
          %i[property finance_model].each { |column| validate_value_by_column(row, column) }
          validate_attribute_columns(row, validation_columns[:attribute_columns])
          validate_value_columns(row, validation_columns[:value_columns])
        end

        # master validator exclusively for finance_metric_type revenue_variance
        def validate_variance_row(row, validation_columns)
          validate_exists(row, :from)
          validate_exists(row, :to)
          format_variance_row(row)
          validate_attribute_columns(row, validation_columns[:attribute_columns])
          validate_valid_variance_mapping(row)
          validate_value_columns(row, validation_columns[:value_columns])
        end

        # validate each column required to identify the record by finance_metric_type
        def validate_attribute_columns(row, validation_columns)
          validation_columns.each do |column|
            validate_value_by_column(row, column)
          rescue ValidationError => e
            (row[:errors] << e.message) && (row[:error_types] << e.class.name.demodulize)
          end
        end

        # validate each value column (dollars, units, etc...) by finance_metric_type
        def validate_value_columns(row, validation_columns)
          validation_columns.each do |column|
            validate_value_format(row, column)
          rescue ValidationError => e
            (row[:errors] << e.message) && (row[:error_types] << e.class.name.demodulize)
          end
        end

        # validating value columns' format to be numeric or decimal
        def validate_value_format(row, column)
          return if column == :model_allocation_description || !row[column] || row[column] == '' || float?(row[column])

          raise InvalidFormatError, "#{row[column]} is not a valid format for #{column}"
        end

        # validate
        # 1) if user provided value
        # 2) if value is valid
        # 3) any additional validations required for that particular column
        def validate_value_by_column(row, column)
          # Revenue Variance only, ie: from_finance_model, to_finance_model
          prefix = (from_or_to = /^(from_|to_)/.match(column.to_s)) ? from_or_to[0] : nil
          stripped_column = prefix ? column.to_s.sub(prefix, '').to_sym : column # stripping from/to from column names

          unless row[:"#{column}_id"]
            validate_exists(row, column)
            if respond_to?(:"validate_#{column}")
              send(:"validate_#{column}",
                   row)
            else
              validate_valid(row, column, stripped_column)
            end
          end

          return unless row[:"#{column}_id"]

          ADDITIONAL_VALIDATIONS_BY_COLUMN[stripped_column]&.each do |validation|
            send(:"validate_#{validation}", row, column:, prefix:)
          end
        end

        # validate the from_to comparison exists for that calendar_year & finance_month
        def validate_valid_variance_mapping(row)
          return unless row[:errors].empty?
          return if revenue_variance_mapper.any? do |mapping|
            mapping.slice(:from_model_id, :from_year_id, :from_finance_month_id, :to_model_id, :to_year_id,
                          :to_finance_month_id).values ==
            row.slice(:from_finance_model_id, :from_calendar_year_id, :from_finance_month_id, :to_finance_model_id,
                      :to_calendar_year_id, :to_finance_month_id).values
          end

          raise InvalidVarianceMappingError,
                "Comparison mapping between #{row[:from_calendar_year]} " \
                "#{row[:from_finance_month]} #{row[:from_finance_model]} " \
                "and #{row[:to_calendar_year]} #{row[:to_finance_month]} #{row[:to_finance_model]} " \
                "does not exist for #{row[:calendar_year]} #{row[:finance_month]}"
        end

        # overriding validate_value_by_column(row, :variance_type_allocation) because
        # variance_type_allocation.name isn't unique without variance_type_id
        # (even though it's not a required field by revenue_variance) therefore need custom validator
        def validate_variance_type_allocation(row)
          %i[variance_type variance_type_allocation].each { |column| validate_exists(row, column) }

          variance_type_allocation = variance_type_allocation_mapper.find do |allocation|
            allocation[:variance_type_name] == (row[:variance_type] || row[:variance_type].titleize) &&
              allocation[:variance_type_allocation_name] == (row[:variance_type_allocation] ||
                                                              row[:variance_type_allocation].titleize)
          end

          if variance_type_allocation
            row.merge!(
              variance_type_allocation_id: variance_type_allocation[:variance_type_allocation_id]
            ) && return
          end

          raise InvalidValueError,
                "#{row[:variance_type_allocation]} is not a valid variance_type_allocation of #{row[:variance_type]}"
        end

        # validate property.include_finance_model == true
        def validate_finance_property(row, **opts)
          column = opts[:column] || :property
          return if check_valid_by_include(row[column], finance_properties)

          raise NotFinancePropertyError, "#{row[column]} is not a finance property"
        end

        # validate user is authorized to make changes to said property
        def validate_user_assignment(row, **opts)
          column = opts[:column] || :property
          return if check_valid_by_include(row[column], assigned_properties)

          raise UserUnauthorizedError, 'user not authorized'
        end

        # validate finance_model != 'Actuals' because Actuals data is updated by
        # updating that year's historical month's Current Estimate
        def validate_not_locked_model(row, **_opts)
          return if row[:finance_metric_type_id] == revenue_variance_metric_type.id || row[:finance_model] != 'Actuals'

          raise LockedFinanceModelError, "finance_model Actuals isn't eligible for upload"
        end

        # validate calendar_year.show_in_finance == true
        def validate_finance_calendar_year(row, **opts)
          column = opts[:column] || :calendar_year
          return if check_valid_by_include(row[column].to_i, finance_calendar_years)

          raise NotFinanceCalendarYearError, "#{row[column]} is not a finance calendar_year"
        end

        # validate if finance_month is valid by finance_model:
        # Budget, LRP, Actuals models correspond to only Full-Year finance_month
        # Current Estimate corresponds to everything BUT Full-Year
        def validate_valid_month_by_model(row, **opts)
          finance_month_column = :"#{opts[:prefix]}finance_month"
          finance_model_column = :"#{opts[:prefix]}finance_model"
          return unless row[finance_month_column] && row[finance_model_column]
          return if valid_month_by_model?(row, finance_month_column, finance_model_column)

          raise InvalidFinanceMonthModelError,
                "#{row[finance_month_column]} is not a valid month for #{row[finance_model_column]}"
        end

        # validate finance_quarter to be valid by finance_metric_type and finance
        # ie: Q1-19 can only pair with 2019, SSN quarters can only pair with Unit
        def validate_valid_quarter_by_metric_and_model(row, *)
          valid_quarters = valid_quarters_by_year_mapper.map do |mapping|
            mapping[:finance_quarter_name] if
              mapping[:finance_metric_type_id] == row[:finance_metric_type_id] &&
              (mapping[:finance_model_id] == row[:finance_model_id] ||
                 row[:finance_metric_type_id] == revenue_variance_metric_type.id)
          end

          return if check_valid_by_include(row[:finance_quarter], valid_quarters)

          raise InvalidQuarterByMetricAndModelError,
                "#{row[:finance_quarter]} is not a valid quarter for " \
                "#{row[:calendar_year]} #{row[:finance_metric_type]} #{row[:finance_model]}"
        end
      end
    end
  end
end
