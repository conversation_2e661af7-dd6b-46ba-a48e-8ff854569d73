# frozen_string_literal: true

module Api
  module Finance
    module UploadServices
      module Errors
        class ValidationError < StandardError; end

        class LockedFinanceModelError < ValidationError; end
        class InvalidFinanceMonthModelError < ValidationError; end
        class InvalidFormatError < ValidationError; end
        class InvalidQuarterByMetricAndModelError < ValidationError; end
        class InvalidValueError < ValidationError; end
        class InvalidVarianceMappingError < ValidationError; end
        class MissingRequiredValueError < ValidationError; end
        class NotFinanceCalendarYearError < ValidationError; end
        class NotFinancePropertyError < ValidationError; end
        class UserUnauthorizedError < ValidationError; end
      end
    end
  end
end
