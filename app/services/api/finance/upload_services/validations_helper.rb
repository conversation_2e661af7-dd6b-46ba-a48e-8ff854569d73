# frozen_string_literal: true

module Api
  module Finance
    module UploadServices
      # Collection of value mappings saved in memory to assist in fast validations in validations_helper.rb
      module ValidationsHelper
        include Errors

        def valid_quarters_by_year_mapper
          @valid_quarters_by_year_mapper ||= FinanceQuarterMappingView
                                             .where(calendar_year: @calendar_year)
                                             .map { |record| record.attributes.symbolize_keys }
        end

        def revenue_variance_mapper
          @revenue_variance_mapper ||= RevenueVarianceMapping
                                       .where(associated_year_id: @calendar_year.id,
                                              associated_month_id: @finance_month.id)
                                       .map { |record| record.attributes.symbolize_keys }
        end

        def variance_type_allocation_mapper
          @variance_type_allocation_mapper ||= VarianceTypeAllocation
                                               .joins(:variance_type)
                                               .select(
                                                 'variance_type_allocation.variance_type_allocation_id',
                                                 'variance_type_allocation.variance_type_allocation_name',
                                                 'variance_type.variance_type_id',
                                                 'variance_type.variance_type_name'
                                               )
                                               .map(&:serializable_hash).map(&:symbolize_keys)
        end

        def finance_properties
          @finance_properties ||= Property.where(include_finance_model: true).pluck(:property_name)
        end

        def finance_calendar_years
          @finance_calendar_years ||= CalendarYear.where(show_in_finance: true).pluck(:calendar_year)
        end

        def assigned_properties
          @assigned_properties ||= Property.for_finance_user(@user).pluck(:property_name)
        end

        # validate if user provided required value
        def validate_exists(row, column)
          return if row[column]

          raise MissingRequiredValueError, "missing required #{column}"
        end

        # validate if the value exists in DB
        def validate_valid(row, column, stripped_column = nil)
          # finance_model (stripped), from_finance_model, to_finance_model all use the same finance_model_mapper
          mapper = mapper_by_model(stripped_column || column)
          id = mapper[row[column]] || mapper[row[column].to_s.titleize]

          # adding corresponding id to each required attribute
          # to be passed onto updater service later
          row.merge!("#{column}_id": id) && return if id

          raise InvalidValueError, "#{row[column]} is not a valid #{column}"
        end

        def valid_month_by_model?(row, finance_month_column, finance_model_column)
          (row[finance_month_column] != 'Full-Year' && row[finance_model_column] == 'Current Estimate') ||
            (row[finance_month_column] == 'Full-Year' && row[finance_model_column] != 'Current Estimate')
        end

        def format_variance_row(row)
          row.merge!(associated_year_id: row[:calendar_year_id], associated_month_id: row[:finance_month_id],
                     from_property: row[:property], to_property: row[:property])

          row[:from_calendar_year], row[:from_finance_model], row[:from_finance_month] = *row[:from]&.split('_')
          row[:to_calendar_year], row[:to_finance_model], row[:to_finance_month] = *row[:to]&.split('_')
        end

        # Constructing a name->id mapping for a table and saving it in memory to allow
        # faster fetching than individual `where(name: 'XXX') queries for each row
        # ie: construct_mapper(:finance_month)
        def mapper_by_model(model)
          mapper_name = "@#{model}_mapper"
          instance_variable_set(mapper_name, construct_mapper(model)) unless instance_variable_defined?(mapper_name)
          instance_variable_get(mapper_name)
        end

        def check_valid_by_include(value, valid_list)
          valid_list.include?(value) || valid_list.include?(value.to_s.titleize)
        end

        def construct_mapper(model)
          mapper = {}

          model.to_s.classify.constantize.all.map do |record|
            # all required models have .name attr except for calendar_year
            mapper[record.name || record.calendar_year] = record.id.to_i
          end

          mapper
        end

        def float?(string)
          true if Float(string)
        rescue ArgumentError
          false
        end
      end
    end
  end
end
