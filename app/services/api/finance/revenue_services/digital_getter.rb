# frozen_string_literal: true

module Api
  module Finance
    module RevenueServices
      class DigitalGetter < Api::Finance::RevenueServices::Get<PERSON>
        def initialize(property, finance_month, calendar_year, opts = {})
          super
        end

        private

        def format_group_by_calendar_year(dataset, finance_model)
          group_by = calendar_year_type_by_finance_model(finance_model)
          dataset = dataset.group_by { |row| row[group_by] }.map do |calendar_year, sub_dataset|
            {
              calendar_year:,
              column_type: :quarter,
              data: format_group_by_finance_revenue_type(sub_dataset, :finance_quarter_name)
            }
          end

          return dataset if finance_model != 'LRP'

          dataset.pop
          dataset << format_compact
        end

        def format_group_by_finance_revenue_type(dataset, column_type)
          dataset.group_by { |row| row[:finance_revenue_type_name] }.map do |finance_revenue_type, sub_dataset|
            {
              finance_revenue_type_name: finance_revenue_type,
              data: format_group_by_column(sub_dataset, column_type)
            }
          end
        end

        def format_group_by_column(dataset, group_by)
          dataset.group_by { |row| row[group_by] }.map do |column, sub_dataset|
            {
              (%i[fq_calendar_year fh_calendar_year].include?(group_by) ? :calendar_year : group_by) => column,
              data: format_column(sub_dataset)
            }
          end
        end

        def format_compact
          {
            calendar_year: "#{@calendar_year.calendar_year + 2} - #{@calendar_year.calendar_year + 4}",
            column_type: :year,
            data: format_group_by_finance_revenue_type(to_hash(by_year_dataset), :fq_calendar_year)
          }
        end

        def property_type
          'Digital'
        end
      end
    end
  end
end
