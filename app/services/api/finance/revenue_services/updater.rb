# frozen_string_literal: true

module Api
  module Finance
    module RevenueServices
      class Updater < Api::Finance::Updater
        EDITABLE_ATTRIBUTES = %i[swing_dollars quarter_dollars model_allocation_description].freeze

        def initialize(user)
          super
          @linear_revenue_type = FinanceRevenueType.find_by_name('Linear')
        end

        def base_model
          @base_model ||= FinanceRevenue
        end

        private

        def create_new_record(record)
          record = record.slice(*create_attributes)
          if record[:finance_revenue_type_id] == @linear_revenue_type.id
            record[:post_swing_quarter_id] = FinanceQuarter
                                             .find(record[:finance_quarter_id])
                                             .post_swing_quarter&.id || -1
          end

          Rails.logger.info("Creating #{record}")
          base_model.create!(record)
        end
      end
    end
  end
end
