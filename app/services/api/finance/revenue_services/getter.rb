# frozen_string_literal: true

module Api
  module Finance
    module RevenueServices
      # The schema structure of finance tables make it extremely difficult for FE to
      # digest and display the data as-is in the layout business wants.
      # Therefore the primary goal of this service is to get all the needed data in minimum number of queries,
      # then manipulate the results into a highly customized format that FE can consume most easily.

      class Getter < Api::Finance::Getter
        def initialize(property, finance_month, calendar_year, opts = {})
          super
        end

        private

        def format_response
          # The basic idea is to group the raw data by finance_model and calendar_year
          # with a few caveats along the way such as splitting quarter data into swing_quarter and non-swing_quarter
          # and adding an additional swing_quarter to each year.
          # The exact final format can be found here:
          # https://github.com/CINBCUniversal/sms/wiki/Finance-API-Payload-Structure
          {
            property_id: @property.id,
            property_name: @property.name,
            finance_month_id: @finance_month.id,
            finance_month_name: @finance_month.finance_month_name,
            calendar_year_id: @calendar_year.id,
            calendar_year: @calendar_year.calendar_year,
            property_type_name: property_type,
            data: format_group_by_finance_model(to_hash(by_quarter_dataset))
          }
        end

        def format_group_by_finance_model(dataset)
          dataset.group_by { |row| row[:finance_model_name] }.map do |finance_model, sub_dataset|
            {
              finance_model_name: finance_model,
              locked: locked?(finance_model),
              data: format_group_by_calendar_year(sub_dataset, finance_model)
            }
          end
        end

        # rubocop:disable Layout/LineLength
        # A lot of the data logic will be incorporated directly into the main query to avoid subsequent DB calls and/or excessive processing
        # 1. finance_month:
        #   1.1 Current Estimate is the only model that uses monthly month (Jan, Feb, Mar, etc...)
        #   1.2 Others use Full-Year
        # 2. date_range filter:
        #   2.1 Current Estimate & Budget models only need current_year
        #   2.2 LRP needs current_year && the following 3 years
        #   2.3 Actuals/Historical last 3 years
        # 3. calendar_year:
        #   3.1 finance_revenue.finance_header.calendar_year != finance_revenue.finance_quarter.calendar_year
        #   3.2 LRP partitions by finance_revenue.finance_quarter.calendar_year
        #   3.3 Others partition by finance_revenue.finance_header.calendar_year
        #   3.4 That's why even though #2 says LRP needs current_year + next 3 years, the db filter is actually the other calendar_year = current_year
        #   3.5 This logic will come into play again in later steps when we actually group the data into groups of year
        #   3.6 Dont even ask...
        # 4. quarter_dates:
        #   4.1 Some properties don't follow the standard quarter date calendar, the actual dates for these come from quarter_date_override
        #   4.2 For LRP, >= current_year + 3 don't have quarter_dates, therefore gotta do a left join instead of inner join through finance_quarter: :quarter_date association
        # 5. Quarter is considered to not have swing_dates if disable_swing_date == 1 AND does not have any override_quarter_date values
        # 6. Allocation name is overwritten by model_allocation_description if show_description == true

        def by_quarter_dataset
          # This query populates data for all the tables whose columns are by quarter.
          # Most tables you see on the UI is of this type.

          base_dataset
            .joins(finance_quarter: %i[calendar_year quarter_date])
            .joins('left join QUARTER_DATE_OVERRIDE on FINANCE_QUARTER.FINANCE_QUARTER_ID = QUARTER_DATE_OVERRIDE.FINANCE_QUARTER_ID
              and QUARTER_DATE_OVERRIDE.PROPERTY_ID = FINANCE_REVENUE_VIEW.PROPERTY_ID')
            .where("(FINANCE_MODEL_NAME in ('Current Estimate', 'Budget') and CALENDAR_YEAR.CALENDAR_YEAR = #{@calendar_year.calendar_year})
              or (FINANCE_MODEL_NAME = 'Actuals' and CALENDAR_YEAR.CALENDAR_YEAR between #{@calendar_year.calendar_year - 3} and #{@calendar_year.calendar_year - 1})
              or (FINANCE_MODEL_NAME = 'LRP'
                and CALENDAR_YEAR.CALENDAR_YEAR = #{@calendar_year.calendar_year}
                and CALENDAR_YEAR_FINANCE_QUARTER.CALENDAR_YEAR between #{@calendar_year.calendar_year} and #{@calendar_year.calendar_year + 2}
            )")
            .select("
              FINANCE_REVENUE_VIEW.*,
              CALENDAR_YEAR.CALENDAR_YEAR_ID as FH_CALENDAR_YEAR_ID,
              CALENDAR_YEAR.CALENDAR_YEAR as FH_CALENDAR_YEAR,
              CALENDAR_YEAR_FINANCE_QUARTER.CALENDAR_YEAR_ID as FQ_CALENDAR_YEAR_ID,
              CALENDAR_YEAR_FINANCE_QUARTER.CALENDAR_YEAR as FQ_CALENDAR_YEAR,
              FINANCE_MONTH_NAME,
              FINANCE_MODEL_NAME,
              SHOW_DESCRIPTION,
              ALLOCATION_NAME,
              FINANCE_REVENUE_TYPE_NAME,
              FINANCE_QUARTER_NAME,
              VALUE_TYPE,
              (case when (DISABLE_SWING_DATE = 1 and QUARTER_DATE_OVERRIDE.SWING_DATE is null) then null else coalesce(QUARTER_DATE_OVERRIDE.SWING_DATE, QUARTER_DATE.SWING_DATE) end) as SWING_DATE,
              coalesce(QUARTER_DATE_OVERRIDE.QUARTER_DATE, QUARTER_DATE.QUARTER_DATE) as QUARTER_DATE
            ")
            .order('FINANCE_MODEL.DISPLAY_ORDER, FINANCE_REVENUE_TYPE.DISPLAY_ORDER, MODEL_ALLOCATION.DISPLAY_ORDER, FQ_CALENDAR_YEAR, FINANCE_QUARTER.FINANCE_QUARTER')
        end
        # rubocop:enable Layout/LineLength

        # rubocop:disable Layout/LineLength
        def by_year_dataset
          # This query populates data for all the tables whose columns are by year.
          # There's only a few of these under LRP.

          base_dataset
            .joins(finance_quarter: [:calendar_year])
            .where("FINANCE_MODEL_NAME = 'LRP' and FINANCE_MONTH_NAME = 'Full-Year'")
            .where('CALENDAR_YEAR.CALENDAR_YEAR': @calendar_year.calendar_year)
            .where('CALENDAR_YEAR_FINANCE_QUARTER.CALENDAR_YEAR': compact_year_range)
            .select("
              CALENDAR_YEAR_FINANCE_QUARTER.CALENDAR_YEAR_ID as FQ_CALENDAR_YEAR_ID,
              FINANCE_REVENUE_TYPE.FINANCE_REVENUE_TYPE_ID,
              ALLOCATION.ALLOCATION_ID,
              max(CALENDAR_YEAR_FINANCE_QUARTER.CALENDAR_YEAR) as FQ_CALENDAR_YEAR,
              max(SHOW_DESCRIPTION) as SHOW_DESCRIPTION,
              max(MODEL_ALLOCATION_DESCRIPTION) as MODEL_ALLOCATION_DESCRIPTION,
              max(ALLOCATION_NAME) as ALLOCATION_NAME,
              max(FINANCE_REVENUE_TYPE_NAME) as FINANCE_REVENUE_TYPE_NAME,
              listagg(FINANCE_REVENUE_ID, ',') within group (order by FINANCE_REVENUE_ID) as FINANCE_REVENUE_IDS,
              listagg(FINANCE_QUARTER.FINANCE_QUARTER_ID, ',') within group (order by FINANCE_QUARTER.FINANCE_QUARTER_ID) as FINANCE_QUARTER_IDS,
              max(FINANCE_REVENUE_VIEW.FINANCE_HEADER_ID) as FINANCE_HEADER_ID,
              max(FINANCE_REVENUE_VIEW.CALENDAR_YEAR_ID) as CALENDAR_YEAR_ID,
              max(FINANCE_REVENUE_VIEW.FINANCE_MONTH_ID) as FINANCE_MONTH_ID,
              max(FINANCE_REVENUE_VIEW.FINANCE_MODEL_ID) as FINANCE_MODEL_ID,
              max(PROPERTY_ID) as PROPERTY_ID,
              sum(QUARTER_DOLLARS) as QUARTER_DOLLARS,
              max(LOCKED) as LOCKED,
              max(VALUE_TYPE) as VALUE_TYPE
            ")
            .group('CALENDAR_YEAR_FINANCE_QUARTER.CALENDAR_YEAR_ID, FINANCE_REVENUE_TYPE.FINANCE_REVENUE_TYPE_ID, ALLOCATION.ALLOCATION_ID')
            .order('FQ_CALENDAR_YEAR, max(FINANCE_REVENUE_TYPE.DISPLAY_ORDER), max(MODEL_ALLOCATION.DISPLAY_ORDER)')
        end
        # rubocop:enable Layout/LineLength

        def base_dataset
          ds = FinanceRevenueView
               .joins(:finance_revenue_type,
                      :allocation,
                      :finance_model,
                      :finance_month,
                      :calendar_year,
                      :model_allocation)
               .where("(FINANCE_MONTH_NAME = '#{@finance_month.name}' and FINANCE_MODEL_NAME = 'Current Estimate')
              or (FINANCE_MONTH_NAME = 'Full-Year' and FINANCE_MODEL_NAME != 'Current Estimate')")
               .where(property_id: @property.id)

          ds = ds.where('FINANCE_MODEL.FINANCE_MODEL_ID = ?', @finance_model_id) if @finance_model_id
          ds
        end

        def calendar_year_type_by_finance_model(finance_model)
          finance_model == 'LRP' ? :fq_calendar_year : :fh_calendar_year
        end

        def compact_year_range
          (@calendar_year.calendar_year + 2)..(@calendar_year.calendar_year + 4)
        end

        def format_group_by_calendar_year
          raise 'CalendarYear group_by format must be declared in inheriting class'
        end

        def property_type
          raise 'PropertyType needs to be declared in inheriting class'
        end

        def base_model
          @base_model ||= FinanceRevenue
        end
      end
    end
  end
end
