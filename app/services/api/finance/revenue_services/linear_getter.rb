# frozen_string_literal: true

module Api
  module Finance
    module RevenueServices
      class LinearGetter < Api::Finance::RevenueServices::Get<PERSON>
        def initialize(property, finance_month, calendar_year, opts = {})
          super
        end

        private

        def format_group_by_calendar_year(dataset, finance_model)
          group_by = calendar_year_type_by_finance_model(finance_model)
          dataset = dataset.group_by { |row| row[group_by] }.map do |calendar_year, sub_dataset|
            {
              calendar_year:,
              column_type: :quarter,
              data: format_group_by_quarter(sub_dataset).flatten
            }
          end

          format_additional_quarter(dataset, group_by)
        end

        def format_additional_quarter(dataset, group_by)
          # For all models, need to include the swing data from the first quarter of the following years;
          # this is the very last column on the left you see on the UI.

          case group_by
          when :fh_calendar_year
            format_additional_quarter_by_removing_data(dataset)
          when :fq_calendar_year
            format_additional_quarter_by_adding_data(dataset)
          else
            raise 'Invalid calendar_year group_by'
          end
        end

        def format_additional_quarter_by_removing_data(dataset)
          # For models that group by finance_header.calendar_year, the additional quarter already exists
          # in the year grouping therefore only needs to remove the non-swing data.

          dataset.each { |year| year[:data].pop }
          dataset
        end

        def format_additional_quarter_by_adding_data(dataset)
          # For model(s) that group by finance_quarter.calendar_year, gotta iterate to the following year
          # and extract the swing data from its first quarter.

          dataset.each do |year|
            following_year = dataset.find { |row| row[:calendar_year] == year[:calendar_year] + 1 }
            next unless following_year

            year[:data] << following_year[:data].first
          end

          dataset.pop
          dataset << format_compact
        end

        def format_compact
          {
            calendar_year: "#{@calendar_year.calendar_year + 2} - #{@calendar_year.calendar_year + 4}",
            column_type: :year,
            data: to_hash(by_year_dataset).group_by { |row| row[:fq_calendar_year] }.map do |calendar_year, sub_dataset|
              {
                calendar_year:,
                data: format_column(sub_dataset)
              }
            end
          }
        end

        def format_group_by_quarter(dataset)
          dataset.group_by { |row| row[:finance_quarter_name] }.map do |_quarter, sub_dataset|
            format_swing_and_quarter_date(sub_dataset)
          end
        end

        def format_swing_and_quarter_date(dataset)
          # For every quarter, split the data into 2 columns:
          # swing quarter and regular quarter with the appropriate labels and dollar values

          [
            format_quarter_column(true, nil, dataset.first[:swing_date], format_column(dataset)),
            format_quarter_column(false, dataset.first[:finance_quarter_name], dataset.first[:quarter_date],
                                  format_column(dataset))
          ]
        end

        def format_quarter_column(is_swing, finance_quarter_name, quarter_date, data)
          {
            swing_quarter: is_swing,
            finance_quarter_name:,
            quarter_date:,
            data:
          }
        end

        def property_type
          'Linear'
        end
      end
    end
  end
end
