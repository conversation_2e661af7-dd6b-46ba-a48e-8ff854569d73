# frozen_string_literal: true

# Abstract class to be used by individual finance_metric_type's services

module Api
  module Finance
    class Getter
      attr_reader :property, :finance_month, :calendar_year

      def initialize(property, finance_month, calendar_year, opts = {})
        Rails.logger.info("Initializing #{self.class}")
        @property = property
        @finance_month = finance_month
        @calendar_year = calendar_year

        @format_response = opts[:format_response]
        @finance_model_id = opts[:finance_model_id]
      end

      def process
        @format_response == 'false' ? to_hash(by_quarter_dataset) : format_response
      end

      private

      def format_response
        {
          property_id: property.id,
          property_name: property.name,
          finance_month_id: finance_month.id,
          finance_month_name: finance_month.finance_month_name,
          calendar_year_id: calendar_year.id,
          calendar_year: calendar_year.calendar_year,
          data: format_group_by_finance_model(to_hash(by_quarter_dataset))
        }
      end

      def format_group_by_finance_model(dataset)
        dataset.group_by { |row| row[:finance_model_name] }.map do |finance_model, sub_dataset|
          dataset = format_group_by_calendar_year(sub_dataset)
          dataset << format_compact(to_hash(by_year_dataset)) if finance_model == 'LRP'

          {
            finance_model_name: finance_model,
            locked: locked?(finance_model),
            data: dataset
          }
        end
      end

      def format_group_by_calendar_year(dataset)
        dataset.group_by { |row| row[:fq_calendar_year] }.map do |calendar_year, sub_dataset|
          {
            calendar_year:,
            column_type: :quarter,
            data: format_group_by_quarter(sub_dataset)
          }
        end
      end

      def format_group_by_quarter(dataset)
        dataset.group_by { |row| row[:finance_quarter_name] }.map do |finance_quarter, sub_dataset|
          {
            finance_quarter:,
            data: format_column(sub_dataset)
          }
        end
      end

      def compact_year_range(calendar_year)
        (calendar_year.calendar_year + 2)..(calendar_year.calendar_year + 4)
      end

      def format_compact(dataset)
        {
          calendar_year: "#{calendar_year.calendar_year + 2} - #{calendar_year.calendar_year + 4}",
          column_type: :year,
          data: dataset.group_by { |row| row[:fq_calendar_year] }.map do |calendar_year, sub_dataset|
            {
              calendar_year:,
              data: format_column(sub_dataset)
            }
          end
        }
      end

      def format_column(dataset)
        returned_fields = "#{base_model.name}View".constantize
                                                  .columns
                                                  .map(&:name)
                                                  .map(&:to_sym) - common_omitted_fields + common_additional_fields
        dataset.map { |row| row.slice(*returned_fields) }
      end

      def common_omitted_fields
        %i[active model_allocation_id demo_daypart_default_id]
      end

      def common_additional_fields
        (unique_additional_fields + %i[finance_quarter_ids allocation_name show_description value_type
                                       model_allocation_description]) << :"#{base_model.primary_key}s"
      end

      def unique_additional_fields
        []
      end

      def locked?(finance_model_name)
        FinanceHeader.find_by(
          calendar_year:,
          property:,
          finance_month:,
          finance_model: FinanceModel.find_by_name(finance_model_name)
        )&.locked || false
      end

      def to_hash(dataset)
        dataset.map(&:serializable_hash).map(&:symbolize_keys)
      end

      def base_model_pk
        @base_model_pk ||= base_model.primary_key.to_sym
      end

      def by_year_dataset
        raise 'Must be declared by inheriting class'
      end

      def by_quarter_dataset
        raise 'Must be declared by inheriting class'
      end

      def base_model
        raise 'Must be declared by inheriting class'
      end
    end
  end
end
