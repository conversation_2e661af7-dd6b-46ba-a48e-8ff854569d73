# frozen_string_literal: true

module Api
  module Finance
    module RiskOpportunityServices
      class Getter < Api::Finance::Getter
        def initialize(property, finance_month, calendar_year, opts = {})
          super
        end

        private

        def by_year_dataset
          # This query populates data for all the tables whose columns are by year.
          # There's only a few of these under LRP.

          base_dataset
            .where("FINANCE_MODEL_NAME = 'LRP' and FINANCE_MONTH_NAME = 'Full-Year'")
            .where('CALENDAR_YEAR.CALENDAR_YEAR': @calendar_year.calendar_year)
            .where('CALENDAR_YEAR_FINANCE_QUARTER.CALENDAR_YEAR': compact_year_range(@calendar_year))
            .select("
              listagg(FINANCE_RISK_OPPORTUNITY_ID, ',')
                within group (order by FINANCE_RISK_OPPORTUNITY_ID) as FINANCE_RISK_OPPORTUNITY_IDS,
              listagg(FINANCE_QUARTER.FINANCE_QUARTER_ID, ',')
                within group (order by FINANCE_QUARTER.FINANCE_QUARTER_ID) as FINANCE_QUARTER_IDS,
              CALENDAR_YEAR_FINANCE_QUARTER.CALENDAR_YEAR as FQ_CALENDAR_YEAR,
              VARIANCE_TYPE.VARIANCE_TYPE_ID,
              max(FINANCE_RISK_OPPORTUNITY_VIEW.FINANCE_HEADER_ID) as FINANCE_HEADER_ID,
              max(FINANCE_RISK_OPPORTUNITY_VIEW.CALENDAR_YEAR_ID) as CALENDAR_YEAR_ID,
              max(FINANCE_RISK_OPPORTUNITY_VIEW.FINANCE_MODEL_ID) as FINANCE_MODEL_ID,
              max(FINANCE_RISK_OPPORTUNITY_VIEW.FINANCE_MONTH_ID) as FINANCE_MONTH_ID,
              max(FINANCE_RISK_OPPORTUNITY_VIEW.PROPERTY_ID) as PROPERTY_ID,
              max(FINANCE_MONTH.FINANCE_MONTH_NAME) as FINANCE_MONTH_NAME,
              max(FINANCE_MODEL.FINANCE_MODEL_NAME) as FINANCE_MODEL_NAME,
              max(VARIANCE_TYPE.VARIANCE_TYPE_NAME) as VARIANCE_TYPE_NAME,
              max(MODEL_ALLOCATION_DESCRIPTION) as MODEL_ALLOCATION_DESCRIPTION,
              max(VARIANCE_TYPE.SHOW_DESCRIPTION) as SHOW_DESCRIPTION,
              sum(QUARTER_DOLLARS) as QUARTER_DOLLARS,
              max(LOCKED) as LOCKED,
              max(FINANCE_COMMENT.USER_COMMENT) as USER_COMMENT,
              max(FINANCE_COMMENT.UPDATED_AT) as USER_COMMENT_UPDATED_AT,
              max(APP_USER.FIRST_NAME || ' ' || APP_USER.LAST_NAME) as USER_COMMENT_UPDATED_BY
            ")
            .group('CALENDAR_YEAR_FINANCE_QUARTER.CALENDAR_YEAR, VARIANCE_TYPE.VARIANCE_TYPE_ID')
            .order('max(VARIANCE_TYPE.DISPLAY_ORDER), FQ_CALENDAR_YEAR')
        end

        def by_quarter_dataset
          # This query populates data for all the tables whose columns are by quarter.
          # Most tables you see on the UI is of this type.

          base_dataset
            .where("(FINANCE_MODEL_NAME in
               ('Current Estimate', 'Budget') and CALENDAR_YEAR.CALENDAR_YEAR = #{@calendar_year.calendar_year})
              or (FINANCE_MODEL_NAME = 'Actuals' and CALENDAR_YEAR.CALENDAR_YEAR
                between #{@calendar_year.calendar_year - 3} and #{@calendar_year.calendar_year - 1})
              or (FINANCE_MODEL_NAME = 'LRP'
                and CALENDAR_YEAR.CALENDAR_YEAR = #{@calendar_year.calendar_year}
                and CALENDAR_YEAR_FINANCE_QUARTER.CALENDAR_YEAR
                  between #{@calendar_year.calendar_year} and #{@calendar_year.calendar_year + 1}
            )")
            .select("
              FINANCE_RISK_OPPORTUNITY_VIEW.*,
              CALENDAR_YEAR_FINANCE_QUARTER.CALENDAR_YEAR as FQ_CALENDAR_YEAR,
              FINANCE_MONTH_NAME,
              FINANCE_MODEL_NAME,
              VARIANCE_TYPE_NAME,
              SHOW_DESCRIPTION,
              FINANCE_QUARTER_NAME,
              FINANCE_COMMENT.USER_COMMENT,
              FINANCE_COMMENT.UPDATED_AT as USER_COMMENT_UPDATED_AT,
              APP_USER.FIRST_NAME || ' ' || APP_USER.LAST_NAME as USER_COMMENT_UPDATED_BY
            ")
            .order('FINANCE_MODEL.DISPLAY_ORDER,
                   VARIANCE_TYPE.DISPLAY_ORDER,
                   FQ_CALENDAR_YEAR,
                   FINANCE_QUARTER.FINANCE_QUARTER')
        end

        def base_dataset
          ds = FinanceRiskOpportunityView
               .joins(:variance_type, :finance_model, :finance_month, :calendar_year, finance_quarter: [:calendar_year])
               .left_joins(finance_comment: [:user])
               .where("(FINANCE_MONTH_NAME = '#{@finance_month.name}' and FINANCE_MODEL_NAME = 'Current Estimate')
              or (FINANCE_MONTH_NAME = 'Full-Year' and FINANCE_MODEL_NAME != 'Current Estimate')")
               .where(property_id: @property.id)

          ds = ds.where('FINANCE_MODEL.FINANCE_MODEL_ID = ?', @finance_model_id) if @finance_model_id
          ds
        end

        def unique_additional_fields
          %i[user_comment user_comment_updated_at user_comment_updated_by variance_type_name]
        end

        def base_model
          @base_model ||= FinanceRiskOpportunity
        end
      end
    end
  end
end
