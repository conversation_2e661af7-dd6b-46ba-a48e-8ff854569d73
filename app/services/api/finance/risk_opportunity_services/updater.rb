# frozen_string_literal: true

module Api
  module Finance
    module RiskOpportunityServices
      class Updater < Api::Finance::Updater
        EDITABLE_ATTRIBUTES = %i[quarter_dollars model_allocation_description].freeze

        def base_model
          @base_model ||= FinanceRiskOpportunity
        end

        private

        def create_new_record(record)
          if record[:user_comment]
            finance_comment = create_comment(record[:user_comment],
                                             record[:user_comment_updated_at])
          end

          record = record.slice(*create_attributes)
          record[:finance_comment_id] = finance_comment&.id || -1

          Rails.logger.info("Creating #{record}")
          base_model.create!(record)
        end

        def update_existing_record(data)
          record = base_model.find(data[base_model_pk])
          raise Error::ApiError::UnprocessableEntityError.new("Bad #{base_model_pk}", data) unless record

          update_hash = data.slice(*editable_attributes)

          # create a new finance_comment if existing record doesn't have one associated
          if data[:user_comment]
            update_hash[:finance_comment_id] =
              create_or_update_comment(record, data[:user_comment], data[:user_comment_updated_at]).id
          end

          Rails.logger.info("Updating #{data}")
          record.update!(update_hash)

          # touch finance_header only for those models that have association with finance_header
          begin
            record.finance_header.touch_with_user(current_user)
          rescue StandardError
            nil
          end
        end

        def create_or_update_comment(record, comment_message, comment_timestamp)
          if (finance_comment = record.finance_comment)
            update_comment(finance_comment, comment_message, comment_timestamp)
          else
            create_comment(comment_message, comment_timestamp)
          end
        end

        def create_comment(comment_message, comment_timestamp)
          comment_timestamp ||= formatted_now
          FinanceComment.create!(user_comment: comment_message, app_user_id: current_user.id,
                                 created_at: comment_timestamp, updated_at: comment_timestamp)
        end

        def update_comment(finance_comment, comment_message, comment_timestamp)
          comment_timestamp ||= formatted_now
          finance_comment.update!(user_comment: comment_message, app_user_id: current_user.id,
                                  updated_at: comment_timestamp)
          finance_comment
        end

        def formatted_now
          Time.now.strftime('%Y-%m-%d %H:%M:%S')
        end
      end
    end
  end
end
