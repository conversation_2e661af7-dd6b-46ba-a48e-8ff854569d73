# frozen_string_literal: true

module Api
  class MoveDealService
    def initialize(deal_ids, destination_parent_deal_id)
      @deal_ids = deal_ids
      @destination_parent_deal_id = destination_parent_deal_id
    end

    def move
      parent_deal = ParentDeal.find(@destination_parent_deal_id)
      linked_deals = []

      ActiveRecord::Base.transaction do
        @deal_ids.each do |deal_id|
          deal = Deal.find(deal_id)
          unless deal.advertiser_id == parent_deal.advertiser_id &&
                 deal.agency_id == parent_deal.agency_id &&
                 deal.marketplace_id == parent_deal.marketplace_id
            raise Error::ApiError::FailedDependencyError,
                  'Invalid parent destination'
          end

          deal.update(parent_deal:)
          linked_deals << Deal.linked_deals(deal.id).all
        end

        linked_deals.flatten.uniq.map { |deal| deal.update(parent_deal:) }
      end
    end
  end
end
