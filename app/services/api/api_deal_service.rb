# frozen_string_literal: true

module Api
  # Drop-in replacement for MyDealService
  class ApiDealService
    def initialize(user, filters = {})
      @user_id = user.id
      @filters = filters

      @filters[:budget_year_id] ||= BudgetYear.find_by_default_budget_year(true)&.id
      # @filters[:marketplace_id] ||= Marketplace.find_by_default_marketplace(true)&.id
    end

    def index
      base_index.select('
        api_deal.*,
        ud.edit_registration
      ').order(sort_opts)
    end

    def budget_totals
      ApiDeal
        .joins("join (#{base_index.distinct.select('budget_id').to_sql}) bi on api_deal.budget_id = bi.budget_id")
        .select('
          sum(api_deal.py_registration_total) as py_registration_total,
          sum(api_deal.ask_total) as ask_total,
          sum(api_deal.current_projection_total) as current_projection_total,
          sum(api_deal.registration_total) as registration_total
        ')
    end

    def status_counts
      base_index.select('
        api_deal.status_id,
        max(api_deal.status_name) as status_name,
        count(distinct api_deal.deal_id) as count
      ').group(:status_id)
    end

    def show
      raise Error::ApiError::UnauthorizedError, 'User does not have access to deal' if no_access?

      ApiDeal
        .joins("join (#{user_deal.to_sql}) ud on api_deal.deal_id = ud.deal_id")
        .where(deal_id: @filters[:id], budget_year_id: @filters[:budget_year_id])
        .select('
          api_deal.*,
          ud.edit_registration
        ')
        .first
    end

    def deal_link_info
      index
        .reorder('')
        .joins('join deal_link_asc on deal_link_asc.deal_id = api_deal.deal_id')
        .joins('join deal_link on deal_link.deal_link_id = deal_link_asc.deal_link_id')
        .joins('join deal_link_type on deal_link.deal_link_type_id = deal_link_type.deal_link_type_id')
        .joins('left join stealth_mode_budget on stealth_mode_budget.budget_id = api_deal.budget_id')
        .select(
          'deal_link_asc.deal_link_asc_id',
          'api_deal.deal_id',
          'api_deal.py_registration_total',
          'api_deal.property_id',
          'api_deal.property_name',
          'api_deal.send_to_salesforce sent_to_salesforce',
          'deal_link.deal_link_id',
          'deal_link.deal_link_name',
          'deal_link_type.deal_link_type_id',
          'deal_link_type.deal_link_type_name',
          'coalesce((stealth_mode_budget.actual_prequarter_amount +
           stealth_mode_budget.actual_quarter4_amount +
           stealth_mode_budget.actual_quarter1_amount +
           stealth_mode_budget.actual_quarter2_amount +
           stealth_mode_budget.actual_quarter3_amount +
           stealth_mode_budget.actual_postquarter_amount), 0) cy_stealth_registration'
        )
    end

    private

    def base_index
      dataset = ApiDeal.where(has_cy_budget: true).assigned_to(@user_id)
      dataset = filter_by_type(dataset)
      dataset = filter_by_ae(dataset)
      dataset = filter_by_agency(dataset)
      filter_by_attributes(dataset)
    end

    def user_deal
      UserDeal.where(app_user_id: @user_id, deal_id: @filters[:id])
    end

    def no_access?
      user_deal.empty?
    end

    def filter_by_type(dataset)
      case @filters.fetch(:deal_type, 'all').downcase
      when 'all' then dataset
      when 'mine' then dataset.belongs_to(@user_id)
      when 'split' then dataset.split_to(@user_id)
      end
    end

    def filter_by_ae(dataset)
      return dataset unless (app_user_id = parse_array_params(@filters[:app_user_id]))

      dataset.belongs_or_split_to(app_user_id)
    end

    def filter_by_agency(dataset)
      return dataset unless (agency_ids = parse_array_params(@filters[:agency_id]))

      agencies_and_descendants = BaseAgency.family_from_parents(BaseAgency.where(agency_id: agency_ids))
      dataset.where(agency_id: agencies_and_descendants.select(:agency_id))
    end

    def filter_by_attributes(dataset)
      attribute_filters.each do |attribute|
        next unless @filters[attribute]

        dataset = dataset.where(attribute => parse_array_params(@filters[attribute]))
      end

      dataset
    end

    def parse_array_params(value)
      return value unless value.is_a?(String)

      value.split(',').map(&:to_i)
    end

    def attribute_filters
      %i[budget_year_id marketplace_id property_id advertiser_id registration_type_id pillar_id placeholder
         measurement_type_id status_id deal_tag_id vertical_id partnership_id sales_type_id deal_id content_id
         selling_vertical_id send_to_salesforce planner_id currency_id sfdc_intg_vertical_id parent_deal_id
         parent_deal_type_id above_the_line_id advertiser_brand_id account_manager_id]
    end

    def sort_opts
      opts = {
        'agency' => ApiDeal::DEFAULT_SORT_OPTS,
        'advertiser' => ApiDeal::ADVERTISER_SORT_OPTS
      }
      sort_key = @filters[:sort_opt]
      sort_key.present? ? opts[sort_key] : opts['agency']
    end
  end
end
