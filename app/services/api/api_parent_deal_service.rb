# frozen_string_literal: true

module Api
  class ApiParentDealService
    PARENT_DEAL_GROUP_COLUMNS = %i[
      advertiser_id
      advertiser_name
      agency_id
      agency_name
      marketplace_id
      marketplace_name
      parent_deal_id
      parent_deal_name
      parent_deal_type_color
      parent_deal_type_id
      parent_deal_type_name
      parent_sf_deal_id
    ].freeze

    def initialize(user, filters = {})
      @user_id = user.id
      @filters = filters

      @filters[:budget_year_id] ||= BudgetYear.find_by_default_budget_year(true)&.id
      # @filters[:marketplace_id] ||= Marketplace.find_by_default_marketplace(true)&.id
    end

    def index
      paginated_parents = paginated_budget_ids_from_parent_deals
      parents_with_deals = ApiParentDeal.where(budget_id: paginated_parents[:budget_ids])
      parent_without_deals = ApiParentDeal.where(parent_deal_id: paginated_parents[:childless_parent_deal_ids])
      parents_with_deals.or(parent_without_deals).order(sort_opts)
    end

    private

    # Dataset must be paginated and grouped by parent deals.
    # Child budgets are aggregated for each parent grouping.
    # An array of budgets is returned for each grouped/paged
    # parent deal set. We must also seperately select parent
    # deals that do not have children
    def paginated_budget_ids_from_parent_deals
      # Union both datasets
      paginated_data = ApiParentDeal.from("
        ((#{parents_with_deals.to_sql}) union (#{parents_without_deals.to_sql})) api_parent_deal
      ").select(
        "listagg(budget_id, ',') within group ( order by null ) as budget_ids,
          parent_deal_id"
      ).distinct
                                    .group(*PARENT_DEAL_GROUP_COLUMNS)
                                    .order(sort_opts)
                                    .page(@filters[:page] || 1).per(@filters[:per_page] || 50)
      {
        budget_ids: paginated_data.map(&:budget_ids).join(',').split(',').reject(&:blank?),
        childless_parent_deal_ids: paginated_data.select { |b| b.budget_ids.blank? }.map(&:parent_deal_id)
      }
    end

    def parents_with_deals
      dataset = ApiParentDeal.where(has_cy_budget: true).assigned_to(@user_id)
      dataset = filter_by_agency(dataset)
      filter_by_attributes(dataset, attribute_filters)
    end

    def parents_without_deals
      dataset = ApiParentDeal.childless_assigned_to(@user_id)
      dataset = filter_by_agency(dataset)
      filter_by_attributes(dataset, childless_attribute_filters)
    end

    def filter_by_agency(dataset)
      return dataset unless (agency_ids = parse_array_params(@filters[:agency_id]))

      agencies_and_descendants = BaseAgency.family_from_parents(BaseAgency.where(agency_id: agency_ids))
      dataset.where(agency_id: agencies_and_descendants.select(:agency_id))
    end

    def filter_by_attributes(dataset, filters)
      filters.each do |attribute|
        next unless @filters[attribute]

        dataset = dataset.where(attribute => parse_array_params(@filters[attribute]))
      end

      dataset
    end

    def parse_array_params(value)
      return value unless value.is_a?(String)

      value.split(',').map(&:to_i)
    end

    def attribute_filters
      %i[advertiser_id budget_year_id deal_id marketplace_id parent_deal_id parent_deal_type_id property_id]
    end

    def childless_attribute_filters
      %i[advertiser_id marketplace_id parent_deal_id parent_deal_type_id]
    end

    def sort_opts
      opts = {
        'agency' => ApiParentDeal::DEFAULT_SORT_OPTS,
        'advertiser' => ApiParentDeal::ADVERTISER_SORT_OPTS
      }
      sort_key = @filters[:sort_opt]
      sort_key.present? ? opts[sort_key] : opts['agency']
    end
  end
end
