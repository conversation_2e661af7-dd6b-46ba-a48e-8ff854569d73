# frozen_string_literal: true

module Api
  module Salesforce
    # Retrieves OperativeOne id's from Salesforce for digital plans as sales system id's
    class SalesSystemRetrieverService
      def perform
        digital_opportunity_ids = Budget.digital_budgets_sent_to_sf_without_sales_systems
                                        .pluck(:sf_opportunity_id)
                                        .compact
        return if digital_opportunity_ids.empty?

        opportunity_ids_with_op1_ids = ::Salesforce::Client.new.get_operative_one_ids(digital_opportunity_ids)

        opportunity_ids_with_op1_ids.each do |opp|
          budget = Budget.find_by(sf_opportunity_id: opp[:opportunity_id])

          Rails.logger.info("Creating sales system #{opp[:operative_one_id]} for digital budget #{budget.id}")
          BudgetSalesSystem.create(budget:, sales_system_id: opp[:operative_one_id]) if budget
        end
      end
    end
  end
end
