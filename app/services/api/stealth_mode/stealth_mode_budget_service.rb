# frozen_string_literal: true

module Api
  module StealthMode
    # Service to interface with Budget/StealthModeBudget updates
    class StealthModeBudgetService
      REQUIRED_KEYS = %i[
        budget_id
        current_pricing
        rate_of_change
        agency_counter_roc
        parent_agency_id
        s_actual_prequarter_amount
        s_actual_quarter4_amount
        s_actual_quarter1_amount
        s_actual_quarter2_amount
        s_actual_quarter3_amount
        s_actual_postquarter_amount
        s_projected_prequarter_amount
        s_projected_quarter4_amount
        s_projected_quarter1_amount
        s_projected_quarter2_amount
        s_projected_quarter3_amount
        s_projected_postquarter_amount
        ask_prequarter_amount
        ask_quarter4_amount
        ask_quarter1_amount
        ask_quarter2_amount
        ask_quarter3_amount
        ask_postquarter_amount
        actual_prequarter_amount
        actual_quarter4_amount
        actual_quarter1_amount
        actual_quarter2_amount
        actual_quarter3_amount
        actual_postquarter_amount
        projected_prequarter_amount
        projected_quarter4_amount
        projected_quarter1_amount
        projected_quarter2_amount
        projected_quarter3_amount
        projected_postquarter_amount
      ].freeze

      DEAL_KEYS = %i[
        bycal
        placeholder
        registration_type_id
        measurement_type_id
        sfdc_intg_vertical_id
      ].freeze

      def initialize(budget:, stealth_enabled:, update_by_stealth_status:)
        @budget_hash = budget.to_h.with_indifferent_access
        @stealth_enabled = stealth_enabled
        @update_by_stealth_status = update_by_stealth_status
        validate_budget_hash
      end

      def update
        update_budget unless @update_by_stealth_status && @stealth_enabled
        update_roc if @update_by_stealth_status && @stealth_enabled
        upsert_stealth_budget if @stealth_enabled
      end

      private

      def budget
        @budget ||= Budget.includes(:stealth_mode_budget).find(@budget_hash[:budget_id])
      end

      def budget_attrs
        bcols = Budget.column_names.map(&:to_sym)
        @budget_hash.slice(*bcols).except(:budget_id)
      end

      def stealth_mode_budget_attrs
        sm_cols = StealthModeBudget.column_names.map(&:to_sym)
        @budget_hash
          .transform_keys { |key| key.end_with?('_amount') ? key.gsub('s_', '') : key }
          .slice(*sm_cols)
          .except(:budget_id)
      end

      def update_budget
        attrs =
          if @stealth_enabled
            budget_attrs
          else
            budget_attrs.merge(new_business_pricing: @budget_hash[:current_pricing] * 100)
          end
        budget.deal.update!(deal_update_attrs)
        budget.update!(attrs)
      end

      def update_roc
        budget.update!(budget_attrs.slice(:rate_of_change))
      end

      def upsert_stealth_budget
        smb = StealthModeBudget.find_or_create_by(budget_id: budget.id)
        smb.update!(stealth_mode_budget_attrs
                      .merge(stealth_new_business_pricing: @budget_hash[:current_pricing] * 100)
                      .except(:budget_id))
      end

      def validate_budget_hash
        hsh_keys = @budget_hash.symbolize_keys.slice(*REQUIRED_KEYS).keys
        return if hsh_keys.size == REQUIRED_KEYS.size

        raise ::Error::ApiError::BadRequestError,
              "Invalid budget argument: #{@budget_hash}. Missing #{REQUIRED_KEYS - hsh_keys}"
      end

      def deal_update_attrs
        DEAL_KEYS.each_with_object({}) do |key, hsh|
          hsh[key] = @budget_hash[key] if @budget_hash.key?(key)
        end
      end
    end
  end
end
