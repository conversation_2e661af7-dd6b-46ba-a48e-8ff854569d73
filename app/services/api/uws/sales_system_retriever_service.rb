# frozen_string_literal: true

module Api
  module Uws
    # Retrieves OnAir id's from UWS for linear plans as sales system id's
    class SalesSystemRetrieverService
      def perform
        Rails.logger.info("Retriving Sales System ID's from UWS")
        budgets = Budget.linear_budgets_sent_to_sf_without_sales_systems
        return if budgets.nil? || budgets.empty?

        query = Rails.env.production? ? prod_query(budgets) : non_prod_query(budgets)
        Rails.logger.info("Query State: #{query.state}, State Reason: #{query.state_reason},
                           Query Execution ID: #{query.query_execution_id}")
        query.wait
        uws_opportunities = query.to_h.map do |r|
          {
            sf_opportunity_id: r['external_opportunity_id'],
            sales_system_id: r['onair_plan_id']
          }
        end

        Rails.logger.info('No UWS Opportunties found') && return if uws_opportunities.empty?

        assign_sales_system_ids(budgets, uws_opportunities)
      end

      private

      def assign_sales_system_ids(budgets, uws_opportunities)
        budgets.each do |budget|
          budget_sf_opportunity_ids = budget.sf_opportunity_id.split(',')

          matching_opportunities = budget_sf_opportunity_ids.map do |budget_sf_opportunity_id|
            uws_opportunities.select do |op|
              op[:sf_opportunity_id] == budget_sf_opportunity_id
            end
          end

          sales_system_ids = matching_opportunities&.flatten&.reject(&:empty?)&.map { |op| op[:sales_system_id] }
          next if sales_system_ids.nil? || sales_system_ids.empty?

          sales_system_ids.each do |sales_system_id|
            Rails.logger.info("Creating sales system #{sales_system_id} for linear budget #{budget.id}")
            BudgetSalesSystem.create(budget:, sales_system_id:)
          end
        end
      end

      def budget_sf_opportunity_ids(budgets)
        ids = budgets.pluck(:sf_opportunity_id).map do |o|
          o.split(',')
        end&.flatten&.reject(&:empty?)

        ids&.map { |o| "'#{o}'" }&.join(',')
      end

      # The prod query specifies the database name when creating the connection - this must be supplied
      # otherwise there will be permission issues
      def prod_query(budgets)
        sql_statement = "select uoh.external_opportunity_id, uph.original_plan_id as onair_plan_id
                         from uws_opportunity_header uoh
                         inner join uws_plan_header uph
                         on uoh.opportunity_id = uph.opportunity_id
                         where uph.original_plan_id is not null
                         and uoh.external_opportunity_id
                           in (#{budget_sf_opportunity_ids(budgets)})"
        Rails.logger.info(sql_statement)

        conn = Athens::Connection.new(database: ENV['UWS_DATABASE_NAME'])
        conn.execute(sql_statement, work_group: ENV['UWS_WORKGROUP'])
      end

      # The non-prod query joins across multiple databases, causing connectivity issues in production
      def non_prod_query(budgets)
        sql_statement = "select uoh.external_opportunity_id, uph.original_plan_id as onair_plan_id
                         from uws_prod.uws_opportunity_header uoh
                         inner join #{ENV['UWS_DATABASE_NAME']}.uws_plan_header uph
                         on uoh.opportunity_id = uph.opportunity_id
                         where uph.original_plan_id is not null
                         and uoh.external_opportunity_id
                           in (#{budget_sf_opportunity_ids(budgets)})"
        Rails.logger.info(sql_statement)

        conn = Athens::Connection.new
        conn.execute(sql_statement, work_group: ENV['UWS_WORKGROUP'])
      end
    end
  end
end
