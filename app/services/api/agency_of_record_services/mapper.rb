# frozen_string_literal: true

module Api
  module AgencyOfRecordServices
    class Mapper
      # list of sfdc keys that need to be mapped
      SFDC_KEYS = %i[sfdc_agency_id sfdc_advertiser_id sfdc_property_type_id sfdc_location_id
                     sfdc_default_category_id].freeze

      EXTERNAL_KEY_TYPE_IDS = {
        advertiser: 1,
        agency: 2,
        category: 3,
        location: 63,
        property_type: 64
      }.freeze

      def initialize(params)
        @update_params = params
      end

      def process
        accepted_keys = AgencyOfRecord.column_names

        {}.tap do |hash|
          @update_params.each do |key, value|
            if SFDC_KEYS.include?(key.to_sym)
              pam_id = pam_id_from(salesforce_id: value) || create_missing_record_with(sfdc_key: key)
              Rails.logger.info("Mapped #{value} to #{pam_id || 'nil'} for #{key}")
              next unless accepted_keys.include?(key_with_removed_prefix_from(key:))

              hash[key_with_removed_prefix_from(key:)] = pam_id
            else
              next unless accepted_keys.include?(key.to_s)

              hash[key] = value
            end
          end
        end.with_indifferent_access
      end

      private

      def system_key_mappings
        @system_key_mappings ||= SystemKeyAssociation
                                 .left_joins(:advertiser, :agency, :category, :location, :property_type)
                                 .joins(:external_system, :external_key_type)
                                 .where(external_key_type_id: EXTERNAL_KEY_TYPE_IDS.values)
                                 .where(external_system: { external_system_name: 'SFDC' })
      end

      def pam_id_from(salesforce_id:)
        mappings = system_key_mappings.find_by(external_system_key: salesforce_id)
        mappings&.pam_key
      end

      def create_missing_record_with(sfdc_key:)
        create_methods[sfdc_key]&.call
      end

      def create_missing_agency
        Rails.logger.info('Creating missing agency')
        ActiveRecord::Base.transaction do
          agency = Agency.create!(agency_name: @update_params[:agency_name])
          SystemKeyAssociation.create!(
            pam_key: agency.id,
            external_system_id: salesforce_system_id,
            external_key_type_id: EXTERNAL_KEY_TYPE_IDS[:agency],
            external_system_key: @update_params[:sfdc_agency_id],
            system_key_asc_type_id: combined_system_key_asc_type_id
          )
          agency.id
        end
      end

      def create_missing_advertiser
        Rails.logger.info('Creating missing advertiser')
        ActiveRecord::Base.transaction do
          advertiser = Advertiser.create!(
            advertiser_name: @update_params[:advertiser_name],
            default_category_id: pam_id_from(salesforce_id: @update_params[:sfdc_default_category_id]) || -1
          )
          SystemKeyAssociation.create!(
            pam_key: advertiser.id,
            external_system_id: salesforce_system_id,
            external_key_type_id: EXTERNAL_KEY_TYPE_IDS[:advertiser],
            external_system_key: @update_params[:sfdc_advertiser_id],
            system_key_asc_type_id: combined_system_key_asc_type_id
          )
          advertiser.id
        end
      end

      def key_with_removed_prefix_from(key:)
        string_key = key.to_s
        string_key.delete_prefix('sfdc_')
      end

      def salesforce_system_id
        @salesforce_system_id ||= ExternalSystem.find_by(external_system_name: 'SFDC')&.id
      end

      def combined_system_key_asc_type_id
        @combined_system_key_asc_type_id ||= SystemKeyAssociationType.find_by(system_key_asc_type_name: 'COMBINED')&.id
      end

      def create_methods
        {
          sfdc_agency_id: -> { create_missing_agency },
          sfdc_advertiser_id: -> { create_missing_advertiser }
        }.with_indifferent_access
      end
    end
  end
end
