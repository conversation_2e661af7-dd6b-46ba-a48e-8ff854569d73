# frozen_string_literal: true

module Api
  class PortalTeamsService
    def initialize(portal_team)
      @portal_team = portal_team
    end

    # Creates an assignment/role if edit_stealth_mode is being set to true
    # Sets portal_management to false if edit_stealth_mode is being set to false on updates
    #
    def set_assignment_and_role(stealth, method)
      return if stealth.nil?

      app_user_id = @portal_team.app_user_id
      agency_id = @portal_team.agency_id

      portal_role_id = Role.find_by(app_role_name: 'Portal')&.app_role_id
      raise RoleNotExistError("Role does not exist by name 'Portal'") unless portal_role_id

      if stealth
        # create the assignment
        assignment = Assignment.find_or_create_by(app_user_id:, agency_id:)
        assignment.update(portal_management: true)

        # create the role
        UserRole.find_or_create_by(app_user_id:, app_role_id: portal_role_id)
      else
        # set the assignment if it's an update
        if method == 'update'
          Assignment.find_by(app_user_id:,
                             agency_id:).falsify_portal_management
        end
      end
    end
  end

  class RoleNotExistError < StandardError; end
end
