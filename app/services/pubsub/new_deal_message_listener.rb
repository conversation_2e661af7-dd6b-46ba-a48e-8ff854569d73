# frozen_string_literal: true

module Pubsub
  # Listens for messages related to a new deals created from Agency Gateway
  # Exchange: Defaults to 'agency.new_deal'
  class NewDealMessageListener
    include FanoutMessageListener
    include PamClient::Concerns::LoggerConcern

    MESSAGE_KEYS = %w[correlation_id source deals].freeze

    def initialize(ex_name = 'agency.new_deals.to.pam', queue_name = 'agency.new_deals')
      @exchange_name = ex_name
      @queue_name = queue_name
      @new_deals = []
      Rails.logger.debug "#{self.class.name} initialized. exchange: '#{exchange_name}', queue: '#{queue_name}'"
    end

    def listen
      new_deal_processor = lambda do |metadata, payload|
        logger.info('*********BEGIN PROCESSING MESSAGE*********')
        logger.debug(payload)
        tag = metadata.delivery_tag
        raise InvalidJsonError unless valid_payload?(payload)

        logger.info('*********CREATING AG NEW DEALS*********')
        create_new_deals
        publish_new_deals
        ack_message(tag)
      rescue StandardError => e
        logger.error("Error creating new deal: #{e.message}")
        logger.error("payload: #{payload}")
        logger.error(e.backtrace.join("\n"))
        reject_message(metadata.delivery_tag, e.message, payload)
      ensure
        logger.info('*********END PROCESSING MESSAGE*********')
      end

      subscribe(new_deal_processor)
    end

    private

    def create_new_deals
      @json_payload['deals'].each do |deal_json|
        agdc = AgNewDealCreator.new(deal_json)
        @new_deals << agdc.create
      end
    end

    def publish_new_deals
      Rails.logger.debug(
        "Sending new deal creation message to #{@json_payload['source']}. First 1000 characters of message:"
      )
      Rails.logger.debug(@new_deals[0..1000])
      Pubsub::Publisher.new_deals(publishable_message.to_json)
    end

    def valid_payload?(payload)
      return false unless valid_payload_json?(payload)

      valid_payload_structure?
    end

    # Returns parsed JSON if it is parsable, nil otherwise
    def valid_payload_json?(payload)
      @json_payload = JSON.parse(payload)
    rescue StandardError
      Rails.logger.warn('Message payload contained invalid JSON.')
      Rails.logger.debug("Message payload: '#{payload}'")
      nil
    end

    def valid_payload_structure?
      keys = @json_payload.keys
      (keys - MESSAGE_KEYS).empty?
    end

    def publishable_message
      {
        correlation_id: @json_payload['correlation_id'],
        source: 'PAM-API',
        new_deals: @new_deals
      }
    end
  end

  class InvalidJsonError < StandardError; end
end
