# frozen_string_literal: true

module Pubsub
  class MappingRulesRpcClient < RpcClient
    attr_accessor :agency

    def initialize(agency)
      super('ag.mapping.ruleset')
      @agency = agency
    end

    # @return [Bunny::Exchange]
    def call
      super(agency_id: agency.id) do |result|
        hash = parse_result(result)
        StealthUpload::Mapping::RuleSet.new(create_new_deals: hash[:create_new_deals],
                                            registration_column: hash[:registration_column_attributes],
                                            rules: hash[:rules_attributes],
                                            source_columns: hash[:source_columns_attributes])
      end
    end

    private

    def parse_result(result)
      result[:data][:attributes][:rules_attributes].each do |rule|
        # Merge conditions_attributes and actions_attributes from rule includes directly into rules
        rule.merge!(result[:included].find do |incl|
                      incl[:type] == 'rules' && incl[:id].to_s == rule[:id].to_s
                    end [:attributes])
        # Merge `sms_target_column_value` from action includes directly into action
        rule[:actions_attributes].each do |action|
          action.merge!(result[:included].find do |incl|
                          incl[:type] == 'actions' && incl[:id].to_s == action[:id].to_s
                        end [:attributes])
        end
        # Merge `source_column_name` from source column attributes directly into condition
        rule[:conditions_attributes].each do |condition|
          condition[:source_column_name] =
            result[:data][:attributes][:source_columns_attributes].find do |col|
              col[:id].to_s == condition[:source_column_id].to_s
            end [:source_column_name]
        end
      end
      result[:data][:attributes]
    end
  end
end
