# frozen_string_literal: true

module Pubsub
  # Class used to parse new deals
  class AgencyDealsParser
    include PamClient::Concerns::LoggerConcern

    attr_reader :parent_agency_id,
                :agency_deals,
                :file_header,
                :errors

    # Creates a new AgencyDealsParser instance
    # @param [Hash] payload
    def initialize(payload, file_header)
      @parent_agency_id = payload['parent_agency_id']
      @agency_deals = payload['agency_deals']
      @file_header = file_header
      @errors = []
    end

    # Parses an AgencyDeal Message
    def parse
      agency_deals.each do |deal|
        ad = build_ag_agency_deal(deal)
        unless ad.valid?
          logger.warn("#parse tried to create an invalid AgencyDeal. deal: #{deal}. Skipping.")
          logger.warn("Validation errors: #{ad.errors.to_a}")
          add_error(ad.errors.to_a)
          next
        end
        ad.save!
        create_ag_deal_comments(ad, deal)
      end
    end

    def valid?
      errors.empty?
    end

    private

    def add_error(error)
      errors << error
    end

    def ag_deal_writeable_columns
      AgencyDeal.column_names.reject do |column_name|
        column_name if %w[ag_agency_deal_id created_at parent_agency_id updated_at deal_id
                          comments].include?(column_name)
      end
    end

    def build_ag_agency_deal(deal_attributes)
      ad = AgencyDeal.where(deal_id: deal_attributes['deal_id']).first_or_initialize
      ad.parent_agency_id = parent_agency_id
      ad.file_header = file_header

      deal_attributes.slice(*ag_deal_writeable_columns).each { |name, value| ad.send(:"#{name}=", value) }
      ad
    end

    # Creates agency deal comments in commentary table
    # Note: Currently using first or create to create comments
    # until we figure out a way to differentiate between creating & updating comments
    #
    # @param ag_deal [AgencyDeal] AgencyDeal object
    # @param deal_attributes [HASH] Deal params
    # @return [Comment] AgencyDealComment Object
    def create_ag_deal_comments(ag_deal, deal_attributes)
      comments = JSON.parse(deal_attributes['comments'])
      comments.each do |comment|
        AgencyDealComment.where(source_id: ag_deal.current_budget_id,
                                commentary_type: 'AgencyDealComment',
                                message: comment['text'],
                                submitted_to_ag: true,
                                comment_type: CommentType.where(comment_type_name:
                                                        comment['comment_type_name']).first,
                                created_at: DateTime.parse(comment['created_at']),
                                app_user_id: -1,
                                commenter_name: comment['commenter_name']).first_or_create
      rescue ActiveRecord::RecordInvalid => e
        Rails.logger.error("Tried to create new AgencyDealComment, but couldn't. " \
                           "Errors: #{e.record.errors.full_messages}, comment JSON: #{comment}")
        raise
      rescue StandardError => e
        Rails.logger.error("Error creating AgencyDealComment. Errors: #{e.message}, comment JSON: #{comment}")
        raise
      end
    end
  end
end
