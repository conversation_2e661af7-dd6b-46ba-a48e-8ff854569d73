# frozen_string_literal: true

module Pubsub
  # Listens for messages coming in from `fanout` exchanges. Expects
  # `exchange_name` and `queue_name` to be before any work can be done
  # (probably in a constructor).
  module FanoutMessageListener
    include Messenger

    attr_reader :queue_name, :exchange_name

    protected

    def queue
      channel.fanout(exchange_name, durable: true)
      channel.queue(*incoming_queue_args)
    end

    def incoming_queue_args
      [queue_name,
       durable: true,
       arguments: { 'x-dead-letter-exchange' => "#{exchange_name}.dlx" }]
    end

    def ack_message(delivery_tag)
      Rails.logger.info("*********ACKNOWLEDGING MESSAGE #{delivery_tag} *********")
      channel.acknowledge(delivery_tag, false)
    end

    def subscribe(code)
      queue.subscribe(block: false, manual_ack: true) do |metadata, payload|
        code.call(metadata, payload)
      end
    end
  end
end
