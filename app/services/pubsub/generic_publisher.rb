# frozen_string_literal: true

module Pubsub
  # Publish Verticals to RabbitMQ
  class GenericPublisher < Publisher
    attr_reader :publishables

    def initialize(type_sym, publishables)
      super(type_sym)
      @publishables = publishables
    end

    def send
      if publishables.nil? || publishables.empty?
        Rails.logger.warn('Object to be published was either nil or empty. Doing nothing.')
        return
      end

      Rails.logger.info("Publishing #{publishables.count} #{type}")
      super(publishables.to_json)
    end

    # rubocop:disable Naming/MethodName
    def self.Publish(type_sym, publishables)
      GenericPublisher.new(type_sym, publishables).send
    end
    # rubocop:enable Naming/MethodName
  end
end
