# frozen_string_literal: true

module Pubsub
  class RpcClient
    include Pubsub::RpcBase

    attr_accessor :correlation_id, :event, :rpc_routing_key, :reply_queue, :result

    def initialize(rpc_routing_key)
      @event = Concurrent::Event.new
      @rpc_routing_key = rpc_routing_key
      @correlation_id = generate_uuid
    end

    # @param [opts] Hash of arguments to send to the remote as arguments to the procedure.
    # @param [block] Required. Gets executed after response from remote is received
    # @raise [ArgumentError] If no block provided
    # @return [Bunny::Exchange]
    def call(**opts, &)
      raise ArgumentError, 'Block required' unless block_given?

      setup_reply_queue(&)
      exchange.publish(opts.to_json,
                       content_type: 'application/vnd.api+json',
                       correlation_id:,
                       routing_key: rpc_routing_key,
                       reply_to: reply_queue.name)
      event.wait(5)
      result
    end

    private

    def generate_uuid
      SecureRandom.uuid
    end

    def setup_reply_queue(&)
      @reply_queue = channel.queue('', exclusive: true)
      reply_queue.subscribe do |_delivery_info, properties, payload|
        logger.debug "#{self.class.name}##{__method__}: properties: #{properties}"
        logger.debug "#{self.class.name}##{__method__}: payload: #{payload}"
        @result = if @correlation_id == properties[:correlation_id]
                    payload_hash = JSON.parse(payload).deep_symbolize_keys
                    yield(payload_hash) if payload_hash[:data]
                  end
        event.set
      end
    end
  end
end
