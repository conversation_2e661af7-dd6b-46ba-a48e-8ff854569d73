# frozen_string_literal: true

module Pubsub
  # Module containing methods common to communicating with RabbitMQ, regardless
  # of exchange type, etc.
  module Messenger
    include EmailNotifier

    attr_reader :exchange_name

    # @return [Bunny::Connection]
    def connection
      @connection ||= MarchHare.connect(uri: ENV['AMQP_URL'])
    rescue StandardError => e
      Rails.logger.error "Error opening connection to RabbitMQ: '#{e}'"
      Rails.logger.error("Error: #{e.message}")
      Rails.logger.debug(e.backtrace.join('\n'))
    end

    # @return [Bunny::Channel]
    def channel
      @channel ||= connection.create_channel
    end

    def reject_message(delivery_tag, error_message, payload)
      Rails.logger.error("*********REJECTING MESSAGE #{delivery_tag} *********")
      channel.reject(delivery_tag)
      notify_failure(error_message, payload)
    end
  end
end
