# frozen_string_literal: true

module Pubsub
  # Listens for messages coming in from RabbitMQ related to deals.
  # Exchange name: defaults to `agency.to.pam`, and should be a fanout.
  # Queue name: defaults to `agency.deals`.
  class DealsMessageListener
    include FanoutMessageListener
    include EmailNotifier

    def initialize(ex_name = 'agency.to.pam', queue_name = 'agency.deals')
      @exchange_name = ex_name
      @queue_name = queue_name
      Rails.logger.debug "#{self.class.name} initialized. exchange: '#{exchange_name}', queue: '#{queue_name}'"
    end

    def listen
      subscribe(lambda do |metadata, payload|
        Rails.logger.info('*********BEGIN PROCESSING MESSAGE*********')
        Rails.logger.debug("payload: #{payload}")
        @payload = payload
        @delivery_tag = metadata.delivery_tag
        validator = DealsMessageValidator.new(payload)
        return process_invalid unless validator.valid?

        @json_payload = validator.json_payload
        @file_header = FileHeader.create_for_ag(@json_payload['parent_agency_id'])
        process_message
        Rails.logger.info('*********END PROCESSING MESSAGE*********')
      end)
    end

    private

    def process_message
      status_error = @file_header.process_status == ProcessStatus.error
      ad_parser = AgencyDealsParser.new(@json_payload, @file_header)
      ad_parser.parse

      !status_error && ad_parser.valid? ? process_valid : process_invalid
    rescue StandardError => e
      process_error(e)
    end

    def process_valid
      @portal_notification = PortalNotification.new(@json_payload, @file_header.file_header_id)
      send_notifications
      ack_message(@delivery_tag)
    end

    def process_invalid
      error_message = 'There was an error when processing agency gateway deals. Message rejected.'
      reject_message(@delivery_tag, error_message, @payload)
      Rails.logger.error(error_message)
      Rails.logger.error(@file_header.filepath)
    end

    def process_error(error)
      reject_message(@delivery_tag, error.message, @payload)
      Rails.logger.error("ERROR IS:\n#{error.inspect}")
      Rails.logger.error("STACKTRACE:\n#{error.backtrace.join("\n")[0...4000]}")
    end
  end
end
