# frozen_string_literal: true

module Pubsub
  # Class to help validate deal messages
  class DealsMessageValidator
    include PamClient::Concerns::LoggerConcern

    attr_accessor :json_payload

    def initialize(payload)
      @payload = payload
    end

    def valid?
      return false unless (@json_payload = valid_payload_json?)

      valid_payload_structure?
    end

    def valid_payload_json?
      JSON.parse(@payload)
    rescue JSON::ParserError
      logger.warn('Message payload contained invalid JSON.')
      logger.debug("Message payload: '#{@payload}'")
      nil
    end

    def valid_payload_structure?
      keys = @json_payload.keys
      keys.include?('parent_agency_id') && keys.include?('agency_deals')
    end
  end
end
