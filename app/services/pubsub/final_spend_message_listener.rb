# frozen_string_literal: true

module Pubsub
  # Listens for messages related to a deal having been indicated as 'Final
  # Spend'
  # Exchange: Defaults to 'agency.final_spend'
  class FinalSpendMessageListener
    include DirectMessageListener
    include EmailNotifier

    def initialize(ex_name = 'agency.final_spend')
      @exchange_name = ex_name
      Rails.logger.debug "#{self.class.name} initialized. exchange_name: '#{@exchange_name}'"
    end

    # Expects payload to be JSON. Ex:
    #    "{\"agency_id\": 19, \"marketplace_id\": 1, \"budget_year\": 2019, \"final_spend\": true}"
    # All keys are required.
    def listen
      subscribe(lambda { |metadata, payload|
        begin
          Rails.logger.debug "#listen: exchange: #{exchange_name}, payload: '#{payload}'"
          amy = save_final_spend_status(payload)
          @portal_notification = PortalNotification.new(
            'parent_agency_id' => amy.agency.id,
            'budget_year' => amy.budget_year.fall_year,
            'marketplace_id' => amy.marketplace_id
          )
          send_final_spend_email
        rescue StandardError => e
          Rails.logger.error("Error: #{e.message}")
          Rails.logger.debug(e.backtrace.join('\n'))
          reject_message(metadata.delivery_tag, e.message, payload)
        end
      })
    end

    private

    def save_final_spend_status(payload)
      payload_hash = filter_payload(payload)
      validate_payload(payload_hash)
      find_or_create_amy(payload_hash).tap do |amy|
        amy.final_spend = payload_hash['final_spend']
        amy.save!
      end
    end

    def find_or_create_amy(payload)
      budget_year_id = BudgetYear.find_by(fall_year: payload['budget_year']).id
      PamClient::AgencyMarketplaceYear.find_or_create_by(agency_id: payload['agency_id'],
                                                         marketplace_id: payload['marketplace_id'],
                                                         budget_year_id:)
    end

    # Prevent arbitrary keys from being passed in
    def filter_payload(payload)
      slice_vals = %w[agency_id marketplace_id budget_year final_spend]
      payload_hash = JSON.parse(payload)
      payload_hash.slice(*slice_vals)
    end

    def validate_payload(payload_hash)
      # Already filtered, so just need to check size
      unless payload_hash.size == 4
        raise 'Invalid payload given to final spend message listener. ' \
              "Payload: #{payload_hash}"
      end

      true
    end
  end
end
