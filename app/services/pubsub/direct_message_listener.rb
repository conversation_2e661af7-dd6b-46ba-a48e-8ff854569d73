# frozen_string_literal: true

module Pubsub
  # Listens for messages coming in from `direct` (queueless) exchanges
  module DirectMessageListener
    include Messenger

    protected

    def exchange
      @exchange ||= channel.direct(exchange_name)
    end

    def queue
      @queue ||= channel.queue('', queue_args)
    end

    def queue_args
      { durable: true,
        exclusive: true,
        arguments: { 'x-dead-letter-exchange' => "#{exchange_name}.dlx" } }
    end

    def subscribe(code)
      queue.bind(exchange)
      queue.subscribe(block: false) do |payload, props, body|
        code.call(payload, props, body)
      ensure
        ActiveRecord::Base.connection_pool.disconnect!
      end
    end
  end
end
