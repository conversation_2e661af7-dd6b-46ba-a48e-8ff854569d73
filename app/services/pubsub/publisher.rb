# frozen_string_literal: true

module Pubsub
  # Sends messages via outgoing_exchange.
  # Exchange is given by `ENV['OUTGOING_DLQ_EXCHANGE']`.
  class Publisher
    include ::Pubsub::Messenger

    attr_reader :type

    # Create a new instance of Publisher
    # @param type [Symbol] the type of message we are sending to the queue
    def initialize(type)
      @type = type
    end

    # Defines class helper methods to initialize and send
    # Usage: `Pubsub::Publisher.deal(json_msg)`
    %i[budget_years comments deal deal_delete deal_headers marketplace new_deals prop_sync].each do |type|
      define_singleton_method type do |msg|
        new(type).send(msg)
      end
    end

    # Send a message to the RabbitMQ Message Queue
    # @param [String]
    def send(msg)
      Rails.logger.info("Sending #{type} updates to AG:")
      Rails.logger.info(msg)
      outgoing_exchange.publish([{ type: @type, msg: }].to_json)
    end

    private

    def outgoing_exchange
      @outgoing_exchange ||= channel.fanout(ENV['OUTGOING_FANOUT_NAME'], durable: true)
    end
  end
end
