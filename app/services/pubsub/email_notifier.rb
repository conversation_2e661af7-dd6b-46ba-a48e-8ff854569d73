# frozen_string_literal: true

module Pubsub
  # Sends email notifications to agency/portal/etc when various messages are
  # received from Agency Gateway/RabbitMQ
  module EmailNotifier
    attr_reader :portal_notification

    # Notify agency and portal management
    def send_notifications
      Rails.logger.info('*********SENDING EMAIL*********')
      notify_nbcu_portals
    rescue StandardError => e
      Rails.logger.error('NOTIFICATION ERRORED OUT')
      Rails.logger.error(e.message)
    end

    def send_final_spend_email
      to_addrs = to_addrs_for_portal_mgmt
      send_notification_email(:final_spend, to_addrs, portal_notification)
    end

    def notify_failure(error_message, payload)
      subject = "PAM-API Rabbit MQ Listener Error - #{Rails.env}"
      body = "<b>Error:</b> #{error_message}.<br /><b>Original Message:</b> #{payload}"
      DlqMailer.notify(subject, body).deliver_now!
    end

    private

    def notify_nbcu_portals
      to_addrs = to_addrs_for_portal_mgmt
      send_notification_email(:notify_portal_management, to_addrs, portal_notification)
    end

    def to_addrs_for_portal_mgmt
      AgPortalNotification.emails_for_portals(portal_notification)
    end

    # Sends email.
    #
    # @param method_sym One of the public methods in `AgencyGatewayNotificationMailer`
    # @param to_addrs Who to send the email to
    # @param agency_id (optional)
    def send_notification_email(method_sym, to_addrs, portal_notification = nil)
      if to_addrs.blank?
        Rails.logger.warn("Request to send notification emails received, but no
                          'to' addresses provided. Doing nothing. method_sym: #{method_sym},
                          to_addrs: #{to_addrs}")
        return false
      end
      args = [method_sym, to_addrs]

      begin
        args << portal_notification if portal_notification
        # TODO: ADD MAILER
        AgencyGatewayNotificationMailer.send(*args).deliver_now
      rescue SocketError
        Rails.logger.error("Could not connect to SMTP server. Check the
                           'config.action_mailer.smtp_settings[:address]' value
                           in the configuration for this environment to make
                           sure it is set and reachable.")
        return false
      rescue ArgumentError => e
        Rails.logger.error("Error sending email. Check that ENV['AGENCY_GATEWAY_ADDR'] is set.")
        Rails.logger.error(e)
        return false
      end

      true
    end
  end
end
