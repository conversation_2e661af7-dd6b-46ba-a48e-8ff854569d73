# frozen_string_literal: true

json.array! @budget_management_scenarios do |scenario|
  json.pacing_budget_id scenario.pacing_budget_id
  json.property_id scenario.property_id
  json.property_name scenario.property_name
  json.property_type_name scenario.property_type_name
  json.advertiser_id scenario.advertiser_id
  json.advertiser_name scenario.advertiser_name
  json.agency_id scenario.agency_id
  json.agency_name scenario.agency_name
  json.bae_app_user_id scenario.bae_app_user_id
  json.buying_ae_name scenario.buying_ae_name
  json.cae_app_user_id scenario.cae_app_user_id
  json.client_ae_name scenario.client_ae_name
  json.pae_app_user_id scenario.pae_app_user_id
  json.planning_ae_name scenario.planning_ae_name
  json.vertical_name scenario.vertical_name
  json.vertical_id scenario.vertical_id
  json.quarter_id scenario.quarter_id
  json.quarter scenario.quarter
  json.qtr_lock_status scenario.qtr_lock_status
  json.calendar_year_id scenario.calendar_year_id
  json.pacing_budget_status_id scenario.pacing_budget_status_id
  json.pacing_budget_status_name scenario.pacing_budget_status_name
  json.budget_dollars scenario.budget_dollars.to_i
  json.projection_dollars scenario.projection_dollars.to_i
  json.cy_cc_sales_dollars scenario.cy_cc_sales_dollars.to_i
  json.py_cc_sales_dollars scenario.py_cc_sales_dollars.to_i
  json.cy_working_dollars scenario.cy_working_dollars.to_i
  json.py_working_dollars scenario.py_working_dollars.to_i
  json.buying_ae_location_id scenario.buying_ae_location_id
  json.buying_ae_location_name scenario.buying_ae_location_name
  json.client_ae_location_id scenario.client_ae_location_id
  json.client_ae_location_name scenario.client_ae_location_name
  json.planning_ae_location_id scenario.planning_ae_location_id
  json.planning_ae_location_name scenario.planning_ae_location_name
  json.buying_tl_id scenario.buying_tl_id
  json.buying_tl_firstname scenario.buying_tl_firstname
  json.buying_tl_lastname scenario.buying_tl_lastname
  json.client_tl_id scenario.client_tl_id
  json.client_tl_firstname scenario.client_tl_firstname
  json.client_tl_lastname scenario.client_tl_lastname
  json.planning_tl_id scenario.planning_tl_id
  json.planning_tl_firstname scenario.planning_tl_firstname
  json.planning_tl_lastname scenario.planning_tl_lastname
  json.calendar_year scenario.calendar_year
  json.pacing_budget_detail_id scenario.pacing_budget_detail_id
  json.bae_split scenario.bae_split
  json.cae_split scenario.cae_split
  json.pae_split scenario.pae_split
  json.py_projection_dollars scenario.py_projection_dollars.to_i
  json.cy_cl_percentage scenario.cy_cl_percentage
  json.py_cl_percentage scenario.py_cl_percentage
  json.filterable_bae_app_user_id scenario.filterable_bae_app_user_id
  json.filterable_cae_app_user_id scenario.filterable_cae_app_user_id
  json.filterable_pae_app_user_id scenario.filterable_pae_app_user_id
  json.filterable_agency_id scenario.filterable_agency_id
  json.filterable_bae_location_id scenario.filterable_bae_location_id
  json.filterable_cae_location_id scenario.filterable_cae_location_id
  json.filterable_pae_location_id scenario.filterable_pae_location_id
  json.opportunity_dollars scenario.opportunity_dollars.to_i
end
