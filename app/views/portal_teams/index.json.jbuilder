# frozen_string_literal: true

json.array! @portal_teams do |portal_team|
  json.portal_team_id portal_team.id
  json.agency_id portal_team.agency_id
  json.agency_name portal_team.agency_name
  json.agency_sfdc_key portal_team.agency_sfdc_key
  json.agency_type portal_team.agency_type
  json.budget_year_id portal_team.budget_year_id
  json.budget_year_short_name portal_team.budget_year_short_name
  json.budget_year_sfdc_key portal_team.budget_year_sfdc_key
  json.marketplace_id portal_team.marketplace_id
  json.marketplace_name portal_team.marketplace_name
  json.marketplace_sfdc_key portal_team.marketplace_sfdc_key
  json.display_order portal_team.display_order
  json.portal_type_id portal_team.portal_type_id
  json.portal_type_name portal_team.portal_type_name
  json.app_user_id portal_team.app_user_id
  json.sso_id portal_team.user_sso_id
  json.app_user_name portal_team.user_name
  json.active portal_team.active
  json.update_agency portal_team.update_agency
  json.agency_gateway_notifications portal_team.agency_gateway_notifications
  json.unlock_request portal_team.unlock_request
  json.edit_stealth_mode portal_team.edit_stealth_mode
  json.created_at portal_team.created_at
  json.updated_at portal_team.updated_at
end
