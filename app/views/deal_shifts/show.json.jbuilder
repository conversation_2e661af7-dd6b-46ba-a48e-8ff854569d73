# frozen_string_literal: true

json.call(@deal_shift, *@deal_shift.attributes.keys)
json.shift_type_name @deal_shift.shift_type_name
json.advertiser_name @deal_shift.advertiser_name
json.agency_name @deal_shift.agency_name
json.property_name @deal_shift.property_name
json.marketplace_name @deal_shift.marketplace_name
json.requested_by_name @deal_shift.requested_by.full_name
json.responder_name @deal_shift.responder&.full_name
json.deal_shift_option_name @deal_shift.deal_shift_option.name
json.pillar_id @deal_shift.deal.property.pillar_id
json.pillar_name @deal_shift.deal.property.pillar&.name
if @deal_shift.children.empty?
  json.shift_to_name @deal_shift.shift_to_name
else
  json.shift_to_name [@deal_shift, @deal_shift.children].flatten.map { |deal_shift|
    "#{deal_shift.shift_type_name.gsub('Account Executive', 'AE')}: #{deal_shift.shift_to_name}"
  }.join(', ')
end
json.guid_recipient_name @deal_shift_guid&.user&.full_name
json.comments do
  json.array! (@deal_shift.parent || @deal_shift).deal_shift_comments.order(:updated_at) do |comment|
    json.call(comment, :commentary_id, :app_user_id, :message, :created_at)
    json.commenter_name comment.commenter_name || comment.user.full_name
  end
end
