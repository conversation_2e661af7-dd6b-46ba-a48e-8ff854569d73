# frozen_string_literal: true

# Job to process external deal records from CDW
# Parquet files are transformed to CSV via the cdwpam-parquet-to-csv lambda. After
# transformation, the lambda calls PAM-API (api/cdw/external_deals_import) to
# execute this job. The CSV is parsed and inserted into the `cdwin_stage` table.
# Upon completion, the `PKG_CDWIN_ETL.PRC_ETL_CDWIN` package is invoked.
#
# Sample CSV File Name = cdw_pam_detail_vw_all_06-06-2023
# CSV Bucket Path: adsales-customapps-dev/[env]/pam/cdw
# Source Parquet Bucket Path: adsales-tech-platform-linear-[env]/pam_extract
class ImportCdwExternalDealsJob < ApplicationJob
  attr_accessor :today_formatted, :rows_to_insert, :file_header, :batch_header, :cdw_stage_count_before,
                :cdw_record_count

  CDW_FILE_PREFIX = 'cdw_pam_detail_vw_all_'

  # rubocop:disable Metrics/AbcSize
  def perform
    if already_processed_today?
      Rails.logger.info("CDW Parquet files already parsed for #{today_formatted}")
    else
      # Sample CDW file name: cdw_pam_detail_vw_all_25-06-2023
      @today_formatted = DateTime.now.in_time_zone('Eastern Time (US & Canada)').strftime('%d-%m-%Y')
      Rails.logger.info("Processing CDW Parquet files for #{today_formatted}")
      begin
        if (csv_file_key = s3_file_key)
          create_headers
          Rails.logger.info("Parsing CDW file: #{csv_file_key}")
          csv_string = read_s3_object(csv_file_key)
          @rows_to_insert = CSV.new(csv_string, headers: true, skip_blanks: true).map(&:to_h)
          @cdw_record_count = rows_to_insert.size
          @cdw_stage_count_before = PamClient::CdwStage.count
          Rails.logger.info("CDW row to insert size: #{@cdw_record_count}")
          insert_rows
          return unless ENV['ENABLE_EXTERNAL_DEAL_INSERT'] == 'true'

          execute_etl
          notify_success
        else
          Rails.logger.info("No CDW CSV files found for #{today_formatted}")
        end
      rescue StandardError => e
        Rails.logger.error('Error processing external deals')
        Rails.logger.error e.backtrace[0..15].join("\n\t")
        Rails.logger.error "#{e.class}: #{e.message}"
        beatch_header&.update(process_status: ProcessStatus.error)
        file_header&.update(process_status: ProcessStatus.error)
        notify_failure(e.message, e.backtrace)
      end
    end
  end
  # rubocop:enable Metrics/AbcSize

  private

  def create_headers
    @batch_header = BatchHeader.create!(feed_type: FeedType.cdw_import,
                                        process_status: ProcessStatus.loading)
    @file_header = FileHeader.create!(
      batch_header:,
      process_status: ProcessStatus.loading,
      filename: 'CDW',
      filepath: "CDW_#{Rails.env}",
      fileostmstmp: DateTime.now
    )
  end

  def insert_rows
    while (row = rows_to_insert.shift)
      begin
        row.transform_keys!(&:to_s).transform_keys!(&:downcase).transform_values! { |v| v&.strip }
        cdwin_stage_id = PamClient::CdwStage.maximum(:cdwin_stage_id) + 1
        insert_values = row.slice(*PamClient::CdwStage.column_names)
                           .merge(cdwin_stage_id:,
                                  file_header_id: file_header.id,
                                  ae_name: row['buying_ae_name'],
                                  guarantee_flag: row['guarantee_ind'],
                                  src_deal_type_cd: row['deal_type_cd'],
                                  sfdc_opportunity_id: row['opportunity_id'],
                                  sfdc_digital_partner: row['sfc_digital_partner'],
                                  sfdc_verticals: row['sfc_verticals'])
        PamClient::CdwStage.create!(insert_values)
      rescue StandardError => e
        Rails.logger.error("Unable to insert external deal: #{row}")
        Rails.logger.error e.backtrace[0..15].join("\n\t")
        Rails.logger.error "#{e.class}: #{e.message}"
        next
      end
    end
    Rails.logger.info('Successfully imported external deals')
  end

  def execute_etl
    # rubocop:disable Layout/LineLength
    etl_sql = "DECLARE success NUMBER; BEGIN PKG_CDWIN_ETL.PRC_ETL_CDWIN(#{file_header.id},#{batch_header.id},success); END;"
    # rubocop:enable Layout/LineLength
    ActiveRecord::Base.connection.execute(etl_sql)
    file_header.update(process_status: ProcessStatus.success)
    batch_header.update(process_status: ProcessStatus.success)
    Rails.logger.info('Successfully processed imported external deals')
  end

  def notify_success
    CdwExternalDealsMailer.with(file_header_id: file_header.id,
                                batch_header_id: batch_header.id,
                                cdw_record_count:,
                                cdw_stage_count_before:,
                                cdw_stage_count_after: PamClient::CdwStage.count).notify_success.deliver_later
  end

  def notify_failure(error_message, backtrace)
    CdwExternalDealsMailer.with(error_message:, backtrace:).notify_failure.deliver_later
  end

  def s3_file_key
    s3_bucket.objects.select do |s3_object|
      return s3_object.key if s3_object.key.include?("#{CDW_FILE_PREFIX}#{@today_formatted}")
    end&.first
  end

  def read_s3_object(object_key)
    s3_bucket.object(object_key).get.body.read
  rescue Aws::S3::Errors::NoSuchKey
    Rails.logger.error("Unable to read S3 object for #{object_key}")
  end

  def s3_bucket
    s3 ||= Aws::S3::Resource.new({ region: 'us-east-1' })
    s3.bucket(ENV['CDW_EXTERNAL_DEAL_S3_BUCKET_NAME'])
  end

  def already_processed_today?
    FileHeader.where(filename: 'CDW', process_status: ProcessStatus.success)
              .where("trunc(fileostmstmp) = TO_DATE(?,'dd-MON-yy')", DateTime.now.strftime('%d-%^b-%y')).any?
  end
end
