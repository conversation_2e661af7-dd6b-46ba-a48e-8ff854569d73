# frozen_string_literal: true

class SalesforceSyncJob < ApplicationJob
  queue_as :"#{ENV['RAILS_ENV']}-pam-api-salesforce"

  # Class variable ensures single mutex across all job instances
  # rubocop:disable Style/ClassVars
  @@sync_mutex = Mutex.new
  # rubocop:enable Style/ClassVars

  def perform
    if @@sync_mutex.try_lock
      begin
        Rails.logger.info("SalesforceSyncJob: Starting sync process (PID: #{Process.pid})")
        Salesforce::Sync::MainLogic.new.run
        Rails.logger.info('SalesforceSyncJob: Sync completed successfully')
      rescue StandardError => e
        Rails.logger.error("SalesforceSyncJob: Sync failed - #{e.class}: #{e.message}")
        Rails.logger.error("Backtrace: #{e.backtrace.first(10).join("\n")}")
        # Re-raise if you want Sidekiq to retry failed jobs
        raise
      ensure
        @@sync_mutex.unlock
        Rails.logger.info('SalesforceSyncJob: Lock released')
      end
    else
      Rails.logger.info('SalesforceSyncJob: Sync already running, skipping execution')
    end
  end
end
