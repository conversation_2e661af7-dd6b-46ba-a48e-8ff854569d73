# frozen_string_literal: true

class CaeAssignmentsJob < ApplicationJob
  def perform
    copy_current_assignments_to_cae
    remove_expired_assignments_from_cae
  end

  private

  def copy_current_assignments_to_cae
    PamClient::CoveringAccountExecutive.in_range.each do |cae|
      cae.covering_for.assignments.each do |assignment|
        copied_assignment = assignment.slice(*assignable_attributes)
        existing_assignment = cae.covering_ae.assignments.where(copied_assignment)
        if existing_assignment.exists?
          existing_assignment.update(expires_on: cae.end_date)
        else
          cae.covering_ae.assignments << Assignment.new(copied_assignment.merge(expires_on: cae.end_date))
        end
      end
    end
  end

  def remove_expired_assignments_from_cae
    PamClient::Assignment.expired.delete_all
  end

  def assignable_attributes
    PamClient::Assignment.column_names - %w[assignment_id app_user_id created_at updated_at last_updated_by_id
                                            expires_on]
  end
end
