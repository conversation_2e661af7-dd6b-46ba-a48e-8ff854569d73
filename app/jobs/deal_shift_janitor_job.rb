# frozen_string_literal: true

class DealShiftJanitor<PERSON>ob < ApplicationJob
  def perform
    remind_pending_deal_shifts
    reject_stale_deal_shifts
  end

  private

  def remind_pending_deal_shifts
    Rails.logger.info('Sending pending deal_shift reminders')

    pending_deal_shifts.each do |deal_shift|
      Api::DealShiftMailerService.new(deal_shift:).remind_requestee_shift_created
      deal_shift.update_attribute(:reminded_at, Time.now)
    rescue StandardError => e
      Rails.logger.debug(e.message)
    end
  end

  def reject_stale_deal_shifts
    Rails.logger.info('Rejecting stale deal shifts')

    stale_deal_shifts.each do |deal_shift|
      deal_shift.reject!
      Api::DealShiftMailerService.new(deal_shift:).notify_requester_shift_auto_rejected
    rescue StandardError => e
      Rails.logger.debug(e.message)
    end
  end

  def pending_deal_shifts
    DealShift.pending.left_joins(:parent)
             .where(reminded_at: nil)
             .where('nvl(parent_deal_shift.date_disposed, deal_shift.created_at) <= ?', Time.now - 2.days)
             .where('deal_shift.parent_deal_shift_id is null or parent_deal_shift.approved = 1')
  end

  def stale_deal_shifts
    DealShift.pending.left_joins(:parent)
             .where.not(reminded_at: nil).where('deal_shift.reminded_at <= ?', Time.now - 2.days)
             .where('deal_shift.parent_deal_shift_id is null or parent_deal_shift.approved = 1')
  end
end
