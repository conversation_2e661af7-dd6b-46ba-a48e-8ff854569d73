# frozen_string_literal: true

class LocksAndSplitsRefreshJob < ApplicationJob
  SPLITS_CACHE_KEY_BASE = 'locks_and_splits/'

  def perform
    Budget.for_year(BudgetYear.current_year).each do |budget|
      cache_key = "#{SPLITS_CACHE_KEY_BASE}#{budget.id}"
      Rails.cache.delete(cache_key)

      Rails.cache.fetch(cache_key, expires: 24.hours) do
        [budget.id, PamClient::SplitterInterface.new(budget, budget.prior_year_budget).get]
      end
    end
  end
end
