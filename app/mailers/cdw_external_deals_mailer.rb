# frozen_string_literal: true

class CdwExternalDealsMailer < ApplicationMailer
  include ActionView::Helpers::NumberHelper

  default from: ENV['PAM_SUPPORT_EMAIL']

  def notify_success
    @file_header_id = params[:file_header_id]
    @batch_header_id = params[:batch_header_id]
    @cdw_record_count = params[:cdw_record_count]
    @cdw_stage_count_before = params[:cdw_stage_count_before]
    @cdw_stage_count_after = params[:cdw_stage_count_after]
    import_percentage = (@cdw_stage_count_after - @cdw_stage_count_before) / @cdw_record_count.to_f * 100
    @formatted_import_percentage = number_to_percentage(import_percentage, precision: 0)

    mail(to: ENV['EXTERNAL_DEAL_IMPORT_SUCCESS_EMAIL'],
         subject: "#{Rails.env}:: Job Status Notification - I-2000-CDW -#{@formatted_import_percentage}")
  end

  def notify_failure
    @error_message = params[:error_message]
    @backtrace = params[:backtrace]

    mail(to: ENV['EXTERNAL_DEAL_IMPORT_FAILURE_EMAIL'],
         subject: "CDW External Deal Import Failure - #{Rails.env}")
  end
end
