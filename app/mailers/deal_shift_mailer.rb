# frozen_string_literal: true

class DealShiftMailer < ApplicationMailer
  default from: ENV['PAM_SUPPORT_EMAIL'], bcc: ENV['DEAL_SHIFT_SUPPORT']

  before_action :fetch_shift_info

  def notify_old_agency_shift_created
    mail(to: @user.email, subject: new_deal_shift_subject)
  end

  def notify_old_agency_shift_updated
    @status =
      if @deal_shift.approved?
        'approved'
      elsif @deal_shift.rejected?
        'rejected'
      else
        raise MailerError, "Invalid deal shift status #{@deal_shift.id}"
      end

    mail(to: @user.email, subject: updated_deal_shift_subject)
  end

  def notify_requestee_shift_created
    mail(to: @user.email, subject: new_deal_shift_subject)
  end

  def remind_requestee_shift_created
    raise MailerError, "Deal Shift #{@deal_shift.id} is not pending" unless @deal_shift.pending?

    subject = "Pam Registrations: Pending Deal Shift Request Reminder ##{@deal_shift.id}"
    mail(to: @user.email, subject:)
  end

  def notify_requester_shift_approved
    raise MailerError, "Deal Shift #{@deal_shift.id} is not approved" unless @deal_shift.approved?

    mail(to: @deal_shift.requested_by.email, subject: updated_deal_shift_subject)
  end

  def notify_requester_shift_rejected
    raise MailerError, "Deal Shift #{@deal_shift.id} is not rejected" unless @deal_shift.rejected?

    mail(to: @deal_shift.requested_by.email, subject: updated_deal_shift_subject)
  end

  def notify_requester_shift_auto_rejected
    raise MailerError, "Deal Shift #{@deal_shift.id} is not rejected" unless @deal_shift.rejected?

    subject = "Pam Registrations: Deal Shift Auto Rejected ##{@deal_shift.id}"

    mail(to: @deal_shift.requested_by.email, subject:)
  end

  def notify_missing_portals
    @error_message = params[:error_message]
    mail(to: ENV['DEAL_SHIFT_SUPPORT'], subject: 'Failed Shift Request')
  end

  private

  def updated_deal_shift_subject
    "PAM Registrations: Deal Shift Request Status Update ##{@deal_shift.id}"
  end

  def new_deal_shift_subject
    "PAM Registrations: Deal Shift Request ##{@deal_shift.id}"
  end

  def fetch_shift_info
    @deal_shift = params[:deal_shift]
    @user = params[:user]
    @shift_comments = (@deal_shift.parent || @deal_shift).deal_shift_comments.order(:updated_at) if @deal_shift
    @deal_shift_guid = @deal_shift.create_guid!(@user.app_user_id) if @deal_shift && @user
  end

  helper do
    def shift_option_text
      case @deal_shift.deal_shift_option
      when DealShiftOption.single_deal
        'Single Deal'
      when DealShiftOption.portfolio
        "All deals for #{@deal_shift.advertiser_name} out of #{@deal_shift.agency_name}"
      when DealShiftOption.pillar
        "All deals for #{@deal_shift.deal.property.pillar&.name}"
      end
    end

    def shift_to_name
      if @deal_shift.children.empty?
        @deal_shift.shift_to_name
      else
        [@deal_shift, @deal_shift.children].flatten.map do |deal_shift|
          "#{deal_shift.shift_type_name.gsub('Account Executive', 'AE')}: #{deal_shift.shift_to_name}"
        end.join(', ')
      end
    end
  end

  class MailerError < StandardError; end
end
