# frozen_string_literal: true

class TeamMemberMailer < ApplicationMailer
  default from: ENV['PAM_SUPPORT_EMAIL']

  def notify_team_member_change
    set_users
    recipient_emails = [@user, @from_manager, @to_manager].compact.map(&:email)
    subject = 'Team Member Added'
    set_descriptions
    mail(to: recipient_emails, subject:) if recipient_emails.any?
  end

  private

  def set_users
    @user = params[:user]
    from_manager_id, to_manager_id = params[:manager_id_change]
    @from_manager = User.find_by(app_user_id: from_manager_id)
    @to_manager = User.find_by(app_user_id: to_manager_id)
  end

  def set_descriptions
    user_fields = [@user&.sso_id, @user&.email, @user&.app_user_title&.app_user_title_name].compact.join(', ')
    @user_description = " - #{user_fields}" unless user_fields.empty?
    @manager_description = " - #{@to_manager.email}" if @to_manager.email
  end
end
