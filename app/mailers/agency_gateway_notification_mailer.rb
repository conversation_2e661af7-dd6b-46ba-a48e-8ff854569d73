# frozen_string_literal: true

# Builds emails to send to agency/portal management team when deals have been
# updated via the Agency Gateway
class AgencyGatewayNotificationMailer < ActionMailer::Base
  default from: ENV['AGENCY_GATEWAY_ADDR']
  helper AgEmailNotificationHelper
  layout 'mailer'

  # Notify agency that Gateway submission was successful
  #
  # @param to To addresses to send notification to
  def notify_agency(to_addrs)
    mail(to: to_addrs, subject: 'Agency Gateway Submission Successful')
  end

  # Notify agency that changes were submitted to the Gateway
  #
  # @param to To addresses to send notification to
  def changes_submitted_to_gateway(to_addrs)
    mail(to: to_addrs, subject: 'NBCU Changes Submitted')
  end

  # Notify portal members that changes were submitted to the Gateway
  #
  # @param to To addresses to send notification to
  # @param agency
  def notify_portal_management(to_addrs, portal_notification)
    begin
      @parent_agency = ParentAgency.find(portal_notification.parent_agency_id)
      @file_header_id = portal_notification.file_header_id
      @agency_name = @parent_agency.agency_name
    rescue ActiveRecord::RecordNotFound
      logger.error("Could not find agency with id #{@parent_agency.id}.")
      logger.error('Not sending notification email.')
      return false
    end

    @subject = "Agency Gateway changes submitted for #{@agency_name}"
    mail(to: to_addrs, subject: @subject)
  end

  # Notify that Final Spend was set
  #
  # @param to_addrs To addresses to send email to
  # @param agency Agency which submitted Final Spend indication
  def final_spend(to_addrs, portal_notification)
    begin
      @budget_year_id = portal_notification.budget_year_id
      @parent_agency_id = portal_notification.parent_agency_id
      @short_yr = BudgetYear.prior_year(BudgetYear.find(@budget_year_id)).short_name
      @parent_agency = ParentAgency.find(@parent_agency_id)
    rescue ActiveRecord::RecordNotFound
      logger.error('Could not find object.')
      logger.error("Was looking for agency id #{@parent_agency_id}.")
      logger.error("Was looking for budget year id #{@budget_year_id}.")
      logger.error('Not sending notification email.')
      return false
    end
    @subject = "Agency Gateway: #{@short_yr} Upfront Spend approved for #{@parent_agency.name}"
    @msg_body = "#{@parent_agency.name} has approved the #{@short_yr} Upfront Spend. " \
                'Feel free to pull the Agency Gateway Variance Report to view final numbers.'

    mail(to: to_addrs, subject: @subject, body: @msg_body)
  end
end
