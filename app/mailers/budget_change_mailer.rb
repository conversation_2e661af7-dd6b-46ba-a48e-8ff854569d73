# frozen_string_literal: true

class BudgetChangeMailer < ApplicationMailer
  default from: ENV['PAM_SUPPORT_EMAIL']

  def notify_prompt_zero
    emails = params[:budget].agency_partners.pluck(:email).compact
    return if emails.empty?

    Rails.logger.info('Sending Deal Status Change Notification Emails')
    set_variables

    mail(to: emails, subject: 'Deal Status Update')
  end

  private

  def set_variables
    @budget = params[:budget]
    @deal = @budget.deal
    @actual_changes = calculate_delta_for(:actual)
    @projected_changes = calculate_delta_for(:projected)
  end

  def calculate_delta_for(type)
    {
      before: sum_quarters(type, :first),
      after: sum_quarters(type, :last)
    }
  end

  def sum_quarters(type, position)
    changes = params[:budget].previous_changes
    %i[prequarter quarter1 quarter2 quarter3 quarter4 postquarter]
      .map { |quarter| "#{type}_#{quarter}_amount" }
      .inject(0) do |sum, attribute|
        sum + changes.fetch(attribute, [@budget.send(attribute)]).send(position)
      end
  end
end
