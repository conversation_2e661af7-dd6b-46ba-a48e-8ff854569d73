# frozen_string_literal: true

module AgEmailNotificationHelper
  def ag_last_submission_changes_table(parent_agency, file_header_id)
    data = AgChangeHistory.for_parent_agency(parent_agency, file_header_id)
    xml = Builder::XmlMarkup.new(indent: 2)
    xml.table do
      build_table_header(xml, data[0])
      build_table_body(xml, data)
    end
    xml
  end

  def build_table_header(xml, header_row)
    return unless header_row

    xml.tr('align' => 'left') do
      header_row.each_key { |key| xml.th(key.to_s.humanize) }
    end
    xml
  end

  def build_table_body(xml, data)
    data.each do |row|
      xml.tr do
        row.each do |key, value|
          string_value = value.to_s
          if %i[old_value new_value].include?(key)
            xml.td(string_value, 'align' => 'right')
          else
            xml.td(string_value)
          end
        end
      end
    end
    xml
  end
end
