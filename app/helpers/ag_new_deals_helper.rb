# frozen_string_literal: true

# Helper to create new deals
module AgNewDealsHelper
  def default_user_id
    User.find_by(first_name: 'A<PERSON>', last_name: 'User').id
  end

  def deal_property(property_id)
    Property.find_by(property_id:)
  end

  def default_brand(brand_id)
    brand_id || -1
  end

  def default_sales_type(sales_type_id, property_id)
    sales_type_id || deal_sales_type(property_id)
  end

  def default_vertical(property_id)
    deal_property(property_id)&.vertical_id
  end

  def fetch_aae(filter_params)
    AorAeAssignment.find_by(filter_params)
  end

  def deal_sales_type(property_id)
    check_if_digital?(property_id) ? digital_sales_type_id : -1
  end

  def check_if_digital?(property_id)
    deal_property(property_id)&.property_type == PropertyType.digital_type
  end

  def digital_sales_type_id
    SalesType.find_by(sales_type_name: 'Digital Video').id
  end
end
