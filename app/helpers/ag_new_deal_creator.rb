# frozen_string_literal: true

# Utility class for creating AG new deals
class AgNewDealCreator
  include AgNewDealsHelper
  include PamClient::Concerns::LoggerConcern

  attr_reader :deal_params, :properties, :budget_year, :agency_deals, :errors

  def initialize(params)
    @properties = params['properties']
    @deal_params = params.except('properties', 'budget_year', 'object_id')
    @budget_year = BudgetYear.find_by(fall_year: params['budget_year'])
    @errors = []
    @agency_deals = { object_id: params['object_id'], deals: [],
                      failed_deals: { properties: [], errors: [] } }
  end

  def create
    properties.each do |property_params|
      params = build_deal_attributes(property_params)
      deal = Deal.new(params)
      unless deal.valid? && budget_year
        collect_invalid_deals(deal, property_params['property_id'])
        next
      end
      create_new_deals(deal)
    end
    agency_deals
  end

  private

  def create_new_deals(deal)
    Deal.transaction do
      deal.save
      PamClient::BudgetUpdater.create_budgets(deal, [budget_year])
    end
    agency_deals[:deals] << ag_deals(deal)
    agency_deals[:deals].flatten!
  end

  def collect_invalid_deals(deal, property_id)
    agency_deals[:failed_deals][:properties] << property_id
    add_errors(deal, property_id)
  end

  def add_errors(deal, property_id)
    logger.warn("Tried to create an invalid new deal for property_id: #{property_id}. Skipping.")
    logger.warn("Validation errors: #{deal.errors.to_a}")
    agency_deals[:failed_deals][:errors] << deal.errors.full_messages
    errors << deal.errors.full_messages
  end

  def ag_deals(deal)
    AgencyGatewayView.data(marketplace_id: deal_params['marketplace_id'], deal_id: deal.deal_id)
  end

  def build_deal_attributes(property_params)
    property_id = property_params['property_id']
    brand_id = deal_params['brand_id']
    sales_type_id = deal_params['sales_type_id']
    filter_params = aae_attributes(property_id)
    aae = fetch_aae(filter_params)
    deal_params.merge(property_params)
               .merge(brand_id: default_brand(brand_id))
               .merge(sales_type_id: default_sales_type(sales_type_id, property_id))
               .merge(vertical_id: default_vertical(property_id))
               .merge(other_attributes(aae))
  end

  def other_attributes(assigned_ae)
    {
      category_id: default_category,
      app_user_id: assigned_ae&.bae_app_user_id || default_user_id,
      cae_app_user_id: assigned_ae&.cae_app_user_id || default_user_id,
      deal_tag_id: -1,
      multi_year: 0,
      bycal: 0,
      source: 'AG'
    }
  end

  def default_category
    Advertiser.find_by(advertiser_id: deal_params['advertiser_id'])&.default_category_id
  end

  def aae_attributes(property_id)
    {
      agency_id: deal_params['agency_id'],
      advertiser_id: deal_params['advertiser_id'],
      property_id:
    }
  end
end
