# frozen_string_literal: true

class BudgetManagementScenario < ActiveRecord::Base
  belongs_to :buying_ae, class_name: 'User', foreign_key: 'bae_app_user_id'
  belongs_to :client_ae, class_name: 'User', foreign_key: 'cae_app_user_id'
  belongs_to :planning_ae, class_name: 'User', foreign_key: 'pae_app_user_id'

  scope :current_year, -> { where(calendar_year_id: CalendarYear.current_calendar_year.id) }
  scope :for_user, lambda { |user, buying_ae_only|
    if buying_ae_only == 'true'
      where(buying_ae: user)
    else
      where(buying_ae: user).or(where(client_ae: user)).or(where(planning_ae: user))
    end
  }
end
