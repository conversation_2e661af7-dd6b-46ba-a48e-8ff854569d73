# frozen_string_literal: true

class PortalTeam < PamClient::PortalTeam
  scope :join_system_key, lambda { |table_name, key_type_id|
    joins(%{left join system_key_asc #{table_name}_asc
      on to_char(portal_team.#{table_name}_id) = #{table_name}_asc.pam_key
      and #{table_name}_asc.EXTERNAL_SYSTEM_ID = #{sfdc_system_id}
      and #{table_name}_asc.EXTERNAL_KEY_TYPE_ID = #{key_type_id}
    })
  }

  class << self
    def system_key_joins
      join_system_key('agency', id_for_system_key_type(name: 'Agency'))
        .join_system_key('marketplace', id_for_system_key_type(name: 'MarketPlace'))
        .join_system_key('budget_year', id_for_system_key_type(name: 'BudgetYear'))
    end

    def sfdc_system_id
      @sfdc_system_id ||= ExternalSystem.find_by(name: 'SFDC')&.id || -1
    end

    private

    def id_for_system_key_type(name:)
      external_key_types.find { |key_type| key_type.external_key_type_name == name }&.id || -1
    end

    def external_key_types
      @external_key_types ||= ExternalKeyType.where(name: %w[Agency MarketPlace BudgetYear]).select(
        :external_key_type_name, :external_key_type_id
      )
    end
  end
end
