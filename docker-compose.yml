version: '3'
services:
  web:
    build: .
    container_name: pam-api_app
    command: rails server -p 3001 -b '0.0.0.0'
    environment:
      SMS_DB_HOST: host.docker.internal
      SMS_API_HOST: host.docker.internal
      AGENCY_GATEWAY_ADDR: <EMAIL>
      PAM_SUPPORT_EMAIL: <EMAIL>
      OUTGOING_DLQ_EXCHANGE: pam.to.agency.dlx
      OUTGOING_FANOUT_NAME: pam.to.agency
      INCOMING_FANOUT_NAME: agency.to.pam
      INCOMING_DEALS_QUEUE_NAME: agency.deals
      INCOMING_FINAL_SPEND_EX: agency.final_spend
      INCOMING_NEW_DEAL_FANOUT_NAME: agency.new_deals.to.pam
      INCOMING_NEW_DEALS_QUEUE_NAME: agency.new_deals
      AMQP_URL: localhost
      AMQP_PORT: 5672
      AMQP_USER: guest
      AMQP_PWD: guest
    volumes:
      - ./app:/pam-api/app
      - ./config:/pam-api/config
      - ./spec:/pam-api/spec
      - ./lib:/pam-api/lib
      - ./vendor:/pam-api/vendor
      - ./Gemfile:/pam-api/Gemfile
      - ./Gemfile.lock:/pam-api/Gemfile.lock
    ports:
      - "3001:3001"
    tty: true
    stdin_open: true
