# frozen_string_literal: true

require 'spec_helper'
require 'support/shared_context/ag_new_deal_data'

module PamClient
  module AgencyGateway
    describe AgNewDealCreator do
      shared_context 'deal params' do
        let(:budget_year) { create(:budget_year, fall_year: Time.now.year) }
        let(:deal_params) do
          <<-STR
          {
            "agency_id": #{agency.id},
            "advertiser_id": #{advertiser.id},
            "brand_id": #{brand.id},
            "budget_year": #{budget_year.fall_year},
            "demographic_id": #{demographic.id},
            "marketplace_id": #{marketplace.id},
            "rating_stream_id": #{rating_stream.id},
            "properties": [
              {
                "property_id": #{property_1.id},
                "deal_name": "Test Deal 1"
              },
              {
                "property_id": #{property_1.id},
                "deal_name": "Test Deal 2"
              },
              {
                "property_id": #{property_3.id},
                "deal_name": "Digital Deal 1"
              }
            ]
          }
          STR
        end

        let(:params) { JSON.parse(deal_params) }
        let(:ag_new_deal_creator) { AgNewDealCreator.new(params) }
      end

      before(:each) do
        Deal.destroy_all
        create_default_user
        create_sponsorship_type
        create_digital_sales_type
      end

      describe '#create' do
        include_context 'deal params'
        include_context 'ag new deal data'

        context 'creates deal and current year budget' do
          it 'creates a deal' do
            ag_new_deal_creator.create
            expect(Deal.all.count).to eq(3)
            expect(Deal.first.advertiser_id).to eq(advertiser.id)
            expect(Deal.first.advertiser_brand_id).to eq(brand.id)
            expect(Deal.first.rating_stream_id).to eq(rating_stream.id)
          end
        end

        context 'given a parentless agency' do
          let(:parentless_params) do
            params.merge('agency_id' => agency_parentless.id)
          end

          subject { AgNewDealCreator.new(parentless_params) }

          it 'creates a deal' do
            subject.create
            expect(Deal.all.count).to eq(3)
          end
        end

        context 'given an agency with a grandparent' do
          let(:child_with_grandparent) do
            pa = grandparent_agency_with_children.parent_agencies.first
            create(:agency, parent_agency_id: pa.agency_id)
          end
          let(:grandparent_params) do
            params.merge('agency_id' => child_with_grandparent.id)
          end

          subject { AgNewDealCreator.new(grandparent_params) }

          it 'creates a deal' do
            subject.create
            expect(Deal.all.count).to eq(3)
          end
        end
      end
    end
  end
end
