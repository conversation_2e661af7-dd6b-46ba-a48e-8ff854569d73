# frozen_string_literal: true

require 'rails_helper'

RSpec.describe MstrConnectionMailer, type: :mailer do
  context 'Sending failed MSTR emails' do
    let!(:pam_support_address) { ENV['PAM_SUPPORT_EMAIL'] }
    let!(:subject) { '(test) [pam-api] MSTR Report failed to send' }
    let!(:user) do
      create(:user, sso_id: '123456', first_name: 'Test', last_name: 'User', email: '<EMAIL>')
    end

    before do
      response = HTTP::Message.new_response('test mstr response body')
      MstrConnectionMailer.with(user:, mstr_response: response).notify_failed_report.deliver_now
    end

    before :each do
      @message = ActionMailer::Base.deliveries.last
      @body = @message.body.to_s
    end

    it 'should have the right to and from addresses' do
      expect(@message.to.first).to eq(pam_support_address)
      expect(@message.from.first).to eq(pam_support_address)
    end

    it 'should cc the user who tried to run the report' do
      expect(@message.cc.first).to eq(user.email)
    end

    it 'should include correct subject' do
      expect(@message.subject).to eq(subject)
    end

    it 'should have the user sso in the body' do
      expect(@body).to include('123456')
    end

    it 'should have the user name the mail body' do
      expect(@body).to include('Test User')
    end

    it 'should contain the mstr response body in the email' do
      expect(@body).to include('test mstr response body')
    end
  end
end
