# frozen_string_literal: true

require 'spec_helper'

RSpec.describe MstrConnectionMailer, type: :mailer do
  let(:to_addresses) { ['<EMAIL>', '<EMAIL>'] }
  let(:from_address) { [ENV['AGENCY_GATEWAY_ADDR']] }

  context 'agency notifications' do
    context 'message is structured correctly for agency after receiving changes' do
      let(:subject) { '(test) Agency Gateway Submission Successful' }

      before do
        AgencyGatewayNotificationMailer.notify_agency(to_addresses).deliver_now
      end

      it 'should send to the right addresses' do
        expect(ActionMailer::Base.deliveries.last.to).to eq(to_addresses)
      end

      it 'should be from the right addresses' do
        expect(ActionMailer::Base.deliveries.last.from).to eq(from_address)
      end

      it 'should have right subject' do
        expect(ActionMailer::Base.deliveries.last.subject).to eq(subject)
      end
    end

    context 'message is structured correctly for agency after sending changes' do
      let(:subject) { '(test) NBCU Changes Submitted' }

      before do
        AgencyGatewayNotificationMailer.changes_submitted_to_gateway(to_addresses).deliver_now
      end

      it 'should send to the right addresses' do
        expect(ActionMailer::Base.deliveries.last.to).to eq(to_addresses)
      end

      it 'should be from the right addresses' do
        expect(ActionMailer::Base.deliveries.last.from).to eq(from_address)
      end

      it 'should have right subject' do
        expect(ActionMailer::Base.deliveries.last.subject).to eq(subject)
      end
    end
  end

  context 'notifying portal management team' do
    let(:agency) { create(:parent_agency) }
    let(:budget_year) { create(:budget_year) }
    let(:subject) { "(test) Agency Gateway changes submitted for #{agency.agency_name}" }
    before do
      allow(AgChangeHistory).to receive(:for_parent_agency).and_return(
        [
          {
            child_agency: 'Carat USA-AGENCY',
            advertiser: 'Macys, INC',
            property: 'USA',
            change_type: 'Quarterly Update',
            old_value: '$10',
            new_value: '$25'
          },
          {
            child_agency: 'Carat USA-AGENCY',
            advertiser: 'Walt Disney Parks & Resorts',
            property: 'USA',
            change_type: 'Updated Registration Total',
            old_value: '$20',
            new_value: '$30'
          },
          {
            child_agency: 'Carat USA-AGENCY',
            advertiser: 'General Motors',
            property: 'Telemundo',
            change_type: 'Prior Year Spend',
            old_value: '$0',
            new_value: '$555'
          }
        ]
      )
      portal_notification = PortalNotification.new(
        'parent_agency_id' => agency.id,
        'marketplace_id' => 10,
        'budget_year' => budget_year.fall_year
      )
      AgencyGatewayNotificationMailer.notify_portal_management(to_addresses, portal_notification).deliver_now
    end

    context 'tabular data' do
      it 'contains proper column headings' do
        headings = %(
          <th>Child agency</th> <th>Advertiser</th> <th>Property</th>
          <th>Change type</th> <th>Old value</th> <th>New value</th>
        ).squish
        expect(ActionMailer::Base.deliveries.last.body.to_s.squish).to include(headings)
      end

      it 'contains random agency name html' do
        expect(ActionMailer::Base.deliveries.last.body).to include('<td>Carat USA-AGENCY</td>')
      end

      it 'contains random property name html' do
        expect(ActionMailer::Base.deliveries.last.body).to include('<td>Telemundo</td>')
      end

      it 'contains random advertiser name html' do
        expect(ActionMailer::Base.deliveries.last.body).to include('<td>General Motors</td>')
      end

      it 'contains prior year spend and change_type' do
        expect(ActionMailer::Base.deliveries.last.body).to include('Prior Year Spend')
      end

      it 'contains quarterly changed record and change_type' do
        expect(ActionMailer::Base.deliveries.last.body).to include('Quarterly Update')
      end

      it 'contains totals changed record and change_type' do
        expect(ActionMailer::Base.deliveries.last.body).to include('Updated Registration Total')
      end

      it 'contains all previous and current values html, right aligned' do
        [10, 20, 25, 30, 0, 555].each do |dollar_value|
          expect(ActionMailer::Base.deliveries.last.body).to include("<td align=\"right\">$#{dollar_value}</td>")
        end
      end
    end

    it 'contains MSTR report link' do
      expect(ActionMailer::Base.deliveries.last.body).to include('http://pam.inbcu.com/reports/microstrategy?forms_report_filter%5Bmicrostrategy_report_id%5D=11840&report_type=4')
    end

    it 'should send to the right addresses' do
      expect(ActionMailer::Base.deliveries.last.to).to eq(to_addresses)
    end

    it 'should be from the right addresses' do
      expect(ActionMailer::Base.deliveries.last.from).to eq(from_address)
    end

    it 'should have right subject' do
      expect(ActionMailer::Base.deliveries.last.subject).to eq(subject)
    end

    it 'should have the agency name in the email body' do
      expect(Nokogiri::HTML.parse(ActionMailer::Base.deliveries.last.body.to_s).text).to include(agency.name)
    end
  end
end
