# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Budget<PERSON>hange<PERSON>ailer, type: :mailer do
  describe '#notify_prompt_zero' do
    before do
      deal = create(:deal,
                    property: create(:property, property_name: 'Property<PERSON>ame'),
                    advertiser: create(:advertiser, advertiser_name: 'Advertiser<PERSON>ame'),
                    agency: create(:agency, agency_name: 'Agency<PERSON>ame'))
      @budget = create(:budget,
                       deal:,
                       actual_prequarter_amount: 0,
                       actual_quarter1_amount: 0,
                       actual_quarter2_amount: 0,
                       actual_quarter3_amount: 0,
                       actual_quarter4_amount: 0,
                       actual_postquarter_amount: 0,
                       projected_prequarter_amount: 0,
                       projected_quarter1_amount: 0,
                       projected_quarter2_amount: 0,
                       projected_quarter3_amount: 0,
                       projected_quarter4_amount: 0,
                       projected_postquarter_amount: 0)
      agency_partner = create(:user, email: '<EMAIL>')

      create(:portal_team,
             marketplace_id: @budget.deal.marketplace_id,
             budget_year_id: @budget.budget_year_id,
             agency_id: @budget.deal.agency_id,
             app_user_id: agency_partner.id,
             unlock_request: 1)
      allow(@budget).to receive(:previous_changes).and_return(
        'actual_prequarter_amount' => [1000, 0], 'actual_quarter1_amount' => [2000, 0],
        'projected_quarter2_amount' => [3000, 0], 'projected_postquarter_amount' => [4000, 0]
      )

      BudgetChangeMailer.with(budget: @budget).notify_prompt_zero.deliver_now
      @email = ActionMailer::Base.deliveries.last
      @body = @email.body.to_s
    end

    it 'should have the right to and from addresses' do
      expect(@email.from.first).to eq(ENV['PAM_SUPPORT_EMAIL'])
      expect(@email.to.first).to eq('<EMAIL>')
    end

    it 'should include correct subject' do
      expect(@email.subject).to eq('(test) Deal Status Update')
    end

    it 'has the status name in the body' do
      expect(@body).to include(@budget.status.name)
    end

    it "has the deal's details in the body" do
      expect(@body).to include(@budget.deal.property.name)
      expect(@body).to include(@budget.deal.advertiser.name)
      expect(@body).to include(@budget.deal.agency.name)
    end

    it 'has the correct dollar value change in the body' do
      expect(@body).to include("$3,000 #{CGI.unescapeHTML('&#8594;')} $0")
      expect(@body).to include("$7,000 #{CGI.unescapeHTML('&#8594;')} $0")
    end
  end
end
