# frozen_string_literal: true

require 'rails_helper'

RSpec.describe TeamMemberMailer, type: :mailer do
  let(:title) { create(:app_user_title, app_user_title_name: 'test_title') }
  let(:manager) { create(:user, email: '<EMAIL>') }
  let(:user) { create(:user, manager_id: manager.id, email: '<EMAIL>', app_user_title: title) }
  let(:new_manager) { create(:user, email: '<EMAIL>') }

  before do
    TeamMemberMailer
      .with(user:, manager_id_change: [manager.id, new_manager.id])
      .notify_team_member_change
      .deliver_now

    @email = ActionMailer::Base.deliveries.last
  end

  describe '#notify_team_member_change' do
    it 'should have the right to and from addresses' do
      expect(@email.from.first).to eq(ENV['PAM_SUPPORT_EMAIL'])
      expect(@email.to).to include(user.email, manager.email, new_manager.email)
    end

    it 'should include correct subject' do
      expect(@email.subject).to eq('(test) Team Member Added')
    end

    it 'has the user and new manager info in the body' do
      expect(@email.body).to include(
        user.full_name,
        user.sso_id,
        user.email,
        user.app_user_title.app_user_title_name,
        new_manager.full_name,
        new_manager.email
      )
    end
  end
end
