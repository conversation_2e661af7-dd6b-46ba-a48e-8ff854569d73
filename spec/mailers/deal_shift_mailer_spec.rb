# frozen_string_literal: true

require 'rails_helper'

RSpec.describe DealShiftMailer, type: :mailer do
  let(:portal_user) { create(:user, active: true, email: '<EMAIL>') }
  let(:requested_by) { create(:user, email: FFaker::Internet.email) }
  let!(:budget_year) { create(:budget_year) }
  let!(:new_agency) { create(:agency, agency_name: 'test agency') }
  let!(:deal) { create(:deal, advertiser:) }
  let!(:advertiser) { create(:advertiser, advertiser_name: 'test advertiser') }
  let!(:deal_shift_option) { create(:deal_shift_option, deal_shift_option_name: 'Single Deal Shift') }
  let(:deal_shift) do
    create(
      :agency_deal_shift,
      shift_to: new_agency.agency_id,
      deal_id: deal.id,
      budget_year_id: budget_year.id,
      requested_by_id: requested_by.app_user_id,
      deal_shift_option:
    )
  end
  let(:email) { ActionMailer::Base.deliveries.last }

  before do
    allow(DealShiftOption).to receive(:single_deal).and_return(deal_shift_option)
  end

  describe '#notify_requestee_shift_created' do
    before do
      DealShiftMailer
        .with(deal_shift:, user: portal_user)
        .notify_requestee_shift_created
        .deliver_now
    end

    it 'should have the right to and from addresses' do
      expect(email.from.first).to eq(ENV['PAM_SUPPORT_EMAIL'])
      expect(email.to).to include(portal_user.email)
    end

    it 'should include correct subject' do
      expect(email.subject).to eq("(test) PAM Registrations: Deal Shift Request ##{deal_shift.id}")
    end

    it 'has the user and new manager info in the body' do
      expect(CGI.unescapeHTML(email.body.to_s)).to include(
        "Agency: #{deal_shift.agency_name}",
        "Property: #{deal_shift.property_name}",
        "Advertiser: #{deal_shift.advertiser_name}",
        "Marketplace: #{deal_shift.marketplace_name}",
        'Shift Type: Agency',
        "Shift To: #{new_agency.agency_name}",
        'Shift Option: Single Deal'
      )
      expect(CGI.unescapeHTML(email.body.to_s)).to include(
        'To respond, please navigate to ' \
        "#{ENV['DEAL_SHIFT_EXTERNAL_ADDRESS']}#{deal_shift.deal_shift_guids.first.guid}"
      )
    end
  end

  describe '#notify_old_agency_shift_created' do
    before do
      DealShiftMailer
        .with(deal_shift:, user: portal_user)
        .notify_old_agency_shift_created
        .deliver_now
    end

    it 'should have the right to and from addresses' do
      expect(email.from.first).to eq(ENV['PAM_SUPPORT_EMAIL'])
      expect(email.to).to include(portal_user.email)
    end

    it 'should include correct subject' do
      expect(email.subject).to eq("(test) PAM Registrations: Deal Shift Request ##{deal_shift.id}")
    end

    it 'has the user and new manager info in the body' do
      expect(CGI.unescapeHTML(email.body.to_s)).to include(
        "Agency: #{deal_shift.deal.agency_name}",
        "Property: #{deal_shift.property_name}",
        "Advertiser: #{deal_shift.advertiser_name}",
        "Marketplace: #{deal_shift.marketplace_name}",
        'Shift Type: Agency',
        "Shift To: #{new_agency.agency_name}",
        'Shift Option: Single Deal'
      )
      expect(CGI.unescapeHTML(email.body.to_s)).to include(
        "This request is still pending with the #{deal_shift.shift_to_name} Portals.",
        'You will receive a notification once the process is complete.'
      )
    end
  end

  describe '#notify_old_agency_shift_updated' do
    before do
      allow(deal_shift).to receive(:approved?).and_return(true)

      DealShiftMailer
        .with(deal_shift:, user: portal_user)
        .notify_old_agency_shift_updated
        .deliver_now
    end

    it 'should have the right to and from addresses' do
      expect(email.from.first).to eq(ENV['PAM_SUPPORT_EMAIL'])
      expect(email.to).to include(portal_user.email)
    end

    it 'should include correct subject' do
      expect(email.subject).to eq("(test) PAM Registrations: Deal Shift Request Status Update ##{deal_shift.id}")
    end

    it 'has the user and new manager info in the body' do
      expect(CGI.unescapeHTML(email.body.to_s)).to include(
        "Agency: #{deal_shift.deal.agency_name}",
        "Property: #{deal_shift.property_name}",
        "Advertiser: #{deal_shift.advertiser_name}",
        "Marketplace: #{deal_shift.marketplace_name}",
        'Shift Type: Agency',
        "Shift To: #{new_agency.agency_name}",
        'Shift Option: Single Deal'
      )
      expect(CGI.unescapeHTML(email.body.to_s)).to include('The following request has been approved.')
    end
  end

  describe '#notify_missing_portals' do
    before do
      DealShiftMailer
        .with(user: requested_by, error_message: 'no portals')
        .notify_missing_portals
        .deliver_now
    end

    it 'should have the right to and from addresses' do
      expect(email.from.first).to eq(ENV['PAM_SUPPORT_EMAIL'])
      expect(email.to).to include(ENV['DEAL_SHIFT_SUPPORT'])
    end

    it 'should have the correct body text' do
      expect(CGI.unescapeHTML(email.body.to_s)).to include(
        "#{requested_by.first_name} #{requested_by.last_name} " \
        'has failed to create a deal request due to the following reason:',
        'no portals',
        'Update data in PAM to prevent failures.'
      )
    end
  end

  describe '#notify_requester_shift_approved' do
    context 'when deal_shift is not approved' do
      it 'raises error' do
        expect do
          DealShiftMailer
            .with(deal_shift:)
            .notify_requester_shift_approved
            .deliver_now
        end.to raise_error(DealShiftMailer::MailerError)
      end
    end

    context 'when deal_shift is approved' do
      before do
        allow(deal_shift).to receive(:approved?).and_return(true)

        DealShiftMailer
          .with(deal_shift:)
          .notify_requester_shift_approved
          .deliver_now
      end

      it 'should have the right to and from addresses' do
        expect(email.from.first).to eq(ENV['PAM_SUPPORT_EMAIL'])
        expect(email.to).to include(requested_by.email)
      end

      it 'should include correct subject' do
        expect(email.subject).to eq("(test) PAM Registrations: Deal Shift Request Status Update ##{deal_shift.id}")
      end
    end
  end

  describe '#notify_requester_shift_rejected' do
    context 'when deal_shift is not rejected' do
      it 'raises error' do
        expect do
          DealShiftMailer
            .with(deal_shift:)
            .notify_requester_shift_rejected
            .deliver_now
        end.to raise_error(DealShiftMailer::MailerError)
      end
    end

    context 'when deal_shift is approved' do
      before do
        allow(deal_shift).to receive(:rejected?).and_return(true)

        DealShiftMailer
          .with(deal_shift:)
          .notify_requester_shift_rejected
          .deliver_now
      end

      it 'should have the right to and from addresses' do
        expect(email.from.first).to eq(ENV['PAM_SUPPORT_EMAIL'])
        expect(email.to).to include(requested_by.email)
      end

      it 'should include correct subject' do
        expect(email.subject).to eq("(test) PAM Registrations: Deal Shift Request Status Update ##{deal_shift.id}")
      end
    end
  end
end
