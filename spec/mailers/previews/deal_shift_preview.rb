# frozen_string_literal: true

# Preview all emails at http://localhost:3001/rails/mailers/deal_shift
class DealShiftPreview < ActionMailer::Preview
  %i[agency marketplace buying_ae client_ae buying_client_ae].each do |shift_type|
    klass = "#{shift_type.to_s.classify}DealShift".constantize

    define_method(:"notify_requestee_shift_created__#{shift_type}") do
      DealShiftMailer
        .with(deal_shift: klass.last, user: User.last)
        .notify_requestee_shift_created
    end

    define_method(:"remind_requestee_shift_created__#{shift_type}") do
      DealShiftMailer
        .with(deal_shift: klass.last, user: User.last)
        .remind_requestee_shift_created
    end

    define_method(:"notify_old_agency_shift_created__#{shift_type}") do
      DealShiftMailer
        .with(deal_shift: klass.last, user: User.last)
        .notify_old_agency_shift_created
    end

    define_method(:"notify_old_agency_shift_updated__#{shift_type}") do
      DealShiftMailer
        .with(deal_shift: DealShift.find_by(approved: true), user: User.last)
        .notify_old_agency_shift_updated
    end

    define_method(:"notify_requester_shift_approved__#{shift_type}") do
      DealShiftMailer
        .with(deal_shift: klass.find_by(approved: true))
        .notify_requester_shift_approved
    end

    define_method(:"notify_requester_shift_rejected__#{shift_type}") do
      DealShiftMailer
        .with(deal_shift: klass.find_by(approved: false))
        .notify_requester_shift_rejected
    end

    define_method(:"notify_requester_shift_auto_rejected__#{shift_type}") do
      DealShiftMailer
        .with(deal_shift: klass.find_by(approved: false))
        .notify_requester_shift_auto_rejected
    end
  end

  def notify_missing_portals
    DealShiftMailer
      .with(user: User.last,
            error_message: PamClient::Concerns::DealShiftErrors::PortalTeamPermissionError.new.message)
      .notify_missing_portals
  end
end
