# frozen_string_literal: true

# Preview all emails at http://localhost:3001/rails/mailers/team_member_mailer
class TeamMemberMailerPreview < ActionMailer::Preview
  def notify_team_member_change
    TeamMemberMailer
      .with(user: users_with_email.first,
            manager_id_change: [users_with_email.first.manager_id, users_with_email.last.id])
      .notify_team_member_change
  end

  private

  def users_with_email
    @users_with_email ||= User.where.not(email: nil)
  end
end
