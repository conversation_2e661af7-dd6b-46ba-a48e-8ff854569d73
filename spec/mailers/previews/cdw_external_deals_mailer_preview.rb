# frozen_string_literal: true

# Preview all emails at http://localhost:3001/rails/mailers/cdw_external_deals_mailer
class CdwExternalDealsMailerPreview < ActionMailer::Preview
  def notify_success
    CdwExternalDealsMailer
      .with(file_header_id: 3_829_484,
            batch_header_id: 8_237_371,
            cdw_record_count: 475,
            cdw_stage_count_before: 987_332,
            cdw_stage_count_after: 987_781)
      .notify_success
  end

  def notify_failure
    CdwExternalDealsMailer
      .with(error_message: 'Something went wrong',
            backtrace: 'It is broken')
      .notify_failure
  end
end
