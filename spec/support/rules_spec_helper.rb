# frozen_string_literal: true

# Helper methods for dealing with DataImport classes

module <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
  def create_rule(src_col_name, src_col_val, tgt_model_id_col, tgt_model_id)
    source_column = begin
                      build(:data_import_source_column,
                            source_column_name: src_col_name)
                    rescue StandardError
                      ruleset.source_columns.find_by(source_column_name: src_col_name)
                    end

    action = build(:data_import_action, target_column_name: tgt_model_id_col, sms_target_column_value: tgt_model_id)
    condition = build(:data_import_condition, condition_name: src_col_name,
                                              source_column_name: source_column.source_column_name,
                                              source_column_value: src_col_val)
    build(:data_import_rule, actions_attributes: [action.as_json.symbolize_keys],
                             conditions_attributes: [condition.as_json.symbolize_keys])
  end
end
