# frozen_string_literal: true

module <PERSON>atterSpecHelper
  # traverse down the levels and recursively check result to see if matched expected structure
  def check_structure(structure, result)
    return unless structure.is_a?(Hash)

    diff = (structure.keys - result.keys) || (result.keys - structure.keys)
    raise format_mismatch_error_msg(structure, result, diff) unless diff == []

    result[:data]&.each do |child_result|
      child_structure = if child_result[:column_type]
                          structure[:data].find { |row| row[:column_type] == child_result[:column_type] }
                        else
                          structure[:data].first
                        end

      check_structure(child_structure, child_result)
    end
  end

  def format_mismatch_error_msg(structure, result, diff)
    "Format mismatch found:
      Expected: #{structure.merge(data: nil) if structure[:data]};
      Got: #{result.merge(data: nil) if result[:data]};
      Diff: #{diff}."
  end
end

RSpec.configure do |config|
  config.include FormatterSpecHelper, formatter_spec_helper: true
end
