# frozen_string_literal: true

module FinanceCommonHelper
  # This helper produces a common subset of fabrications
  # (finance_models, calendar_years, etc...) for all finance specs

  def fabricate_common_data(finance_metric_type_name)
    finance_metric_type = FinanceMetricType.first_or_create(finance_metric_type_name:)
    @property ||= create(:property, :linear, include_finance_model: 1)

    current_year = Date.today.year
    calendar_years = ((current_year - 5)..(current_year + 5)).to_a.map do |year|
      create(:calendar_year, calendar_year: year, default_calendar_year: (year == current_year), show_in_finance: 1)
    end
    @calendar_year = CalendarYear.current_calendar_year

    finance_months = Array.new(12) { create(:finance_month) } << create(:finance_month, name: 'Full-Year')
    @finance_month = FinanceMonth.first
    @finance_month.update!(historical_month: true)

    finance_models = ['Current Estimate', 'Budget', 'LRP', 'Actuals'].map { |name| create(:finance_model, name:) }

    calendar_years.each do |year|
      4.times.each_with_index.map do |index|
        create(:quarter_date,
               finance_quarter: create(:finance_quarter,
                                       calendar_year: year,
                                       finance_metric_type:,
                                       disable_swing_date: false,
                                       quarter: create(:quarter,
                                                       calendar_year: year,
                                                       quarter: index + 1)))
      end
    end
    finance_quarters = FinanceQuarter.where(calendar_year_id: @calendar_year.id)

    finance_headers = finance_months.product(finance_models).filter_map do |finance_month, finance_model|
      next unless (finance_month.name != 'Full-Year' && finance_model.name == 'Current Estimate') ||
                  (finance_month.name == 'Full-Year' && finance_model.name != 'Current Estimate')

      create(:finance_header, property: @property, calendar_year: @calendar_year, finance_month:,
                              finance_model:, locked: false)
    end

    [finance_metric_type, finance_headers, finance_quarters]
  end

  def construct_composite_update_hash(record, update_attr_value = 1_000)
    finance_header = record.finance_header
    hash = {
      calendar_year_id: finance_header.calendar_year_id,
      finance_month_id: finance_header.finance_month_id,
      finance_model_id: finance_header.finance_model_id,
      property_id: finance_header.property_id,
      @update_attr => update_attr_value
    }

    (@composite_pks - finance_header_composite_ids).each do |key|
      hash[key] = record.send(key)
    end

    hash
  end

  def finance_header_composite_ids
    %i[calendar_year_id finance_month_id finance_model_id property_id]
  end

  def cleanse_database
    DatabaseCleaner.clean_with(:truncation, except: ActiveRecord::Base.connection.views + [:ar_internal_metadata])
  end
end

RSpec.configure do |config|
  config.include FinanceCommonHelper, finance_common_helper: true
end
