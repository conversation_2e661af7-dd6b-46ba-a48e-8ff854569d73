# frozen_string_literal: true

shared_examples 'a direct message listener' do
  before(:all) do
    connection = MarchHare.connect
    @channel = connection.create_channel
    $rabbit_mq_connection = connection
  end

  before(:each) do
    subject.listen
  end

  describe 'the ActiveRecord connection pool' do
    let(:exchg) { @channel.direct(subject.exchange_name) }

    it 'should not grow in size after receiving an incoming message' do
      num_conns_before = ActiveRecord::Base.connection_pool.connections.size

      pool_size = ActiveRecord::Base.connection_pool.size
      # Was failing when called more than the connection pool size
      (pool_size + 1).times do
        expect { exchg.publish(payload) }.not_to raise_error
        sleep(0.5) # Failure only happens when messages come in sequentially
      end

      num_conns_after = ActiveRecord::Base.connection_pool.connections.size
      expect(num_conns_after).to be <= num_conns_before
    end
  end
end
