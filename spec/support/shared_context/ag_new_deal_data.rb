# frozen_string_literal: true

shared_context 'ag new deal data' do
  let(:marketplace) { create(:marketplace) }
  let(:agency) { create(:agency) }
  let(:agency2) { create(:agency) }
  let(:agency_parentless) { create(:agency, parent_agency_id: -1) }
  let(:grandparent_agency_with_children) { create(:grandparent_agency, :with_children) }
  let(:budget_year) { create(:budget_year) }
  let(:advertiser) { create(:advertiser) }
  let(:advertiser2) { create(:advertiser) }
  let(:brand) { create(:advertiser_brand, advertiser:) }
  let(:demographic) { create(:demographic) }
  let(:rating_stream) { create(:rating_stream) }
  let(:sales_type) { create(:sales_type) }
  let!(:property_1) { create(:property) }
  let!(:property_2) { create(:property) }
  let!(:property_3) { create(:property, :digital) }
  let(:create_digital_sales_type) { create(:sales_type, name: 'Digital Video') }
  let(:create_sponsorship_type) { create(:sponsorship_type, name: 'No Sponsorship') }
  let(:create_default_user) { create(:user, first_name: 'AE', last_name: 'User', account_executive: true) }
end
