# frozen_string_literal: true

require 'rails_helper'

RSpec.describe CaeAssignmentsJob, type: :job do
  describe '#perform_later' do
    it 'enqueues the job' do
      expect do
        CaeAssignmentsJob.perform_later
      end.to have_enqueued_job
    end

    context 'cae in range' do
      let!(:cae_record) { create(:covering_ae) }

      it 'copies ae assignment to cae' do
        ae_assignment = FactoryBot.create(:assignment, user: cae_record.covering_for)
        cae_user = cae_record.covering_ae
        expect(cae_user.assignments.count).to be_zero

        CaeAssignmentsJob.perform_now

        expect(cae_user.reload.assignments.count).to eq(1)
        cae_assignment = cae_user.assignments.first
        expect(cae_assignment.agency_id).to eq(ae_assignment.agency_id)
        expect(cae_assignment.property_id).to eq(ae_assignment.property_id)
        expect(cae_assignment.advertiser_id).to eq(ae_assignment.advertiser_id)
      end

      it 'updates the expiration on the copied assignment' do
        FactoryBot.create(:assignment, user: cae_record.covering_for)
        cae_user = cae_record.covering_ae
        expect(cae_user.assignments.count).to be_zero

        CaeAssignmentsJob.perform_now

        expect(cae_user.reload.assignments.first.expires_on).to eq(cae_record.end_date)
        new_end_date = cae_record.end_date + 1.month
        cae_record.update(end_date: new_end_date)

        CaeAssignmentsJob.perform_now

        expect(cae_user.reload.assignments.first.expires_on).to eq(new_end_date)
      end

      it 'removes expired assignments' do
        FactoryBot.create(:assignment, user: cae_record.covering_ae, expires_on: DateTime.now - 3.days)

        cae_user = cae_record.covering_ae
        expect(cae_user.assignments.count).to eq(1)

        CaeAssignmentsJob.perform_now

        expect(cae_user.reload.assignments.count).to be_zero
      end
    end

    context 'cae not in range' do
      let!(:cae_record) { create(:covering_ae, start_date: DateTime.now - 3.days, end_date: DateTime.now - 2.days) }
      let!(:ae_assignment) { create(:assignment, user: cae_record.covering_for) }

      it 'copies does not ae assignment to cae' do
        cae_user = cae_record.covering_ae
        expect(cae_user.assignments.count).to be_zero

        CaeAssignmentsJob.perform_now

        expect(cae_user.reload.assignments.count).to be_zero
      end
    end
  end
end
