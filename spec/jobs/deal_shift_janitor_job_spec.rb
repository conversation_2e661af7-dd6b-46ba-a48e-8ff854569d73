# frozen_string_literal: true

require 'rails_helper'

RSpec.describe DealShiftJanitorJob, type: :job do
  let!(:one_day_old_approved_ds) { create(:deal_shift, approved: true, created_at: Time.now - 1.days) }
  let!(:one_day_old_rejected_ds) { create(:deal_shift, approved: false, created_at: Time.now - 1.days) }

  let!(:one_day_old_pending_unreminded_ds) { create(:deal_shift, approved: nil, created_at: Time.now - 1.days) }
  let!(:three_day_old_pending_unreminded_ds) { create(:deal_shift, approved: nil, created_at: Time.now - 3.days) }
  let!(:five_day_old_pending_unreminded_ds) { create(:deal_shift, approved: nil, created_at: Time.now - 5.days) }
  let!(:pending_unreminded_child_with_pending_parent_ds) do
    parent = create(:deal_shift, approved: nil)
    create(:deal_shift, approved: nil, created_at: Time.now - 3.days, parent:)
  end
  let!(:pending_unreminded_child_with_rejected_parent_ds) do
    parent = create(:deal_shift, approved: 0)
    create(:deal_shift, approved: nil, created_at: Time.now - 3.days, parent:)
  end
  let!(:pending_unreminded_child_with_approved_parent_ds0) do
    parent = create(:deal_shift, approved: 1, date_disposed: Time.now - 1.days)
    create(:deal_shift, approved: nil, created_at: Time.now - 3.days, parent:)
  end
  let!(:pending_unreminded_child_with_approved_parent_ds1) do
    parent = create(:deal_shift, approved: 1, date_disposed: Time.now - 3.days)
    create(:deal_shift, approved: nil, created_at: Time.now - 3.days, parent:)
  end
  let!(:three_day_old_pending_reminded_ds) do
    create(:deal_shift, approved: nil, created_at: Time.now - 3.days, reminded_at: Time.now - 1.days)
  end
  let!(:five_day_old_pending_reminded_ds) do
    create(:deal_shift, approved: nil, created_at: Time.now - 3.days, reminded_at: Time.now - 3.days)
  end
  let!(:pending_reminded_child_with_pending_parent_ds) do
    parent = create(:deal_shift, approved: nil)
    create(:deal_shift, approved: nil, created_at: Time.now - 3.days, parent:, reminded_at: Time.now - 3.days)
  end
  let!(:pending_reminded_child_with_rejected_parent_ds) do
    parent = create(:deal_shift, approved: 0)
    create(:deal_shift, approved: nil, created_at: Time.now - 3.days, parent:, reminded_at: Time.now - 3.days)
  end
  let!(:pending_reminded_child_with_approved_parent_ds) do
    parent = create(:deal_shift, approved: 1)
    create(:deal_shift, approved: nil, created_at: Time.now - 3.days, parent:, reminded_at: Time.now - 3.days)
  end

  describe '#perform_later' do
    it 'enqueues the job' do
      expect do
        DealShiftJanitorJob.perform_later
      end.to have_enqueued_job
    end
  end

  it 'sends the correct reminder emails' do
    count = 0
    Api::DealShiftMailerService.any_instance.stub(:remind_requestee_shift_created) { count += 1 }

    DealShiftJanitorJob.perform_now

    expect(count).to eq(3)
    expect(one_day_old_pending_unreminded_ds.reload.reminded_at).to be_nil
    expect(three_day_old_pending_unreminded_ds.reload.reminded_at).not_to be_nil
    expect(five_day_old_pending_unreminded_ds.reload.reminded_at).not_to be_nil
    expect(pending_unreminded_child_with_pending_parent_ds.reload.reminded_at).to be_nil
    expect(pending_unreminded_child_with_rejected_parent_ds.reload.reminded_at).to be_nil
    expect(pending_unreminded_child_with_approved_parent_ds0.reload.reminded_at).to be_nil
    expect(pending_unreminded_child_with_approved_parent_ds1.reload.reminded_at).not_to be_nil
  end

  it 'automatically rejects the stale deal_shifts' do
    count = 0
    Api::DealShiftMailerService.any_instance.stub(:notify_requester_shift_auto_rejected) { count += 1 }

    DealShiftJanitorJob.perform_now

    expect(count).to eq(2)
    expect(three_day_old_pending_reminded_ds.reload.approved).to be_nil
    expect(five_day_old_pending_reminded_ds.reload.approved).to be false
    expect(pending_reminded_child_with_pending_parent_ds.reload.approved).to be nil
    expect(pending_reminded_child_with_rejected_parent_ds.reload.approved).to be nil
    expect(pending_reminded_child_with_approved_parent_ds.reload.approved).to be false
  end
end
