# frozen_string_literal: true

FactoryBot.define do
  factory :data_import_rule_set, class: StealthUpload::Mapping::RuleSet do
    source_columns { [attributes_for(:data_import_source_column).as_json.symbolize_keys] }
    registration_column { attributes_for(:data_import_registration_column).as_json.symbolize_keys }
    rules do
      rule = attributes_for(:data_import_rule).as_json.symbolize_keys
      [{ conditions_attributes: rule[:conditions], actions_attributes: rule[:actions] }]
    end

    initialize_with { new(attributes) }
  end
end
