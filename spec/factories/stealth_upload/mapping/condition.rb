# frozen_string_literal: true

FactoryBot.define do
  factory :data_import_condition, class: StealthUpload::Mapping::Condition do
    sequence(:condition_index)
    sequence(:condition_name) { |n| "Condition #{n}" }
    sequence(:source_column_id)
    sequence(:source_column_name) { |n| "Source Column #{n}" }
    sequence(:source_column_value) { |n| "Source Column Value #{n}" }
    sequence(:rule_id)

    initialize_with { new(attributes) }
  end
end
