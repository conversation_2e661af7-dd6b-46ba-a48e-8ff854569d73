# frozen_string_literal: true

require 'spec_helper'

RSpec.describe Salesforce::Sync::MainLogic, type: :model do
  let(:main_logic) { described_class.new }
  let(:sf_logic) { instance_double(Salesforce::Sync::SfLogic) }
  let(:pam_logic) { instance_double(Salesforce::Sync::PamLogic) }

  before do
    allow(Salesforce::Sync::SfLogic).to receive(:new).and_return(sf_logic)
    allow(Salesforce::Sync::PamLogic).to receive(:new).and_return(pam_logic)
    allow(sf_logic).to receive(:connect_sf)
  end

  after do
    # Clean up test data to avoid conflicts
    # Delete from all agency types (they all use the same agency table)
    Agency.delete_all
  end

  describe '#process_industries' do
    let(:sf_industry) do
      {
        'Id' => 'SF001',
        'Name' => 'Test Industry'
      }
    end

    before do
      allow(sf_logic).to receive(:get_sf_industries).and_return([sf_industry])
      allow(sf_logic).to receive(:sf_industry_status)
      allow(pam_logic).to receive(:fetch_or_create_pam_obj)
    end

    it 'processes Salesforce industries' do
      expect(sf_logic).to receive(:get_sf_industries).with(nil, Salesforce::Sync::SyncConfig.max_records)
      expect(sf_logic).to receive(:sf_industry_status).with(sf_industry, 'pickedup')
      expect(pam_logic).to receive(:fetch_or_create_pam_obj).with(
        Category,
        'SF001',
        'Test Industry',
        create_pamobj: true,
        update_pamobj: true
      )
      expect(sf_logic).to receive(:sf_industry_status).with(sf_industry, 'processed')

      main_logic.process_industries
    end

    it 'handles errors during processing' do
      allow(pam_logic).to receive(:fetch_or_create_pam_obj).and_raise(StandardError.new('Test error'))
      expect(BacktraceHelper).to receive(:print_exception)

      main_logic.process_industries
    end
  end

  describe '#process_agencies' do
    let(:sf_agencies) do
      [
        {
          'Id' => 'SF002',
          'Name' => 'Test Agency',
          'ParentAgency__c' => nil,
          'IsParent__c' => true,
          'isActive__c' => true
        },
        {
          'Id' => 'SF003',
          'Name' => 'Child Agency',
          'ParentAgency__c' => 'SF002',
          'IsParent__c' => false,
          'isActive__c' => true
        }
      ]
    end

    before do
      allow(sf_logic).to receive(:get_sf_agencies).and_return(sf_agencies)
      allow(sf_logic).to receive(:sf_agency_status)
      allow(pam_logic).to receive(:fetch_or_create_pam_obj)
    end

    it 'processes Salesforce agencies in correct order' do
      expect(sf_logic).to receive(:get_sf_agencies).with(nil, Salesforce::Sync::SyncConfig.max_parent_records)
      sf_agencies.each do |agency|
        expect(sf_logic).to receive(:sf_agency_status).with(agency, 'pickedup')
        expect(pam_logic).to receive(:fetch_or_create_pam_obj).with(
          BaseAgency,
          agency['Id'],
          agency['Name'],
          create_pamobj: true,
          update_pamobj: true
        )
        expect(sf_logic).to receive(:sf_agency_status).with(agency, 'processed')
      end

      main_logic.process_agencies
    end

    it 'handles errors during processing' do
      allow(pam_logic).to receive(:fetch_or_create_pam_obj).and_raise(StandardError.new('Test error'))
      expect(BacktraceHelper).to receive(:print_exception).twice

      main_logic.process_agencies
    end
  end

  describe '#process_advertisers' do
    let(:sf_advertiser) do
      {
        'Id' => 'SF004',
        'Name' => 'Test Advertiser',
        'Industry__c' => 'IND001',
        'Industry_Name__c' => 'Test Industry',
        'BillingState' => 'CA',
        'isActive__c' => true,
        'Target_Account__c' => 'Yes',
        'D2C_Target_Account__c' => 'No',
        'Global_Account__c' => 'Yes',
        'Peacock_Target_Account__c' => 'No'
      }
    end

    before do
      allow(sf_logic).to receive(:get_sf_advertisers).and_return([sf_advertiser])
      allow(sf_logic).to receive(:sf_advertiser_status)
      allow(pam_logic).to receive(:fetch_or_create_pam_obj)
      allow(pam_logic).to receive(:fetch_geo_state).and_return(double(geo_state_id: 1))
    end

    it 'processes Salesforce advertisers' do
      expect(sf_logic).to receive(:get_sf_advertisers).with(nil, Salesforce::Sync::SyncConfig.max_records)
      expect(sf_logic).to receive(:sf_advertiser_status).with(sf_advertiser, 'pickedup')
      expect(pam_logic).to receive(:fetch_or_create_pam_obj).with(
        Category,
        'IND001',
        'Test Industry',
        create_pamobj: true,
        update_pamobj: false
      )
      expect(pam_logic).to receive(:fetch_geo_state).with('CA')
      expect(pam_logic).to receive(:fetch_or_create_pam_obj).with(
        Advertiser,
        'SF004',
        'Test Advertiser',
        create_pamobj: true,
        update_pamobj: true
      )
      expect(sf_logic).to receive(:sf_advertiser_status).with(sf_advertiser, 'processed')

      main_logic.process_advertisers
    end

    it 'handles errors during processing' do
      allow(pam_logic).to receive(:fetch_or_create_pam_obj).and_raise(StandardError.new('Test error'))
      expect(BacktraceHelper).to receive(:print_exception)

      main_logic.process_advertisers
    end
  end

  describe '#order_agencies' do
    let(:agencies) do
      [
        { 'Id' => 'SF001', 'IsParent__c' => true, 'ParentAgency__c' => nil },
        { 'Id' => 'SF002', 'IsParent__c' => true, 'ParentAgency__c' => 'SF001' },
        { 'Id' => 'SF003', 'IsParent__c' => false, 'ParentAgency__c' => 'SF002' }
      ]
    end

    it 'correctly orders agencies based on hierarchy' do
      ordered_agencies = main_logic.order_agencies(agencies)
      expect(ordered_agencies.map { |a| a['Id'] }).to eq(%w[SF001 SF002 SF003])
      # order_agencies only assigns Agency or ParentAgency, GrandParentAgency is assigned later with cutoff protection
      expect(ordered_agencies.map { |a| a['agency_type'] }).to eq(%w[ParentAgency ParentAgency Agency])
    end
  end

  describe 'Agency Creation Test' do
    it 'can create Agency records using agency table' do
      # Test basic agency creation
      agency = create_agency(
        agency_name: 'Test Agency Creation',
        agency_type: 'Agency'
      )

      expect(agency).to be_persisted
      expect(agency.agency_name).to eq('Test Agency Creation')
      expect(agency.agency_type).to eq('Agency')
      expect(agency).to be_a(Agency)

      puts "✅ Successfully created Agency record with ID: #{agency.agency_id}"
    end

    it 'can create ParentAgency records' do
      parent = create_agency(
        agency_name: 'Test Parent Agency',
        agency_type: 'ParentAgency'
      )

      expect(parent).to be_persisted
      expect(parent.agency_type).to eq('ParentAgency')
      expect(parent).to be_a(ParentAgency)

      puts "✅ Successfully created ParentAgency record with ID: #{parent.agency_id}"
    end

    it 'can create GrandParentAgency records' do
      grandparent = create_agency(
        agency_name: 'Test GrandParent Agency',
        agency_type: 'GrandParentAgency'
      )

      expect(grandparent).to be_persisted
      expect(grandparent.agency_type).to eq('GrandParentAgency')
      expect(grandparent).to be_a(GrandParentAgency)

      puts "✅ Successfully created GrandParentAgency record with ID: #{grandparent.agency_id}"
    end
  end

  describe '#validate_parent_assignment' do
    before do
      # Set test cutoff to 10 days
      allow(ENV).to receive(:fetch).with('AGENCY_PROTECTION_CUTOFF_DAYS', '10').and_return('10')
    end

    context 'with valid assignments' do
      it 'allows assignment when no parent' do
        expect(main_logic.send(:validate_parent_assignment, 1, -1)).to be true
      end

      it 'allows simple parent-child relationship' do
        parent = create_agency(agency_id: 100, agency_type: 'Agency', agency_name: 'Parent Agency')
        child = create_agency(agency_id: 101, agency_type: 'Agency', agency_name: 'Child Agency')

        expect(main_logic.send(:validate_parent_assignment, child.agency_id, parent.agency_id)).to be true
      end

      it 'allows 3-level hierarchy (max allowed)' do
        grandparent = create_agency(agency_id: 100, agency_type: 'GrandParentAgency', agency_name: 'Grandparent Agency')
        parent = create_agency(agency_id: 101, agency_type: 'ParentAgency', parent_agency_id: 100, agency_name: 'Parent Agency')
        child = create_agency(agency_id: 102, agency_type: 'Agency', agency_name: 'Child Agency')

        expect(main_logic.send(:validate_parent_assignment, child.agency_id, parent.agency_id)).to be true
      end
    end

    context 'with circular relationships' do
      it 'blocks direct circular relationship (A -> B, B -> A)' do
        agency_a = create_agency(agency_id: 100, agency_type: 'ParentAgency')
        agency_b = create_agency(agency_id: 101, agency_type: 'Agency', parent_agency_id: 100)

        expect(main_logic.send(:validate_parent_assignment, agency_a.agency_id, agency_b.agency_id)).to be false
      end

      it 'blocks 3-way circular relationship (A -> B -> C -> A)' do
        agency_a = create_agency(agency_id: 100, agency_type: 'GrandParentAgency')
        agency_b = create_agency(agency_id: 101, agency_type: 'ParentAgency', parent_agency_id: 100)
        agency_c = create_agency(agency_id: 102, agency_type: 'Agency', parent_agency_id: 101)

        expect(main_logic.send(:validate_parent_assignment, agency_a.agency_id, agency_c.agency_id)).to be false
      end

      it 'blocks self-assignment (A -> A)' do
        agency = create_agency(agency_id: 100, agency_type: 'Agency')

        expect(main_logic.send(:validate_parent_assignment, agency.agency_id, agency.agency_id)).to be false
      end
    end

    context 'with deep hierarchy violations' do
      it 'blocks 4-level hierarchy creation' do
        great_grandparent = create_agency(agency_id: 100, agency_type: 'GrandParentAgency')
        grandparent = create_agency(agency_id: 101, agency_type: 'ParentAgency', parent_agency_id: 100)
        parent = create_agency(agency_id: 102, agency_type: 'Agency', parent_agency_id: 101)
        child = create_agency(agency_id: 103, agency_type: 'Agency')

        expect(main_logic.send(:validate_parent_assignment, child.agency_id, parent.agency_id)).to be false
      end

      it 'blocks assignment that would create 4+ levels through child descendants' do
        parent = create_agency(agency_id: 100, agency_type: 'ParentAgency')
        child = create_agency(agency_id: 101, agency_type: 'ParentAgency')
        grandchild = create_agency(agency_id: 102, agency_type: 'ParentAgency', parent_agency_id: 101)
        great_grandchild = create_agency(agency_id: 103, agency_type: 'Agency', parent_agency_id: 102)

        expect(main_logic.send(:validate_parent_assignment, child.agency_id, parent.agency_id)).to be false
      end
    end

    context 'with non-existent agencies' do
      it 'blocks assignment when parent does not exist' do
        child = create_agency(agency_id: 100, agency_type: 'Agency')

        expect(main_logic.send(:validate_parent_assignment, child.agency_id, 999)).to be false
      end
    end

    context 'with existing circular data in database' do
      it 'handles existing circular relationships gracefully' do
        # Create circular relationship in database (should not happen in production)
        agency_a = create_agency(agency_id: 100, agency_type: 'ParentAgency')
        agency_b = create_agency(agency_id: 101, agency_type: 'Agency', parent_agency_id: 100)

        # Manually create circular relationship (bypassing validations)
        agency_a.update_column(:parent_agency_id, 101)

        # Should detect and block further circular assignments
        agency_c = create_agency(agency_id: 102, agency_type: 'Agency')
        expect(main_logic.send(:validate_parent_assignment, agency_c.agency_id, agency_a.agency_id)).to be false
      end

      it 'handles deep existing hierarchies' do
        # Create 4-level hierarchy in database (should not happen)
        level1 = create_agency(agency_id: 100, agency_type: 'GrandParentAgency')
        level2 = create_agency(agency_id: 101, agency_type: 'ParentAgency', parent_agency_id: 100)
        level3 = create_agency(agency_id: 102, agency_type: 'Agency', parent_agency_id: 101)
        level4 = create_agency(agency_id: 103, agency_type: 'Agency', parent_agency_id: 102)

        # Should block adding level 5
        level5 = create_agency(agency_id: 104, agency_type: 'Agency')
        expect(main_logic.send(:validate_parent_assignment, level5.agency_id, level4.agency_id)).to be false
      end
    end

    context 'with complex reparenting scenarios' do
      it 'handles moving subtree that would create deep hierarchy' do
        # Original structure: A -> B -> C, D -> E
        agency_a = create_agency(agency_id: 100, agency_type: 'GrandParentAgency')
        agency_b = create_agency(agency_id: 101, agency_type: 'ParentAgency', parent_agency_id: 100)
        agency_c = create_agency(agency_id: 102, agency_type: 'Agency', parent_agency_id: 101)
        agency_d = create_agency(agency_id: 103, agency_type: 'ParentAgency')
        agency_e = create_agency(agency_id: 104, agency_type: 'Agency', parent_agency_id: 103)

        # Try to make D a child of C (would create A -> B -> C -> D -> E = 5 levels)
        expect(main_logic.send(:validate_parent_assignment, agency_d.agency_id, agency_c.agency_id)).to be false
      end

      it 'allows valid reparenting within 3-level limit' do
        # Structure: A -> B, C -> D
        agency_a = create_agency(agency_id: 100, agency_type: 'ParentAgency')
        agency_b = create_agency(agency_id: 101, agency_type: 'Agency', parent_agency_id: 100)
        agency_c = create_agency(agency_id: 102, agency_type: 'ParentAgency')
        agency_d = create_agency(agency_id: 103, agency_type: 'Agency', parent_agency_id: 102)

        # Move D under B (creates A -> B -> D, which is valid)
        expect(main_logic.send(:validate_parent_assignment, agency_d.agency_id, agency_b.agency_id)).to be true
      end
    end

    context 'with boundary conditions' do
      it 'handles exactly 3-level hierarchy (boundary case)' do
        grandparent = create_agency(agency_id: 100, agency_type: 'GrandParentAgency')
        parent = create_agency(agency_id: 101, agency_type: 'ParentAgency', parent_agency_id: 100)
        child = create_agency(agency_id: 102, agency_type: 'Agency')

        # This should be exactly at the limit (3 levels)
        expect(main_logic.send(:validate_parent_assignment, child.agency_id, parent.agency_id)).to be true
      end

      it 'blocks exactly 4-level hierarchy (over boundary)' do
        level1 = create_agency(agency_id: 100, agency_type: 'GrandParentAgency')
        level2 = create_agency(agency_id: 101, agency_type: 'ParentAgency', parent_agency_id: 100)
        level3 = create_agency(agency_id: 102, agency_type: 'Agency', parent_agency_id: 101)
        level4 = create_agency(agency_id: 103, agency_type: 'Agency')

        # This would create exactly 4 levels (over limit)
        expect(main_logic.send(:validate_parent_assignment, level4.agency_id, level3.agency_id)).to be false
      end
    end
  end

  describe '#process_sf_agency with comprehensive scenarios' do
    let(:base_sf_agency) do
      {
        'Id' => 'SF001',
        'Name' => 'Test Agency',
        'IsParent__c' => false,
        'ParentAgency__c' => nil,
        'agency_type' => 'Agency'
      }
    end

    before do
      allow(ENV).to receive(:fetch).with('AGENCY_PROTECTION_CUTOFF_DAYS', '10').and_return('10')
    end

    context 'with new agency creation scenarios' do
      it 'creates standalone agency without parent' do
        agency = create_agency(agency_id: 100, agency_type: 'Agency')

        allow(pam_logic).to receive(:fetch_or_create_pam_obj)
          .with(BaseAgency, nil, nil, create_pamobj: true, update_pamobj: false)
          .and_return(nil)
        allow(pam_logic).to receive(:fetch_or_create_pam_obj)
          .with(BaseAgency, 'SF001', 'Test Agency', create_pamobj: true, update_pamobj: true)
          .and_yield(agency)
          .and_return(agency)

        expect { main_logic.send(:process_sf_agency, base_sf_agency) }.not_to raise_error
        expect(agency.parent_agency_id).to eq(-1)
      end

      it 'creates agency with valid parent and promotes parent type' do
        parent = create_agency(agency_id: 100, agency_type: 'Agency')
        child = create_agency(agency_id: 101, agency_type: 'Agency')

        sf_data = base_sf_agency.merge('ParentAgency__c' => 'SF_PARENT')

        allow(pam_logic).to receive(:fetch_or_create_pam_obj)
          .with(BaseAgency, 'SF_PARENT', nil, create_pamobj: true, update_pamobj: false)
          .and_return(parent)
        allow(pam_logic).to receive(:fetch_or_create_pam_obj)
          .with(BaseAgency, 'SF001', 'Test Agency', create_pamobj: true, update_pamobj: true)
          .and_yield(child)
          .and_return(child)

        expect { main_logic.send(:process_sf_agency, sf_data) }.not_to raise_error

        # Reload using BaseAgency since type may have changed
        reloaded_parent = BaseAgency.find(parent.agency_id)
        expect(reloaded_parent.agency_type).to eq('ParentAgency')
        expect(child.parent_agency_id).to eq(parent.agency_id)
      end

      it 'creates grandchild and promotes grandparent within cutoff' do
        grandparent = create_agency(
          agency_id: 100,
          agency_type: 'ParentAgency',
          created_at: 5.days.ago
        )
        parent = create_agency(
          agency_id: 101,
          agency_type: 'ParentAgency',
          parent_agency_id: 100,
          created_at: 5.days.ago
        )
        child = create_agency(agency_id: 102, agency_type: 'Agency')

        sf_data = base_sf_agency.merge('ParentAgency__c' => 'SF_PARENT')

        allow(pam_logic).to receive(:fetch_or_create_pam_obj)
          .with(BaseAgency, 'SF_PARENT', nil, create_pamobj: true, update_pamobj: false)
          .and_return(parent)
        allow(pam_logic).to receive(:fetch_or_create_pam_obj)
          .with(BaseAgency, 'SF001', 'Test Agency', create_pamobj: true, update_pamobj: true)
          .and_yield(child)
          .and_return(child)

        expect { main_logic.send(:process_sf_agency, sf_data) }.not_to raise_error

        # Reload using BaseAgency since type may have changed
        reloaded_grandparent = BaseAgency.find(grandparent.agency_id)
        expect(reloaded_grandparent.agency_type).to eq('GrandParentAgency')
      end
    end

    context 'with protection cutoff scenarios' do
      it 'allows changes to new agencies (within cutoff)' do
        new_agency = create_agency(
          agency_id: 100,
          agency_type: 'ParentAgency',
          agency_name: 'Original Name',
          created_at: 5.days.ago
        )

        allow(pam_logic).to receive(:fetch_or_create_pam_obj)
          .with(BaseAgency, nil, nil, create_pamobj: true, update_pamobj: false)
          .and_return(nil)
        allow(pam_logic).to receive(:fetch_or_create_pam_obj)
          .with(BaseAgency, 'SF001', 'Updated Name', create_pamobj: true, update_pamobj: true)
          .and_yield(new_agency)
          .and_return(new_agency)

        sf_data = base_sf_agency.merge('Name' => 'Updated Name')

        expect { main_logic.send(:process_sf_agency, sf_data) }.not_to raise_error
        expect(new_agency.agency_name).to eq('Updated Name')
      end

      it 'blocks changes to old agencies (past cutoff)' do
        old_agency = create_agency(
          agency_id: 100,
          agency_type: 'ParentAgency',
          agency_name: 'Original Name',
          created_at: 15.days.ago
        )

        allow(pam_logic).to receive(:fetch_or_create_pam_obj)
          .with(BaseAgency, nil, nil, create_pamobj: true, update_pamobj: false)
          .and_return(nil)
        allow(pam_logic).to receive(:fetch_or_create_pam_obj)
          .with(BaseAgency, 'SF001', 'Blocked Update', create_pamobj: true, update_pamobj: true)
          .and_yield(old_agency)
          .and_return(old_agency)

        sf_data = base_sf_agency.merge('Name' => 'Blocked Update')

        expect { main_logic.send(:process_sf_agency, sf_data) }.not_to raise_error
        expect(old_agency.agency_name).to eq('Original Name') # Should remain unchanged
      end

      it 'blocks grandparent promotion when past cutoff' do
        old_grandparent = create_agency(
          agency_id: 100,
          agency_type: 'ParentAgency',
          created_at: 15.days.ago
        )
        parent = create_agency(
          agency_id: 101,
          agency_type: 'ParentAgency',
          parent_agency_id: 100
        )
        child = create_agency(agency_id: 102, agency_type: 'Agency')

        sf_data = base_sf_agency.merge('ParentAgency__c' => 'SF_PARENT')

        allow(pam_logic).to receive(:fetch_or_create_pam_obj)
          .with(BaseAgency, 'SF_PARENT', nil, create_pamobj: true, update_pamobj: false)
          .and_return(parent)
        allow(pam_logic).to receive(:fetch_or_create_pam_obj)
          .with(BaseAgency, 'SF001', 'Test Agency', create_pamobj: true, update_pamobj: true)
          .and_yield(child)
          .and_return(child)

        expect { main_logic.send(:process_sf_agency, sf_data) }.not_to raise_error

        # Reload using BaseAgency since type may have changed
        reloaded_old_grandparent = BaseAgency.find(old_grandparent.agency_id)
        expect(reloaded_old_grandparent.agency_type).to eq('ParentAgency') # No promotion
      end
    end

    context 'with validation failure scenarios' do
      it 'reverts parent assignment when circular relationship detected' do
        # Create existing parent-child relationship
        existing_parent = create_agency(agency_id: 100, agency_type: 'ParentAgency')
        existing_child = create_agency(agency_id: 101, agency_type: 'Agency', parent_agency_id: 100)

        # Try to make parent a child of its own child (circular)
        sf_data = {
          'Id' => 'SF_PARENT',
          'Name' => 'Parent Agency',
          'ParentAgency__c' => 'SF_CHILD',
          'agency_type' => 'Agency'
        }

        allow(pam_logic).to receive(:fetch_or_create_pam_obj)
          .with(BaseAgency, 'SF_CHILD', nil, create_pamobj: true, update_pamobj: false)
          .and_return(existing_child)
        allow(pam_logic).to receive(:fetch_or_create_pam_obj)
          .with(BaseAgency, 'SF_PARENT', 'Parent Agency', create_pamobj: true, update_pamobj: true)
          .and_yield(existing_parent)
          .and_return(existing_parent)

        expect { main_logic.send(:process_sf_agency, sf_data) }.not_to raise_error

        # Reload using BaseAgency since type may have changed
        reloaded_existing_parent = BaseAgency.find(existing_parent.agency_id)
        expect(reloaded_existing_parent.parent_agency_id).to eq(-1) # Should be reverted
      end

      it 'reverts parent assignment when deep hierarchy detected' do
        # Create 3-level hierarchy
        level1 = create_agency(agency_id: 100, agency_type: 'GrandParentAgency')
        level2 = create_agency(agency_id: 101, agency_type: 'ParentAgency', parent_agency_id: 100)
        level3 = create_agency(agency_id: 102, agency_type: 'Agency', parent_agency_id: 101)
        level4 = create_agency(agency_id: 103, agency_type: 'Agency')

        # Try to create 4th level
        sf_data = {
          'Id' => 'SF_LEVEL4',
          'Name' => 'Level 4 Agency',
          'ParentAgency__c' => 'SF_LEVEL3',
          'agency_type' => 'Agency'
        }

        allow(pam_logic).to receive(:fetch_or_create_pam_obj)
          .with(BaseAgency, 'SF_LEVEL3', nil, create_pamobj: true, update_pamobj: false)
          .and_return(level3)
        allow(pam_logic).to receive(:fetch_or_create_pam_obj)
          .with(BaseAgency, 'SF_LEVEL4', 'Level 4 Agency', create_pamobj: true, update_pamobj: true)
          .and_yield(level4)
          .and_return(level4)

        expect { main_logic.send(:process_sf_agency, sf_data) }.not_to raise_error
        expect(level4.reload.parent_agency_id).to eq(-1) # Should be reverted
      end
    end

    context 'with Salesforce data edge cases' do
      it 'handles empty parent agency ID' do
        agency = create_agency(agency_id: 100, agency_type: 'Agency')
        sf_data = base_sf_agency.merge('ParentAgency__c' => '')

        allow(pam_logic).to receive(:fetch_or_create_pam_obj)
          .with(BaseAgency, '', nil, create_pamobj: true, update_pamobj: false)
          .and_return(nil)
        allow(pam_logic).to receive(:fetch_or_create_pam_obj)
          .with(BaseAgency, 'SF001', 'Test Agency', create_pamobj: true, update_pamobj: true)
          .and_yield(agency)
          .and_return(agency)

        expect { main_logic.send(:process_sf_agency, sf_data) }.not_to raise_error
        expect(agency.parent_agency_id).to eq(-1)
      end

      it 'handles whitespace-only parent agency ID' do
        agency = create_agency(agency_id: 100, agency_type: 'Agency')
        sf_data = base_sf_agency.merge('ParentAgency__c' => '   ')

        allow(pam_logic).to receive(:fetch_or_create_pam_obj)
          .with(BaseAgency, '   ', nil, create_pamobj: true, update_pamobj: false)
          .and_return(nil)
        allow(pam_logic).to receive(:fetch_or_create_pam_obj)
          .with(BaseAgency, 'SF001', 'Test Agency', create_pamobj: true, update_pamobj: true)
          .and_yield(agency)
          .and_return(agency)

        expect { main_logic.send(:process_sf_agency, sf_data) }.not_to raise_error
        expect(agency.parent_agency_id).to eq(-1)
      end

      it 'handles mixed cutoff scenarios in promotion chain' do
        # Old grandparent (past cutoff) with new parent (within cutoff)
        old_grandparent = create_agency(
          agency_id: 100,
          agency_type: 'ParentAgency',
          created_at: 15.days.ago
        )
        new_parent = create_agency(
          agency_id: 101,
          agency_type: 'ParentAgency',
          parent_agency_id: 100,
          created_at: 5.days.ago
        )
        child = create_agency(agency_id: 102, agency_type: 'Agency')

        sf_data = base_sf_agency.merge('ParentAgency__c' => 'SF_PARENT')

        allow(pam_logic).to receive(:fetch_or_create_pam_obj)
          .with(BaseAgency, 'SF_PARENT', nil, create_pamobj: true, update_pamobj: false)
          .and_return(new_parent)
        allow(pam_logic).to receive(:fetch_or_create_pam_obj)
          .with(BaseAgency, 'SF001', 'Test Agency', create_pamobj: true, update_pamobj: true)
          .and_yield(child)
          .and_return(child)

        expect { main_logic.send(:process_sf_agency, sf_data) }.not_to raise_error

        # Old grandparent should NOT be promoted (past cutoff)
        reloaded_old_grandparent = BaseAgency.find(old_grandparent.agency_id)
        expect(reloaded_old_grandparent.agency_type).to eq('ParentAgency')
        # Child should still be assigned to parent
        expect(child.parent_agency_id).to eq(new_parent.agency_id)
      end
    end
  end

  describe '#process_sf_industry with comprehensive scenarios' do
    let(:sf_industry_data) do
      {
        'Id' => 'IND001',
        'Name' => 'Technology'
      }
    end

    before do
      # Set up default mock behavior for pam_logic
      allow(pam_logic).to receive(:fetch_or_create_pam_obj)
    end

    context 'with new industry creation' do
      it 'creates new category from Salesforce industry' do
        category_mock = double('Category', product_category_name: nil)
        allow(category_mock).to receive(:product_category_name=)

        expect(pam_logic).to receive(:fetch_or_create_pam_obj).with(
          Category,
          'IND001',
          'Technology',
          create_pamobj: true,
          update_pamobj: true
        ).and_yield(category_mock)

        expect { main_logic.send(:process_sf_industry, sf_industry_data) }.not_to raise_error
      end

      it 'updates existing category name' do
        existing_category = double('Category', product_category_name: 'Old Tech')

        expect(pam_logic).to receive(:fetch_or_create_pam_obj).with(
          Category,
          'IND001',
          'Technology',
          create_pamobj: true,
          update_pamobj: true
        ).and_yield(existing_category)

        expect(existing_category).to receive(:product_category_name=).with('Technology')

        expect { main_logic.send(:process_sf_industry, sf_industry_data) }.not_to raise_error
      end
    end

    context 'with edge cases' do
      it 'handles empty industry name' do
        sf_data = sf_industry_data.merge('Name' => '')

        category_mock = double('Category', product_category_name: nil)
        allow(category_mock).to receive(:product_category_name=)

        expect(pam_logic).to receive(:fetch_or_create_pam_obj).with(
          Category,
          'IND001',
          '',
          create_pamobj: true,
          update_pamobj: true
        ).and_yield(category_mock)

        expect { main_logic.send(:process_sf_industry, sf_data) }.not_to raise_error
      end

      it 'handles nil industry name' do
        sf_data = sf_industry_data.merge('Name' => nil)

        category_mock = double('Category', product_category_name: nil)
        allow(category_mock).to receive(:product_category_name=)

        expect(pam_logic).to receive(:fetch_or_create_pam_obj).with(
          Category,
          'IND001',
          nil,
          create_pamobj: true,
          update_pamobj: true
        ).and_yield(category_mock)

        expect { main_logic.send(:process_sf_industry, sf_data) }.not_to raise_error
      end

      it 'handles special characters in industry name' do
        sf_data = sf_industry_data.merge('Name' => 'Tech & Media (B2B)')

        category_mock = double('Category', product_category_name: nil)
        allow(category_mock).to receive(:product_category_name=)

        expect(pam_logic).to receive(:fetch_or_create_pam_obj).with(
          Category,
          'IND001',
          'Tech & Media (B2B)',
          create_pamobj: true,
          update_pamobj: true
        ).and_yield(category_mock)

        expect { main_logic.send(:process_sf_industry, sf_data) }.not_to raise_error
      end
    end
  end

  describe '#process_sf_advertiser with comprehensive scenarios' do
    let(:sf_advertiser_data) do
      {
        'Id' => 'ADV001',
        'Name' => 'Test Advertiser Corp',
        'Industry__c' => 'IND001',
        'Industry_Name__c' => 'Technology',
        'BillingState' => 'CA',
        'Target_Account__c' => 'Yes',
        'D2C_Target_Account__c' => 'No',
        'Global_Account__c' => 'Yes',
        'Peacock_Target_Account__c' => 'No'
      }
    end

    let(:mock_category) { double('Category', product_category_id: 100) }
    let(:mock_geo_state) { double('GeoState', geo_state_id: 5) }

    before do
      # Set up default mock behavior for pam_logic
      allow(pam_logic).to receive(:fetch_or_create_pam_obj)
      allow(pam_logic).to receive(:fetch_geo_state).and_return(mock_geo_state)
    end

    context 'with new advertiser creation' do
      it 'creates advertiser with all required fields' do
        # Mock category creation
        category_mock_for_creation = double('Category', product_category_name: nil)
        allow(category_mock_for_creation).to receive(:product_category_name=)

        expect(pam_logic).to receive(:fetch_or_create_pam_obj).with(
          Category,
          'IND001',
          'Technology',
          create_pamobj: true,
          update_pamobj: false
        ).and_yield(category_mock_for_creation).and_return(mock_category)

        # Mock geo state lookup
        expect(pam_logic).to receive(:fetch_geo_state).with('CA').and_return(mock_geo_state)

        # Mock advertiser creation with all required methods
        advertiser_mock = double('Advertiser')
        allow(advertiser_mock).to receive(:new_record?).and_return(true)
        expect(advertiser_mock).to receive(:advertiser_name=).with('Test Advertiser Corp')
        expect(advertiser_mock).to receive(:default_category=).with(mock_category)
        expect(advertiser_mock).to receive(:main_state_id=).with(5)
        expect(advertiser_mock).to receive(:target_account=).with(true)
        expect(advertiser_mock).to receive(:d2c_target_account=).with(false)
        expect(advertiser_mock).to receive(:global_account=).with(true)
        expect(advertiser_mock).to receive(:peacock_target_account=).with(false)
        expect(advertiser_mock).to receive(:active=).with(true)

        expect(pam_logic).to receive(:fetch_or_create_pam_obj).with(
          Advertiser,
          'ADV001',
          'Test Advertiser Corp',
          create_pamobj: true,
          update_pamobj: true
        ).and_yield(advertiser_mock)

        expect { main_logic.send(:process_sf_advertiser, sf_advertiser_data) }.not_to raise_error
      end

      it 'creates category if it does not exist' do
        category_mock = double('Category')
        expect(category_mock).to receive(:product_category_name=).with('Technology')

        expect(pam_logic).to receive(:fetch_or_create_pam_obj).with(
          Category,
          'IND001',
          'Technology',
          create_pamobj: true,
          update_pamobj: false
        ).and_yield(category_mock).and_return(mock_category)

        # Mock the advertiser creation
        advertiser_mock = create_advertiser_mock

        expect(pam_logic).to receive(:fetch_or_create_pam_obj).with(
          Advertiser,
          'ADV001',
          'Test Advertiser Corp',
          create_pamobj: true,
          update_pamobj: true
        ).and_yield(advertiser_mock)

        expect { main_logic.send(:process_sf_advertiser, sf_advertiser_data) }.not_to raise_error
      end
    end

    context 'with boolean field handling' do
      it 'correctly converts "Yes" to true and "No" to false' do
        advertiser_mock = create_advertiser_mock

        # Test boolean conversions
        expect(advertiser_mock).to receive(:target_account=).with(true)  # "Yes" -> true
        expect(advertiser_mock).to receive(:d2c_target_account=).with(false)  # "No" -> false
        expect(advertiser_mock).to receive(:global_account=).with(true)  # "Yes" -> true
        expect(advertiser_mock).to receive(:peacock_target_account=).with(false)  # "No" -> false

        allow(pam_logic).to receive(:fetch_or_create_pam_obj).and_return(mock_category)
        expect(pam_logic).to receive(:fetch_or_create_pam_obj).with(
          Advertiser, 'ADV001', 'Test Advertiser Corp', create_pamobj: true, update_pamobj: true
        ).and_yield(advertiser_mock)

        expect { main_logic.send(:process_sf_advertiser, sf_advertiser_data) }.not_to raise_error
      end

      it 'handles mixed case boolean values' do
        sf_data = sf_advertiser_data.merge(
          'Target_Account__c' => 'yes',
          'D2C_Target_Account__c' => 'NO',
          'Global_Account__c' => 'Yes',
          'Peacock_Target_Account__c' => 'no'
        )

        advertiser_mock = create_advertiser_mock

        expect(advertiser_mock).to receive(:target_account=).with(true)   # "yes" -> true
        expect(advertiser_mock).to receive(:d2c_target_account=).with(false)  # "NO" -> false
        expect(advertiser_mock).to receive(:global_account=).with(true)   # "Yes" -> true
        expect(advertiser_mock).to receive(:peacock_target_account=).with(false)  # "no" -> false

        allow(pam_logic).to receive(:fetch_or_create_pam_obj).and_return(mock_category)
        expect(pam_logic).to receive(:fetch_or_create_pam_obj).with(
          Advertiser, 'ADV001', 'Test Advertiser Corp', create_pamobj: true, update_pamobj: true
        ).and_yield(advertiser_mock)

        expect { main_logic.send(:process_sf_advertiser, sf_data) }.not_to raise_error
      end

      it 'handles nil boolean values' do
        sf_data = sf_advertiser_data.merge(
          'Target_Account__c' => nil,
          'D2C_Target_Account__c' => nil,
          'Global_Account__c' => nil,
          'Peacock_Target_Account__c' => nil
        )

        advertiser_mock = create_advertiser_mock

        expect(advertiser_mock).to receive(:target_account=).with(false)  # nil -> false
        expect(advertiser_mock).to receive(:d2c_target_account=).with(false)  # nil -> false
        expect(advertiser_mock).to receive(:global_account=).with(false)  # nil -> false
        expect(advertiser_mock).to receive(:peacock_target_account=).with(false)  # nil -> false

        allow(pam_logic).to receive(:fetch_or_create_pam_obj).and_return(mock_category)
        expect(pam_logic).to receive(:fetch_or_create_pam_obj).with(
          Advertiser, 'ADV001', 'Test Advertiser Corp', create_pamobj: true, update_pamobj: true
        ).and_yield(advertiser_mock)

        expect { main_logic.send(:process_sf_advertiser, sf_data) }.not_to raise_error
      end
    end

    context 'with edge cases and error handling' do
      it 'handles missing industry information' do
        sf_data = sf_advertiser_data.merge(
          'Industry__c' => nil,
          'Industry_Name__c' => nil
        )

        advertiser_mock = create_advertiser_mock

        expect(pam_logic).to receive(:fetch_or_create_pam_obj).with(
          Advertiser, 'ADV001', 'Test Advertiser Corp', create_pamobj: true, update_pamobj: true
        ).and_yield(advertiser_mock)

        expect { main_logic.send(:process_sf_advertiser, sf_data) }.not_to raise_error
      end

      it 'handles missing billing state' do
        sf_data = sf_advertiser_data.merge('BillingState' => nil)

        advertiser_mock = create_advertiser_mock

        allow(pam_logic).to receive(:fetch_or_create_pam_obj).and_return(mock_category)
        expect(pam_logic).to receive(:fetch_or_create_pam_obj).with(
          Advertiser, 'ADV001', 'Test Advertiser Corp', create_pamobj: true, update_pamobj: true
        ).and_yield(advertiser_mock)

        expect { main_logic.send(:process_sf_advertiser, sf_data) }.not_to raise_error
      end

      it 'handles empty advertiser name' do
        sf_data = sf_advertiser_data.merge('Name' => '')

        advertiser_mock = create_advertiser_mock
        expect(advertiser_mock).to receive(:advertiser_name=).with('')

        allow(pam_logic).to receive(:fetch_or_create_pam_obj).and_return(mock_category)
        expect(pam_logic).to receive(:fetch_or_create_pam_obj).with(
          Advertiser, 'ADV001', '', create_pamobj: true, update_pamobj: true
        ).and_yield(advertiser_mock)

        expect { main_logic.send(:process_sf_advertiser, sf_data) }.not_to raise_error
      end

      it 'handles special characters in advertiser name' do
        sf_data = sf_advertiser_data.merge('Name' => 'Test Corp & Co. (USA)')

        advertiser_mock = create_advertiser_mock
        expect(advertiser_mock).to receive(:advertiser_name=).with('Test Corp & Co. (USA)')

        allow(pam_logic).to receive(:fetch_or_create_pam_obj).and_return(mock_category)
        expect(pam_logic).to receive(:fetch_or_create_pam_obj).with(
          Advertiser, 'ADV001', 'Test Corp & Co. (USA)', create_pamobj: true, update_pamobj: true
        ).and_yield(advertiser_mock)

        expect { main_logic.send(:process_sf_advertiser, sf_data) }.not_to raise_error
      end
    end
  end

  describe '#process_industries with error handling' do
    before do
      allow(sf_logic).to receive(:get_sf_industries).and_return([])
    end

    context 'with successful processing' do
      it 'processes multiple industries successfully' do
        industries = [
          { 'Id' => 'IND001', 'Name' => 'Technology' },
          { 'Id' => 'IND002', 'Name' => 'Healthcare' },
          { 'Id' => 'IND003', 'Name' => 'Finance' }
        ]

        expect(sf_logic).to receive(:get_sf_industries).with(nil, Salesforce::Sync::SyncConfig.max_records).and_return(industries)

        industries.each do |industry|
          expect(sf_logic).to receive(:sf_industry_status).with(industry, 'pickedup')
          expect(main_logic).to receive(:process_sf_industry).with(industry)
          expect(sf_logic).to receive(:sf_industry_status).with(industry, 'processed')
        end

        expect { main_logic.send(:process_industries) }.not_to raise_error
      end

      it 'handles empty industry list' do
        expect(sf_logic).to receive(:get_sf_industries).with(nil, Salesforce::Sync::SyncConfig.max_records).and_return([])
        expect { main_logic.send(:process_industries) }.not_to raise_error
      end
    end

    context 'with error scenarios' do
      it 'handles Salesforce connection errors' do
        expect(sf_logic).to receive(:get_sf_industries).and_raise(StandardError.new('SF Connection Failed'))

        expect { main_logic.send(:process_industries) }.to raise_error(StandardError, 'SF Connection Failed')
      end

      it 'continues processing after individual industry error' do
        industries = [
          { 'Id' => 'IND001', 'Name' => 'Technology' },
          { 'Id' => 'IND002', 'Name' => 'Healthcare' }
        ]

        expect(sf_logic).to receive(:get_sf_industries).and_return(industries)
        expect(sf_logic).to receive(:sf_industry_status).with(industries[0], 'pickedup')
        expect(main_logic).to receive(:process_sf_industry).with(industries[0]).and_raise(StandardError.new('Industry 1 failed'))
        expect(sf_logic).to receive(:sf_industry_status).with(industries[1], 'pickedup')
        expect(main_logic).to receive(:process_sf_industry).with(industries[1])
        expect(sf_logic).to receive(:sf_industry_status).with(industries[1], 'processed')

        # Should continue processing despite first industry failing
        expect { main_logic.send(:process_industries) }.not_to raise_error
      end
    end
  end

  describe '#process_advertisers with error handling' do
    before do
      allow(sf_logic).to receive(:get_sf_advertisers).and_return([])
    end

    context 'with successful processing' do
      it 'processes multiple advertisers successfully' do
        advertisers = [
          { 'Id' => 'ADV001', 'Name' => 'Advertiser 1', 'Industry__c' => 'IND001' },
          { 'Id' => 'ADV002', 'Name' => 'Advertiser 2', 'Industry__c' => 'IND002' }
        ]

        expect(sf_logic).to receive(:get_sf_advertisers).with(nil, Salesforce::Sync::SyncConfig.max_records).and_return(advertisers)

        advertisers.each do |advertiser|
          expect(sf_logic).to receive(:sf_advertiser_status).with(advertiser, 'pickedup')
          expect(main_logic).to receive(:process_sf_advertiser).with(advertiser)
          expect(sf_logic).to receive(:sf_advertiser_status).with(advertiser, 'processed')
        end

        expect { main_logic.send(:process_advertisers) }.not_to raise_error
      end

      it 'handles empty advertiser list' do
        expect(sf_logic).to receive(:get_sf_advertisers).with(nil, Salesforce::Sync::SyncConfig.max_records).and_return([])
        expect { main_logic.send(:process_advertisers) }.not_to raise_error
      end
    end

    context 'with error scenarios' do
      it 'handles Salesforce connection errors' do
        expect(sf_logic).to receive(:get_sf_advertisers).and_raise(StandardError.new('SF Connection Failed'))

        expect { main_logic.send(:process_advertisers) }.to raise_error(StandardError, 'SF Connection Failed')
      end

      it 'continues processing after individual advertiser error' do
        advertisers = [
          { 'Id' => 'ADV001', 'Name' => 'Advertiser 1' },
          { 'Id' => 'ADV002', 'Name' => 'Advertiser 2' }
        ]

        expect(sf_logic).to receive(:get_sf_advertisers).and_return(advertisers)
        expect(sf_logic).to receive(:sf_advertiser_status).with(advertisers[0], 'pickedup')
        expect(main_logic).to receive(:process_sf_advertiser).with(advertisers[0]).and_raise(StandardError.new('Advertiser 1 failed'))
        expect(sf_logic).to receive(:sf_advertiser_status).with(advertisers[1], 'pickedup')
        expect(main_logic).to receive(:process_sf_advertiser).with(advertisers[1])
        expect(sf_logic).to receive(:sf_advertiser_status).with(advertisers[1], 'processed')

        # Should continue processing despite first advertiser failing
        expect { main_logic.send(:process_advertisers) }.not_to raise_error
      end
    end
  end

  describe 'Integration scenarios' do
    context 'with realistic Salesforce data' do
      it 'processes complete advertiser with industry chain' do
        # Simulate realistic SF data
        sf_industry = { 'Id' => 'IND_TECH_001', 'Name' => 'Technology & Software' }
        sf_advertiser = {
          'Id' => 'ADV_MICROSOFT_001',
          'Name' => 'Microsoft Corporation',
          'Industry__c' => 'IND_TECH_001',
          'Industry_Name__c' => 'Technology & Software',
          'BillingState' => 'WA',
          'Target_Account__c' => 'Yes',
          'D2C_Target_Account__c' => 'No',
          'Global_Account__c' => 'Yes',
          'Peacock_Target_Account__c' => 'No'
        }

        # Mock the category creation/lookup
        category_mock = double('Category', product_category_id: 150)
        category_mock_for_creation = create_category_mock

        # First call from process_sf_industry (update_pamobj: true)
        expect(pam_logic).to receive(:fetch_or_create_pam_obj).with(
          Category, 'IND_TECH_001', 'Technology & Software', create_pamobj: true, update_pamobj: true
        ).and_yield(category_mock_for_creation).and_return(category_mock)

        # Second call from process_sf_advertiser (update_pamobj: false)
        expect(pam_logic).to receive(:fetch_or_create_pam_obj).with(
          Category, 'IND_TECH_001', 'Technology & Software', create_pamobj: true, update_pamobj: false
        ).and_yield(category_mock_for_creation).and_return(category_mock)

        # Mock geo state lookup
        geo_state_mock = double('GeoState', geo_state_id: 48)
        expect(pam_logic).to receive(:fetch_geo_state).with('WA').and_return(geo_state_mock)

        # Mock advertiser creation
        advertiser_mock = create_advertiser_mock
        expect(advertiser_mock).to receive(:advertiser_name=).with('Microsoft Corporation')
        expect(advertiser_mock).to receive(:default_category=).with(category_mock)
        expect(advertiser_mock).to receive(:main_state_id=).with(48)
        expect(advertiser_mock).to receive(:target_account=).with(true)
        expect(advertiser_mock).to receive(:d2c_target_account=).with(false)
        expect(advertiser_mock).to receive(:global_account=).with(true)
        expect(advertiser_mock).to receive(:peacock_target_account=).with(false)
        expect(advertiser_mock).to receive(:active=).with(true)

        expect(pam_logic).to receive(:fetch_or_create_pam_obj).with(
          Advertiser, 'ADV_MICROSOFT_001', 'Microsoft Corporation', create_pamobj: true, update_pamobj: true
        ).and_yield(advertiser_mock)

        # Process industry first, then advertiser
        expect { main_logic.send(:process_sf_industry, sf_industry) }.not_to raise_error
        expect { main_logic.send(:process_sf_advertiser, sf_advertiser) }.not_to raise_error
      end
    end
  end

  private

  def create_advertiser_mock
    advertiser_mock = double('Advertiser')
    allow(advertiser_mock).to receive(:new_record?).and_return(true)
    allow(advertiser_mock).to receive(:advertiser_name=)
    allow(advertiser_mock).to receive(:default_category=)
    allow(advertiser_mock).to receive(:main_state_id=)
    allow(advertiser_mock).to receive(:target_account=)
    allow(advertiser_mock).to receive(:d2c_target_account=)
    allow(advertiser_mock).to receive(:global_account=)
    allow(advertiser_mock).to receive(:peacock_target_account=)
    allow(advertiser_mock).to receive(:active=)
    advertiser_mock
  end

  def create_category_mock
    category_mock = double('Category', product_category_name: nil)
    allow(category_mock).to receive(:product_category_name=)
    category_mock
  end

  def create_agency(attributes = {})
    # Generate unique agency name to avoid validation conflicts
    unique_name = attributes[:agency_name] || "Test Agency #{SecureRandom.hex(4)}"

    defaults = {
      agency_name: unique_name,
      agency_type: 'Agency',
      parent_agency_id: -1,
      active: true
    }

    # Merge attributes but handle created_at specially for Oracle
    agency_attributes = defaults.merge(attributes.except(:created_at))

    # Determine which concrete class to use based on agency_type
    agency_class = case agency_attributes[:agency_type]
                   when 'GrandParentAgency'
                     GrandParentAgency
                   when 'ParentAgency'
                     ParentAgency
                   else
                     Agency
                   end

    # Create the agency using the appropriate concrete class
    agency = agency_class.create!(agency_attributes)

    # Set created_at using raw SQL if provided to avoid Oracle date parsing issues
    if attributes[:created_at]
      set_agency_created_at(agency, attributes[:created_at])
    end

    agency
  end

  def set_agency_created_at(agency, created_at_value)
    # Convert the created_at value to a proper timestamp
    timestamp = case created_at_value
                when String
                  Time.parse(created_at_value)
                when Time, DateTime
                  created_at_value
                else
                  # Handle ActiveSupport::Duration like 5.days.ago
                  created_at_value.respond_to?(:to_time) ? created_at_value.to_time : Time.current
                end

    # Use raw SQL to update created_at to avoid Oracle date parsing issues
    connection = ActiveRecord::Base.connection
    oracle_timestamp = timestamp.strftime('%Y-%m-%d %H:%M:%S')

    # Use parameterized query to avoid SQL injection
    connection.execute(
      "UPDATE agency SET created_at = TIMESTAMP '#{oracle_timestamp}' WHERE agency_id = #{agency.agency_id}"
    )

    # Reload the agency to reflect the updated created_at
    agency.reload
  end
end
