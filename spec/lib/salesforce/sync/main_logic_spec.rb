# frozen_string_literal: true

require 'spec_helper'

RSpec.describe Salesforce::Sync::MainLogic, type: :model do
  let(:main_logic) { described_class.new }
  let(:sf_logic) { instance_double(Salesforce::Sync::SfLogic) }
  let(:pam_logic) { instance_double(Salesforce::Sync::<PERSON><PERSON>og<PERSON>) }

  before do
    allow(Salesforce::Sync::SfLogic).to receive(:new).and_return(sf_logic)
    allow(Salesforce::Sync::PamLog<PERSON>).to receive(:new).and_return(pam_logic)
    allow(sf_logic).to receive(:connect_sf)
  end

  describe '#process_industries' do
    let(:sf_industry) do
      {
        'Id' => 'SF001',
        'Name' => 'Test Industry'
      }
    end

    before do
      allow(sf_logic).to receive(:get_sf_industries).and_return([sf_industry])
      allow(sf_logic).to receive(:sf_industry_status)
      allow(pam_logic).to receive(:fetch_or_create_pam_obj)
    end

    it 'processes Salesforce industries' do
      expect(sf_logic).to receive(:get_sf_industries).with(nil, 40)
      expect(sf_logic).to receive(:sf_industry_status).with(sf_industry, 'pickedup')
      expect(pam_logic).to receive(:fetch_or_create_pam_obj).with(
        PamClient::Category,
        'SF001',
        'Test Industry',
        create_pamobj: true,
        update_pamobj: true
      )
      expect(sf_logic).to receive(:sf_industry_status).with(sf_industry, 'processed')

      main_logic.process_industries
    end

    it 'handles errors during processing' do
      allow(pam_logic).to receive(:fetch_or_create_pam_obj).and_raise(StandardError.new('Test error'))
      expect(BacktraceHelper).to receive(:print_exception)

      main_logic.process_industries
    end
  end

  describe '#process_agencies' do
    let(:sf_agencies) do
      [
        {
          'Id' => 'SF002',
          'Name' => 'Test Agency',
          'ParentAgency__c' => nil,
          'IsParent__c' => true,
          'isActive__c' => true
        },
        {
          'Id' => 'SF003',
          'Name' => 'Child Agency',
          'ParentAgency__c' => 'SF002',
          'IsParent__c' => false,
          'isActive__c' => true
        }
      ]
    end

    before do
      allow(sf_logic).to receive(:get_sf_agencies).and_return(sf_agencies)
      allow(sf_logic).to receive(:sf_agency_status)
      allow(pam_logic).to receive(:fetch_or_create_pam_obj)
    end

    it 'processes Salesforce agencies in correct order' do
      expect(sf_logic).to receive(:get_sf_agencies).with(nil, 500)
      sf_agencies.each do |agency|
        expect(sf_logic).to receive(:sf_agency_status).with(agency, 'pickedup')
        expect(pam_logic).to receive(:fetch_or_create_pam_obj).with(
          PamClient::BaseAgency,
          agency['Id'],
          agency['Name'],
          create_pamobj: true,
          update_pamobj: true
        )
        expect(sf_logic).to receive(:sf_agency_status).with(agency, 'processed')
      end

      main_logic.process_agencies
    end

    it 'handles errors during processing' do
      allow(pam_logic).to receive(:fetch_or_create_pam_obj).and_raise(StandardError.new('Test error'))
      expect(BacktraceHelper).to receive(:print_exception).twice

      main_logic.process_agencies
    end
  end

  describe '#process_advertisers' do
    let(:sf_advertiser) do
      {
        'Id' => 'SF004',
        'Name' => 'Test Advertiser',
        'Industry__c' => 'IND001',
        'Industry_Name__c' => 'Test Industry',
        'BillingState' => 'CA',
        'isActive__c' => true,
        'Target_Account__c' => 'Yes',
        'D2C_Target_Account__c' => 'No',
        'Global_Account__c' => 'Yes',
        'Peacock_Target_Account__c' => 'No'
      }
    end

    before do
      allow(sf_logic).to receive(:get_sf_advertisers).and_return([sf_advertiser])
      allow(sf_logic).to receive(:sf_advertiser_status)
      allow(pam_logic).to receive(:fetch_or_create_pam_obj)
      allow(pam_logic).to receive(:fetch_geo_state).and_return(double(geo_state_id: 1))
    end

    it 'processes Salesforce advertisers' do
      expect(sf_logic).to receive(:get_sf_advertisers).with(nil, 40)
      expect(sf_logic).to receive(:sf_advertiser_status).with(sf_advertiser, 'pickedup')
      expect(pam_logic).to receive(:fetch_or_create_pam_obj).with(
        PamClient::Category,
        'IND001',
        'Test Industry',
        create_pamobj: true,
        update_pamobj: false
      )
      expect(pam_logic).to receive(:fetch_geo_state).with('CA')
      expect(pam_logic).to receive(:fetch_or_create_pam_obj).with(
        PamClient::Advertiser,
        'SF004',
        'Test Advertiser',
        create_pamobj: true,
        update_pamobj: true
      )
      expect(sf_logic).to receive(:sf_advertiser_status).with(sf_advertiser, 'processed')

      main_logic.process_advertisers
    end

    it 'handles errors during processing' do
      allow(pam_logic).to receive(:fetch_or_create_pam_obj).and_raise(StandardError.new('Test error'))
      expect(BacktraceHelper).to receive(:print_exception)

      main_logic.process_advertisers
    end
  end

  describe '#order_agencies' do
    let(:agencies) do
      [
        { 'Id' => 'SF001', 'IsParent__c' => true, 'ParentAgency__c' => nil },
        { 'Id' => 'SF002', 'IsParent__c' => true, 'ParentAgency__c' => 'SF001' },
        { 'Id' => 'SF003', 'IsParent__c' => false, 'ParentAgency__c' => 'SF002' }
      ]
    end

    it 'correctly orders agencies based on hierarchy' do
      ordered_agencies = main_logic.order_agencies(agencies)
      expect(ordered_agencies.map { |a| a['Id'] }).to eq(%w[SF001 SF002 SF003])
      expect(ordered_agencies.map { |a| a['agency_type'] }).to eq(%w[GrandParentAgency ParentAgency Agency])
    end
  end
end
