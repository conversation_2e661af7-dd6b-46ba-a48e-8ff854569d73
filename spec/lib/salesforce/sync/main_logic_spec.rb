# frozen_string_literal: true

require 'spec_helper'

RSpec.describe Salesforce::Sync::MainLogic, type: :model do
  let(:main_logic) { described_class.new }
  let(:sf_logic) { instance_double(Salesforce::Sync::SfLogic) }
  let(:pam_logic) { instance_double(Salesforce::Sync::<PERSON><PERSON>og<PERSON>) }

  before do
    allow(Salesforce::Sync::SfLogic).to receive(:new).and_return(sf_logic)
    allow(Salesforce::Sync::PamLog<PERSON>).to receive(:new).and_return(pam_logic)
    allow(sf_logic).to receive(:connect_sf)
  end

  describe '#process_industries' do
    let(:sf_industry) do
      {
        'Id' => 'SF001',
        'Name' => 'Test Industry'
      }
    end

    before do
      allow(sf_logic).to receive(:get_sf_industries).and_return([sf_industry])
      allow(sf_logic).to receive(:sf_industry_status)
      allow(pam_logic).to receive(:fetch_or_create_pam_obj)
    end

    it 'processes Salesforce industries' do
      expect(sf_logic).to receive(:get_sf_industries).with(nil, 40)
      expect(sf_logic).to receive(:sf_industry_status).with(sf_industry, 'pickedup')
      expect(pam_logic).to receive(:fetch_or_create_pam_obj).with(
        PamClient::Category,
        'SF001',
        'Test Industry',
        create_pamobj: true,
        update_pamobj: true
      )
      expect(sf_logic).to receive(:sf_industry_status).with(sf_industry, 'processed')

      main_logic.process_industries
    end

    it 'handles errors during processing' do
      allow(pam_logic).to receive(:fetch_or_create_pam_obj).and_raise(StandardError.new('Test error'))
      expect(BacktraceHelper).to receive(:print_exception)

      main_logic.process_industries
    end
  end

  describe '#process_agencies' do
    let(:sf_agencies) do
      [
        {
          'Id' => 'SF002',
          'Name' => 'Test Agency',
          'ParentAgency__c' => nil,
          'IsParent__c' => true,
          'isActive__c' => true
        },
        {
          'Id' => 'SF003',
          'Name' => 'Child Agency',
          'ParentAgency__c' => 'SF002',
          'IsParent__c' => false,
          'isActive__c' => true
        }
      ]
    end

    before do
      allow(sf_logic).to receive(:get_sf_agencies).and_return(sf_agencies)
      allow(sf_logic).to receive(:sf_agency_status)
      allow(pam_logic).to receive(:fetch_or_create_pam_obj)
    end

    it 'processes Salesforce agencies in correct order' do
      expect(sf_logic).to receive(:get_sf_agencies).with(nil, 500)
      sf_agencies.each do |agency|
        expect(sf_logic).to receive(:sf_agency_status).with(agency, 'pickedup')
        expect(pam_logic).to receive(:fetch_or_create_pam_obj).with(
          PamClient::BaseAgency,
          agency['Id'],
          agency['Name'],
          create_pamobj: true,
          update_pamobj: true
        )
        expect(sf_logic).to receive(:sf_agency_status).with(agency, 'processed')
      end

      main_logic.process_agencies
    end

    it 'handles errors during processing' do
      allow(pam_logic).to receive(:fetch_or_create_pam_obj).and_raise(StandardError.new('Test error'))
      expect(BacktraceHelper).to receive(:print_exception).twice

      main_logic.process_agencies
    end
  end

  describe '#process_advertisers' do
    let(:sf_advertiser) do
      {
        'Id' => 'SF004',
        'Name' => 'Test Advertiser',
        'Industry__c' => 'IND001',
        'Industry_Name__c' => 'Test Industry',
        'BillingState' => 'CA',
        'isActive__c' => true,
        'Target_Account__c' => 'Yes',
        'D2C_Target_Account__c' => 'No',
        'Global_Account__c' => 'Yes',
        'Peacock_Target_Account__c' => 'No'
      }
    end

    before do
      allow(sf_logic).to receive(:get_sf_advertisers).and_return([sf_advertiser])
      allow(sf_logic).to receive(:sf_advertiser_status)
      allow(pam_logic).to receive(:fetch_or_create_pam_obj)
      allow(pam_logic).to receive(:fetch_geo_state).and_return(double(geo_state_id: 1))
    end

    it 'processes Salesforce advertisers' do
      expect(sf_logic).to receive(:get_sf_advertisers).with(nil, 40)
      expect(sf_logic).to receive(:sf_advertiser_status).with(sf_advertiser, 'pickedup')
      expect(pam_logic).to receive(:fetch_or_create_pam_obj).with(
        PamClient::Category,
        'IND001',
        'Test Industry',
        create_pamobj: true,
        update_pamobj: false
      )
      expect(pam_logic).to receive(:fetch_geo_state).with('CA')
      expect(pam_logic).to receive(:fetch_or_create_pam_obj).with(
        PamClient::Advertiser,
        'SF004',
        'Test Advertiser',
        create_pamobj: true,
        update_pamobj: true
      )
      expect(sf_logic).to receive(:sf_advertiser_status).with(sf_advertiser, 'processed')

      main_logic.process_advertisers
    end

    it 'handles errors during processing' do
      allow(pam_logic).to receive(:fetch_or_create_pam_obj).and_raise(StandardError.new('Test error'))
      expect(BacktraceHelper).to receive(:print_exception)

      main_logic.process_advertisers
    end
  end

  describe '#order_agencies' do
    let(:agencies) do
      [
        { 'Id' => 'SF001', 'IsParent__c' => true, 'ParentAgency__c' => nil },
        { 'Id' => 'SF002', 'IsParent__c' => true, 'ParentAgency__c' => 'SF001' },
        { 'Id' => 'SF003', 'IsParent__c' => false, 'ParentAgency__c' => 'SF002' }
      ]
    end

    it 'correctly orders agencies based on hierarchy' do
      ordered_agencies = main_logic.order_agencies(agencies)
      expect(ordered_agencies.map { |a| a['Id'] }).to eq(%w[SF001 SF002 SF003])
      expect(ordered_agencies.map { |a| a['agency_type'] }).to eq(%w[GrandParentAgency ParentAgency Agency])
    end
  end

  describe '#validate_parent_assignment' do
    before do
      # Set test cutoff to 10 days
      allow(ENV).to receive(:fetch).with('AGENCY_PROTECTION_CUTOFF_DAYS', '10').and_return('10')
    end

    context 'with valid assignments' do
      it 'allows assignment when no parent' do
        expect(main_logic.send(:validate_parent_assignment, 1, -1)).to be true
      end

      it 'allows simple parent-child relationship' do
        parent = create_agency(agency_id: 100, agency_type: 'Agency')
        child = create_agency(agency_id: 101, agency_type: 'Agency')

        expect(main_logic.send(:validate_parent_assignment, child.agency_id, parent.agency_id)).to be true
      end

      it 'allows 3-level hierarchy (max allowed)' do
        grandparent = create_agency(agency_id: 100, agency_type: 'GrandParentAgency')
        parent = create_agency(agency_id: 101, agency_type: 'ParentAgency', parent_agency_id: 100)
        child = create_agency(agency_id: 102, agency_type: 'Agency')

        expect(main_logic.send(:validate_parent_assignment, child.agency_id, parent.agency_id)).to be true
      end
    end

    context 'with circular relationships' do
      it 'blocks direct circular relationship (A -> B, B -> A)' do
        agency_a = create_agency(agency_id: 100, agency_type: 'ParentAgency')
        agency_b = create_agency(agency_id: 101, agency_type: 'Agency', parent_agency_id: 100)

        expect(main_logic.send(:validate_parent_assignment, agency_a.agency_id, agency_b.agency_id)).to be false
      end

      it 'blocks 3-way circular relationship (A -> B -> C -> A)' do
        agency_a = create_agency(agency_id: 100, agency_type: 'GrandParentAgency')
        agency_b = create_agency(agency_id: 101, agency_type: 'ParentAgency', parent_agency_id: 100)
        agency_c = create_agency(agency_id: 102, agency_type: 'Agency', parent_agency_id: 101)

        expect(main_logic.send(:validate_parent_assignment, agency_a.agency_id, agency_c.agency_id)).to be false
      end

      it 'blocks self-assignment (A -> A)' do
        agency = create_agency(agency_id: 100, agency_type: 'Agency')

        expect(main_logic.send(:validate_parent_assignment, agency.agency_id, agency.agency_id)).to be false
      end
    end

    context 'with deep hierarchy violations' do
      it 'blocks 4-level hierarchy creation' do
        great_grandparent = create_agency(agency_id: 100, agency_type: 'GrandParentAgency')
        grandparent = create_agency(agency_id: 101, agency_type: 'ParentAgency', parent_agency_id: 100)
        parent = create_agency(agency_id: 102, agency_type: 'Agency', parent_agency_id: 101)
        child = create_agency(agency_id: 103, agency_type: 'Agency')

        expect(main_logic.send(:validate_parent_assignment, child.agency_id, parent.agency_id)).to be false
      end

      it 'blocks assignment that would create 4+ levels through child descendants' do
        parent = create_agency(agency_id: 100, agency_type: 'ParentAgency')
        child = create_agency(agency_id: 101, agency_type: 'ParentAgency')
        grandchild = create_agency(agency_id: 102, agency_type: 'ParentAgency', parent_agency_id: 101)
        great_grandchild = create_agency(agency_id: 103, agency_type: 'Agency', parent_agency_id: 102)

        expect(main_logic.send(:validate_parent_assignment, child.agency_id, parent.agency_id)).to be false
      end
    end

    context 'with non-existent agencies' do
      it 'blocks assignment when parent does not exist' do
        child = create_agency(agency_id: 100, agency_type: 'Agency')

        expect(main_logic.send(:validate_parent_assignment, child.agency_id, 999)).to be false
      end
    end

    context 'with existing circular data in database' do
      it 'handles existing circular relationships gracefully' do
        # Create circular relationship in database (should not happen in production)
        agency_a = create_agency(agency_id: 100, agency_type: 'ParentAgency')
        agency_b = create_agency(agency_id: 101, agency_type: 'Agency', parent_agency_id: 100)

        # Manually create circular relationship (bypassing validations)
        agency_a.update_column(:parent_agency_id, 101)

        # Should detect and block further circular assignments
        agency_c = create_agency(agency_id: 102, agency_type: 'Agency')
        expect(main_logic.send(:validate_parent_assignment, agency_c.agency_id, agency_a.agency_id)).to be false
      end

      it 'handles deep existing hierarchies' do
        # Create 4-level hierarchy in database (should not happen)
        level1 = create_agency(agency_id: 100, agency_type: 'GrandParentAgency')
        level2 = create_agency(agency_id: 101, agency_type: 'ParentAgency', parent_agency_id: 100)
        level3 = create_agency(agency_id: 102, agency_type: 'Agency', parent_agency_id: 101)
        level4 = create_agency(agency_id: 103, agency_type: 'Agency', parent_agency_id: 102)

        # Should block adding level 5
        level5 = create_agency(agency_id: 104, agency_type: 'Agency')
        expect(main_logic.send(:validate_parent_assignment, level5.agency_id, level4.agency_id)).to be false
      end
    end

    context 'with complex reparenting scenarios' do
      it 'handles moving subtree that would create deep hierarchy' do
        # Original structure: A -> B -> C, D -> E
        agency_a = create_agency(agency_id: 100, agency_type: 'GrandParentAgency')
        agency_b = create_agency(agency_id: 101, agency_type: 'ParentAgency', parent_agency_id: 100)
        agency_c = create_agency(agency_id: 102, agency_type: 'Agency', parent_agency_id: 101)
        agency_d = create_agency(agency_id: 103, agency_type: 'ParentAgency')
        agency_e = create_agency(agency_id: 104, agency_type: 'Agency', parent_agency_id: 103)

        # Try to make D a child of C (would create A -> B -> C -> D -> E = 5 levels)
        expect(main_logic.send(:validate_parent_assignment, agency_d.agency_id, agency_c.agency_id)).to be false
      end

      it 'allows valid reparenting within 3-level limit' do
        # Structure: A -> B, C -> D
        agency_a = create_agency(agency_id: 100, agency_type: 'ParentAgency')
        agency_b = create_agency(agency_id: 101, agency_type: 'Agency', parent_agency_id: 100)
        agency_c = create_agency(agency_id: 102, agency_type: 'ParentAgency')
        agency_d = create_agency(agency_id: 103, agency_type: 'Agency', parent_agency_id: 102)

        # Move D under B (creates A -> B -> D, which is valid)
        expect(main_logic.send(:validate_parent_assignment, agency_d.agency_id, agency_b.agency_id)).to be true
      end
    end

    context 'with boundary conditions' do
      it 'handles exactly 3-level hierarchy (boundary case)' do
        grandparent = create_agency(agency_id: 100, agency_type: 'GrandParentAgency')
        parent = create_agency(agency_id: 101, agency_type: 'ParentAgency', parent_agency_id: 100)
        child = create_agency(agency_id: 102, agency_type: 'Agency')

        # This should be exactly at the limit (3 levels)
        expect(main_logic.send(:validate_parent_assignment, child.agency_id, parent.agency_id)).to be true
      end

      it 'blocks exactly 4-level hierarchy (over boundary)' do
        level1 = create_agency(agency_id: 100, agency_type: 'GrandParentAgency')
        level2 = create_agency(agency_id: 101, agency_type: 'ParentAgency', parent_agency_id: 100)
        level3 = create_agency(agency_id: 102, agency_type: 'Agency', parent_agency_id: 101)
        level4 = create_agency(agency_id: 103, agency_type: 'Agency')

        # This would create exactly 4 levels (over limit)
        expect(main_logic.send(:validate_parent_assignment, level4.agency_id, level3.agency_id)).to be false
      end
    end
  end

  describe '#process_sf_agency with comprehensive scenarios' do
    let(:base_sf_agency) do
      {
        'Id' => 'SF001',
        'Name' => 'Test Agency',
        'IsParent__c' => false,
        'ParentAgency__c' => nil,
        'agency_type' => 'Agency'
      }
    end

    before do
      allow(ENV).to receive(:fetch).with('AGENCY_PROTECTION_CUTOFF_DAYS', '10').and_return('10')
    end

    context 'with new agency creation scenarios' do
      it 'creates standalone agency without parent' do
        agency = create_agency(agency_id: 100, agency_type: 'Agency')

        allow(pam_logic).to receive(:fetch_or_create_pam_obj)
          .with(BaseAgency, nil, nil, create_pamobj: true, update_pamobj: false)
          .and_return(nil)
        allow(pam_logic).to receive(:fetch_or_create_pam_obj)
          .with(BaseAgency, 'SF001', 'Test Agency', create_pamobj: true, update_pamobj: true)
          .and_yield(agency)
          .and_return(agency)

        expect { main_logic.send(:process_sf_agency, base_sf_agency) }.not_to raise_error
        expect(agency.parent_agency_id).to eq(-1)
      end

      it 'creates agency with valid parent and promotes parent type' do
        parent = create_agency(agency_id: 100, agency_type: 'Agency')
        child = create_agency(agency_id: 101, agency_type: 'Agency')

        sf_data = base_sf_agency.merge('ParentAgency__c' => 'SF_PARENT')

        allow(pam_logic).to receive(:fetch_or_create_pam_obj)
          .with(BaseAgency, 'SF_PARENT', nil, create_pamobj: true, update_pamobj: false)
          .and_return(parent)
        allow(pam_logic).to receive(:fetch_or_create_pam_obj)
          .with(BaseAgency, 'SF001', 'Test Agency', create_pamobj: true, update_pamobj: true)
          .and_yield(child)
          .and_return(child)

        expect { main_logic.send(:process_sf_agency, sf_data) }.not_to raise_error
        expect(parent.reload.agency_type).to eq('ParentAgency')
        expect(child.parent_agency_id).to eq(parent.agency_id)
      end

      it 'creates grandchild and promotes grandparent within cutoff' do
        grandparent = create_agency(
          agency_id: 100,
          agency_type: 'ParentAgency',
          created_at: 5.days.ago
        )
        parent = create_agency(
          agency_id: 101,
          agency_type: 'ParentAgency',
          parent_agency_id: 100,
          created_at: 5.days.ago
        )
        child = create_agency(agency_id: 102, agency_type: 'Agency')

        sf_data = base_sf_agency.merge('ParentAgency__c' => 'SF_PARENT')

        allow(pam_logic).to receive(:fetch_or_create_pam_obj)
          .with(BaseAgency, 'SF_PARENT', nil, create_pamobj: true, update_pamobj: false)
          .and_return(parent)
        allow(pam_logic).to receive(:fetch_or_create_pam_obj)
          .with(BaseAgency, 'SF001', 'Test Agency', create_pamobj: true, update_pamobj: true)
          .and_yield(child)
          .and_return(child)

        expect { main_logic.send(:process_sf_agency, sf_data) }.not_to raise_error
        expect(grandparent.reload.agency_type).to eq('GrandParentAgency')
      end
    end

    context 'with protection cutoff scenarios' do
      it 'allows changes to new agencies (within cutoff)' do
        new_agency = create_agency(
          agency_id: 100,
          agency_type: 'ParentAgency',
          agency_name: 'Original Name',
          created_at: 5.days.ago
        )

        allow(pam_logic).to receive(:fetch_or_create_pam_obj)
          .with(BaseAgency, nil, nil, create_pamobj: true, update_pamobj: false)
          .and_return(nil)
        allow(pam_logic).to receive(:fetch_or_create_pam_obj)
          .with(BaseAgency, 'SF001', 'Updated Name', create_pamobj: true, update_pamobj: true)
          .and_yield(new_agency)
          .and_return(new_agency)

        sf_data = base_sf_agency.merge('Name' => 'Updated Name')

        expect { main_logic.send(:process_sf_agency, sf_data) }.not_to raise_error
        expect(new_agency.agency_name).to eq('Updated Name')
      end

      it 'blocks changes to old agencies (past cutoff)' do
        old_agency = create_agency(
          agency_id: 100,
          agency_type: 'ParentAgency',
          agency_name: 'Original Name',
          created_at: 15.days.ago
        )

        allow(pam_logic).to receive(:fetch_or_create_pam_obj)
          .with(BaseAgency, nil, nil, create_pamobj: true, update_pamobj: false)
          .and_return(nil)
        allow(pam_logic).to receive(:fetch_or_create_pam_obj)
          .with(BaseAgency, 'SF001', 'Blocked Update', create_pamobj: true, update_pamobj: true)
          .and_yield(old_agency)
          .and_return(old_agency)

        sf_data = base_sf_agency.merge('Name' => 'Blocked Update')

        expect { main_logic.send(:process_sf_agency, sf_data) }.not_to raise_error
        expect(old_agency.agency_name).to eq('Original Name') # Should remain unchanged
      end

      it 'blocks grandparent promotion when past cutoff' do
        old_grandparent = create_agency(
          agency_id: 100,
          agency_type: 'ParentAgency',
          created_at: 15.days.ago
        )
        parent = create_agency(
          agency_id: 101,
          agency_type: 'ParentAgency',
          parent_agency_id: 100
        )
        child = create_agency(agency_id: 102, agency_type: 'Agency')

        sf_data = base_sf_agency.merge('ParentAgency__c' => 'SF_PARENT')

        allow(pam_logic).to receive(:fetch_or_create_pam_obj)
          .with(BaseAgency, 'SF_PARENT', nil, create_pamobj: true, update_pamobj: false)
          .and_return(parent)
        allow(pam_logic).to receive(:fetch_or_create_pam_obj)
          .with(BaseAgency, 'SF001', 'Test Agency', create_pamobj: true, update_pamobj: true)
          .and_yield(child)
          .and_return(child)

        expect { main_logic.send(:process_sf_agency, sf_data) }.not_to raise_error
        expect(old_grandparent.reload.agency_type).to eq('ParentAgency') # No promotion
      end
    end

    context 'with validation failure scenarios' do
      it 'reverts parent assignment when circular relationship detected' do
        # Create existing parent-child relationship
        existing_parent = create_agency(agency_id: 100, agency_type: 'ParentAgency')
        existing_child = create_agency(agency_id: 101, agency_type: 'Agency', parent_agency_id: 100)

        # Try to make parent a child of its own child (circular)
        sf_data = {
          'Id' => 'SF_PARENT',
          'Name' => 'Parent Agency',
          'ParentAgency__c' => 'SF_CHILD',
          'agency_type' => 'Agency'
        }

        allow(pam_logic).to receive(:fetch_or_create_pam_obj)
          .with(BaseAgency, 'SF_CHILD', nil, create_pamobj: true, update_pamobj: false)
          .and_return(existing_child)
        allow(pam_logic).to receive(:fetch_or_create_pam_obj)
          .with(BaseAgency, 'SF_PARENT', 'Parent Agency', create_pamobj: true, update_pamobj: true)
          .and_yield(existing_parent)
          .and_return(existing_parent)

        expect { main_logic.send(:process_sf_agency, sf_data) }.not_to raise_error
        expect(existing_parent.reload.parent_agency_id).to eq(-1) # Should be reverted
      end

      it 'reverts parent assignment when deep hierarchy detected' do
        # Create 3-level hierarchy
        level1 = create_agency(agency_id: 100, agency_type: 'GrandParentAgency')
        level2 = create_agency(agency_id: 101, agency_type: 'ParentAgency', parent_agency_id: 100)
        level3 = create_agency(agency_id: 102, agency_type: 'Agency', parent_agency_id: 101)
        level4 = create_agency(agency_id: 103, agency_type: 'Agency')

        # Try to create 4th level
        sf_data = {
          'Id' => 'SF_LEVEL4',
          'Name' => 'Level 4 Agency',
          'ParentAgency__c' => 'SF_LEVEL3',
          'agency_type' => 'Agency'
        }

        allow(pam_logic).to receive(:fetch_or_create_pam_obj)
          .with(BaseAgency, 'SF_LEVEL3', nil, create_pamobj: true, update_pamobj: false)
          .and_return(level3)
        allow(pam_logic).to receive(:fetch_or_create_pam_obj)
          .with(BaseAgency, 'SF_LEVEL4', 'Level 4 Agency', create_pamobj: true, update_pamobj: true)
          .and_yield(level4)
          .and_return(level4)

        expect { main_logic.send(:process_sf_agency, sf_data) }.not_to raise_error
        expect(level4.reload.parent_agency_id).to eq(-1) # Should be reverted
      end
    end

    context 'with Salesforce data edge cases' do
      it 'handles empty parent agency ID' do
        agency = create_agency(agency_id: 100, agency_type: 'Agency')
        sf_data = base_sf_agency.merge('ParentAgency__c' => '')

        allow(pam_logic).to receive(:fetch_or_create_pam_obj)
          .with(BaseAgency, '', nil, create_pamobj: true, update_pamobj: false)
          .and_return(nil)
        allow(pam_logic).to receive(:fetch_or_create_pam_obj)
          .with(BaseAgency, 'SF001', 'Test Agency', create_pamobj: true, update_pamobj: true)
          .and_yield(agency)
          .and_return(agency)

        expect { main_logic.send(:process_sf_agency, sf_data) }.not_to raise_error
        expect(agency.parent_agency_id).to eq(-1)
      end

      it 'handles whitespace-only parent agency ID' do
        agency = create_agency(agency_id: 100, agency_type: 'Agency')
        sf_data = base_sf_agency.merge('ParentAgency__c' => '   ')

        allow(pam_logic).to receive(:fetch_or_create_pam_obj)
          .with(BaseAgency, '   ', nil, create_pamobj: true, update_pamobj: false)
          .and_return(nil)
        allow(pam_logic).to receive(:fetch_or_create_pam_obj)
          .with(BaseAgency, 'SF001', 'Test Agency', create_pamobj: true, update_pamobj: true)
          .and_yield(agency)
          .and_return(agency)

        expect { main_logic.send(:process_sf_agency, sf_data) }.not_to raise_error
        expect(agency.parent_agency_id).to eq(-1)
      end

      it 'handles mixed cutoff scenarios in promotion chain' do
        # Old grandparent (past cutoff) with new parent (within cutoff)
        old_grandparent = create_agency(
          agency_id: 100,
          agency_type: 'ParentAgency',
          created_at: 15.days.ago
        )
        new_parent = create_agency(
          agency_id: 101,
          agency_type: 'ParentAgency',
          parent_agency_id: 100,
          created_at: 5.days.ago
        )
        child = create_agency(agency_id: 102, agency_type: 'Agency')

        sf_data = base_sf_agency.merge('ParentAgency__c' => 'SF_PARENT')

        allow(pam_logic).to receive(:fetch_or_create_pam_obj)
          .with(BaseAgency, 'SF_PARENT', nil, create_pamobj: true, update_pamobj: false)
          .and_return(new_parent)
        allow(pam_logic).to receive(:fetch_or_create_pam_obj)
          .with(BaseAgency, 'SF001', 'Test Agency', create_pamobj: true, update_pamobj: true)
          .and_yield(child)
          .and_return(child)

        expect { main_logic.send(:process_sf_agency, sf_data) }.not_to raise_error

        # Old grandparent should NOT be promoted (past cutoff)
        expect(old_grandparent.reload.agency_type).to eq('ParentAgency')
        # Child should still be assigned to parent
        expect(child.parent_agency_id).to eq(new_parent.agency_id)
      end
    end
  end

  private

  def create_agency(attributes = {})
    defaults = {
      agency_name: 'Test Agency',
      agency_type: 'Agency',
      parent_agency_id: -1,
      active: true,
      created_at: Time.current
    }

    BaseAgency.create!(defaults.merge(attributes))
  end
end
