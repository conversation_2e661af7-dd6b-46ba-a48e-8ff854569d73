# frozen_string_literal: true

require 'spec_helper'

RSpec.describe SmsApiClient, type: :model do
  describe '#redirect' do
    it 'builds the request with correct response' do
      rsp = {}.to_json

      params = {
        display_order: true,
        include_attributes: 'sales_system_id_required,not_returning_reason_flag,active',
        include_inactive_data: true
      }

      stub_request(:get, "http://#{ApiHelper::SMS_API_HOST}/api/dropdown_values/OpportunityStatus")
        .with(query: params.merge(access_token: SmsApiClient::PAM_ACCESS_TOKEN))
        .to_return(status: 200, body: rsp, headers: {})

      response = SmsApiClient.redirect(:get, '/dropdown_values/OpportunityStatus', params)
      expect(response.code.to_i).to eq(200)
      expect(response.body).to eq(rsp)
    end
  end
end
