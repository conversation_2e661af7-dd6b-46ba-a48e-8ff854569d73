# frozen_string_literal: true

module StealthUpload
  module Upload
    describe MappedRow do
      include RulesSpecHelper

      context 'the RuleSet has agency, advertiser, and property mappings' do
        let!(:mediahub) { create(:agency, name: 'MediaHub - AGENCY') }
        let!(:usa) { create(:property, name: 'USA', demographic: nil) }
        let!(:fox) { create(:advertiser, name: 'Fox Broadcasting Company') }
        let!(:registration_col) do
          build(:data_import_registration_column,
                total_dollars: 'BY 19-20')
        end

        before do
          @rule1 = create_rule('Business', 'MediaHub', 'agency_id', mediahub.id)
          @rule2 = create_rule('Network', 'USA NETWORK', 'property_id', usa.id)
          @rule3 = create_rule('Client', 'FOX Broadcasting', 'advertiser_id', fox.id)
          @ruleset = build(:data_import_rule_set, rules: [@rule1, @rule2, @rule3], registration_column: nil)
        end

        subject { described_class.new(@headers, @values, @ruleset) }

        context 'with valid headers and values' do
          before do
            @headers = ['Business', 'Client', 'Media Type', 'Media Owner', 'Network', 'Day Part', 'Program', 'BY 19-20']
            @values = ['MediaHub', 'FOX Broadcasting', 'Cable', 'NBC Universal', 'USA NETWORK', 'Cable', nil, '5980000']
          end

          context 'and no registration column mapping' do
            it 'should not be valid' do
              expect(subject).not_to be_valid
              expect(subject.errors.full_messages).to match_array(['Registration column(s) not given'])
            end
          end

          context 'and a registration column mapping is defined' do
            before do
              @ruleset = build(:data_import_rule_set, rules: [@rule1, @rule2, @rule3],
                                                      registration_column: registration_col.as_json.symbolize_keys)
            end

            subject { described_class.new(@headers, @values, @ruleset) }

            it { should be_valid }

            context 'the column which maps to agency is missing from the input' do
              before do
                @headers = ['Client', 'Media Type', 'Media Owner', 'Network', 'Day Part', 'Program', 'BY 19-20']
                @values = ['FOX Broadcasting', 'Cable', 'NBC Universal', 'USA NETWORK', 'Cable', nil, '5980000']
              end

              it 'should be invalid' do
                expect(subject).not_to be_valid
                err_msg = 'Required value(s) missing: Business'
                expect(subject.errors.full_messages).to match_array([err_msg])
              end
            end

            context 'the column which maps to agency is present' do
              before do
                @headers = ['Business', 'Client', 'Media Type', 'Media Owner', 'Network', 'Day Part', 'Program',
                            'BY 19-20']
              end

              context 'but the value is an empty string' do
                before do
                  @values = ['', 'FOX Broadcasting', 'Cable', 'NBC Universal', 'USA NETWORK', 'Cable', nil, '5980000']
                end

                it 'should be invalid' do
                  expect(subject).not_to be_valid
                  err_msg = 'Required value(s) missing: Business'
                  expect(subject.errors.full_messages).to match_array([err_msg])
                end
              end

              context 'but the value is nil' do
                before do
                  @values = [nil, 'FOX Broadcasting', 'Cable', 'NBC Universal', 'USA NETWORK', 'Cable', nil, '5980000']
                end

                it 'should be invalid' do
                  expect(subject).not_to be_valid
                  err_msg = 'Required value(s) missing: Business'
                  expect(subject.errors.full_messages).to match_array([err_msg])
                end
              end

              context 'but nothing is mapped for the given value' do
                before do
                  @values = ['BadAgencyName', 'FOX Broadcasting', 'Cable', 'NBC
                            Universal', 'USA NETWORK', 'Cable', nil, '5980000']
                end

                it 'should be invalid' do
                  expect(subject).not_to be_valid
                  err_msg = 'Required value(s) missing: Business'
                  expect(subject.errors.full_messages).to match_array([err_msg])
                end
              end
            end

            context 'a demographic mapping is defined' do
              let!(:demo) { create(:demographic, name: 'P25-45') }

              before do
                @rule4 = create_rule('Demo', 'P25-45', 'demographic_id', demo.id)
                @ruleset = build(:data_import_rule_set, rules: [@rule1, @rule2, @rule3, @rule4],
                                                        registration_column: registration_col.as_json.symbolize_keys)
              end

              context 'and present in the input' do
                before do
                  @headers = ['Business', 'Client', 'Media Owner', 'Network', 'Program', 'BY 19-20', 'Demo']
                  @values = ['MediaHub', 'FOX Broadcasting', 'NBC Universal', 'USA NETWORK', nil, '5980000', 'P25-45']
                end

                it { should be_valid }

                it '#mapped_h should have the correct demographic_id' do
                  expect(subject.mapped_h[:demographic_id]).to eq(demo.id)
                end
              end
            end
          end
        end
      end
    end
  end
end
