# frozen_string_literal: true

module StealthUpload
  module Upload
    describe MappedModelCell do
      include RulesSpecHelper

      context 'for an advertiser' do
        let!(:mediahub) { create(:agency, name: 'MediaHub - AGENCY') }
        let!(:usa) { create(:property, name: 'USA') }

        before do
          @rule1 = create_rule('Network', 'USA NETWORK', 'property_id', usa.id)
          @ruleset = build(:data_import_rule_set, rules: [@rule1])
          @usa_rule = @ruleset.rules[0]
        end

        it 'should be valid' do
          cell = MappedModelCell.new(header: @usa_rule.maps_from, value: ['USA NETWORK'], rule: @usa_rule)
          expect(cell).to be_valid
        end

        describe 'given an empty or nil value' do
          it 'should be invalid' do
            cell = MappedModelCell.new(header: @usa_rule.maps_from, value: [''], rule: @usa_rule)
            expect(cell).not_to be_valid
            expect(cell.errors.full_messages).to match_array(['A value is required for Network'])
          end
        end

        describe 'given an invalid value' do
          it 'should be invalid' do
            cell = MappedModelCell.new(header: @usa_rule.maps_from, value: ['ASDF'], rule: @usa_rule)
            expect(cell).not_to be_valid
            expect(cell.errors.full_messages).to match_array(['A value is required for Network'])
          end
        end
      end
    end
  end
end
