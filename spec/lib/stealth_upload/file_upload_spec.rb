# frozen_string_literal: true

require 'rails_helper'

# rubocop:disable Metrics/ModuleLength
module StealthUpload
  describe FileUpload do
    include RulesSpecHelper
    include ActionDispatch::TestProcess::FixtureFile
    let!(:parent_agency_id) { 1 }
    let!(:parent_agency) { create(:agency, id: parent_agency_id) }
    let!(:user) { create(:user) }
    let!(:rule_set) { build(:data_import_rule_set, rules: []) }
    [
      'AGENCY',
      'CLIENT',
      'Property Type',
      'Property',
      'Vertical',
      'actual_prequarter_amount',
      'actual_quarter1_amount',
      'actual_quarter2_amount',
      'actual_quarter3_amount',
      'actual_quarter4_amount',
      'actual_postquarter_amount'
    ].each do |col|
      let!(col.to_sym) do
        build(
          :data_import_source_column,
          source_column_name: col
        )
      end
    end
    let!(:budget_year) { create(:budget_year) }
    let!(:marketplace) { create(:marketplace) }
    let!(:property) { create(:property) }
    let!(:adv_att_cricket) { create(:advertiser, advertiser_name: 'AT&T Cricket Wireless, LLC') }
    let!(:deal) do
      create(
        :deal,
        property:,
        agency: parent_agency,
        advertiser: adv_att_cricket,
        marketplace:
      )
    end

    let!(:budget) do
      create(
        :budget,
        deal:,
        budget_year:,
        actual_prequarter_amount: 0,
        actual_quarter1_amount: 0,
        actual_quarter2_amount: 0,
        actual_quarter3_amount: 0,
        actual_quarter4_amount: 0,
        actual_postquarter_amount: 0
      )
    end

    let!(:file) { fixture_file_upload('agency_data_import_upload.csv', 'text/csv') }
    let!(:csv) { Api::Upload::Parsers::Csv.new(file).parse }
    let!(:params) do
      [
        input: csv,
        parent_agency_id: parent_agency.id,
        marketplace_id: marketplace.id,
        budget_year_id: budget_year.id,
        current_user: user
      ]
    end

    describe '.perform' do
      it 'creates an instance with the params' do
        dbl = double('file_upload')
        allow(described_class).to receive(:new).and_return(dbl)
        allow(dbl).to receive(:perform)
        described_class.new(input: csv,
                            parent_agency_id: parent_agency.id,
                            marketplace_id: marketplace.id,
                            budget_year_id: budget_year.id,
                            current_user: user).perform
        expect(described_class).to have_received(:new).with(*params)
      end

      context 'a rule_set exists for the parent_agency_id' do
        before :each do
          allow(StealthUpload::Mapping::RuleSet).to receive(:initialize).and_return([rule_set])
          allow(rule_set).to receive(:present?).and_return(true)
          allow(rule_set).to receive(:rules).and_return([])
          allow(rule_set)
            .to receive(:apply_rules).and_return(row: 1)
        end

        it 'returns a hash with an errors array and a success_count number' do
          allow(rule_set).to receive(:rules).and_return([])
          allow_any_instance_of(Pubsub::MappingRulesRpcClient).to receive(:call)
          file_upload = described_class.new(input: csv,
                                            parent_agency_id: parent_agency.id,
                                            marketplace_id: marketplace.id,
                                            budget_year_id: budget_year.id,
                                            current_user: user)
          result = file_upload.result
          expect(result[:success_count]).to eq(0)
          expect(result[:errors].count).to_not eq(0)
        end
      end

      context 'new deal enabled for parent_agency_id' do
        let!(:bravo) { create(:property, name: 'Bravo') }
        let!(:carmax) { create(:advertiser, name: 'CarMax') }
        let!(:demo1) { create(:demographic, name: 'F25-54') }
        let!(:demo2) { create(:demographic, name: 'P35+') }
        let!(:rating_stream) { create(:rating_stream, name: 'C3') }
        let!(:product_category) { create(:product_category, id: -1) }
        let(:advert_all) { create(:advertiser, id: -1) }

        before do
          @rule1 = create_rule('Agency', 'Dentsu Aegis', 'agency_id', parent_agency.id)
          @rule2 = create_rule('Property', 'Bravo', 'property_id', bravo.id)
          @rule3 = create_rule('Advertiser', 'Carmax', 'advertiser_id', carmax.id)
          @rule4 = create_rule('Demographic', 'F25-54', 'demographic_id', demo1.id)
          @rule5 = create_rule('Demographic', 'P35+', 'demographic_id', demo2.id)
        end

        context 'and parsed by total registration' do
          let!(:registration_col) { build(:data_import_registration_column, total_dollars: 'Total') }
          let!(:data_import_rule_set) do
            build(:data_import_rule_set, rules: [@rule1, @rule2, @rule3, @rule4, @rule5], create_new_deals: true,
                                         registration_column: registration_col)
          end
          let!(:file) { fixture_file_upload('dentsu_data_import_upload.csv', 'text/csv') }
          let!(:csv) { Api::Upload::Parsers::Csv.new(file).parse }

          it 'creates a new deal with split dollars' do
            allow_any_instance_of(Pubsub::MappingRulesRpcClient).to receive(:call).and_return(data_import_rule_set)
            Deal.delete_all
            expect(Deal.count).to be_zero
            described_class.new(input: csv,
                                parent_agency_id: parent_agency.id,
                                marketplace_id: marketplace.id,
                                budget_year_id: budget_year.id,
                                current_user: user).perform
            expect(Deal.count).to eq(1)
            expect(Deal.first.budgets.count).to eq(1)

            budget = Deal.first.budgets.first

            # Split uploaded total
            expect(budget.actual_prequarter_amount).to be_zero
            expect(budget.actual_quarter1_amount).to eq(25_139)
            expect(budget.actual_quarter2_amount).to eq(25_139)
            expect(budget.actual_quarter3_amount).to eq(25_138)
            expect(budget.actual_quarter4_amount).to eq(25_139)
            expect(budget.actual_postquarter_amount).to be_zero
          end

          it 'only updates deals based on specified budget year' do
            allow_any_instance_of(Pubsub::MappingRulesRpcClient).to receive(:call).and_return(data_import_rule_set)
            Deal.delete_all
            another_budget_year = create(:budget_year)
            existing_deal = create(:deal, property:, agency: parent_agency, advertiser: adv_att_cricket,
                                          marketplace:)
            create(:budget,
                   deal: existing_deal,
                   budget_year: another_budget_year,
                   actual_prequarter_amount: 0,
                   actual_quarter1_amount: 0,
                   actual_quarter2_amount: 0,
                   actual_quarter3_amount: 0,
                   actual_quarter4_amount: 0,
                   actual_postquarter_amount: 0)

            described_class.new(input: csv,
                                parent_agency_id: parent_agency.id,
                                marketplace_id: marketplace.id,
                                budget_year_id: budget_year.id,
                                current_user: user).perform
            existing_deal_budget = existing_deal.budgets.first
            new_deal_budget = Deal.last.budgets.first

            expect(existing_deal_budget.actual_prequarter_amount).to be_zero
            expect(existing_deal_budget.actual_quarter1_amount).to be_zero
            expect(existing_deal_budget.actual_quarter2_amount).to be_zero
            expect(existing_deal_budget.actual_quarter3_amount).to be_zero
            expect(existing_deal_budget.actual_quarter4_amount).to be_zero
            expect(existing_deal_budget.actual_postquarter_amount).to be_zero

            expect(new_deal_budget.actual_prequarter_amount).to be_zero
            expect(new_deal_budget.actual_quarter1_amount).to eq(25_139)
            expect(new_deal_budget.actual_quarter2_amount).to eq(25_139)
            expect(new_deal_budget.actual_quarter3_amount).to eq(25_138)
            expect(new_deal_budget.actual_quarter4_amount).to eq(25_139)
            expect(new_deal_budget.actual_postquarter_amount).to be_zero
          end

          context 'stealth enabled' do
            let!(:amy) do
              create(:agency_marketplace_year, agency: parent_agency, marketplace:,
                                               budget_year:, stealth_enabled: true)
            end

            it 'creates a new deal with a stealth budget' do
              allow_any_instance_of(Pubsub::MappingRulesRpcClient).to receive(:call).and_return(data_import_rule_set)
              Deal.delete_all
              expect(Deal.count).to be_zero
              described_class.new(input: csv,
                                  parent_agency_id: parent_agency.id,
                                  marketplace_id: marketplace.id,
                                  budget_year_id: budget_year.id,
                                  current_user: user).perform
              expect(Deal.count).to eq(1)
              expect(Deal.first.budgets.count).to eq(1)
              expect(Deal.first.budgets.first.stealth_mode_budget).not_to be_nil

              sm_budget = Deal.first.budgets.first.stealth_mode_budget

              # Split uploaded total
              expect(sm_budget.actual_prequarter_amount).to be_zero
              expect(sm_budget.actual_quarter1_amount).to eq(25_139)
              expect(sm_budget.actual_quarter2_amount).to eq(25_139)
              expect(sm_budget.actual_quarter3_amount).to eq(25_138)
              expect(sm_budget.actual_quarter4_amount).to eq(25_139)
              expect(sm_budget.actual_postquarter_amount).to be_zero
            end
          end
        end

        context 'and parsed by quarter registration' do
          let!(:registration_col) do
            build(:data_import_registration_column, total_dollars: '',
                                                    actual_prequarter_amount: 'PREQ',
                                                    actual_quarter1_amount: 'Q1',
                                                    actual_quarter2_amount: 'Q2',
                                                    actual_quarter3_amount: 'Q3',
                                                    actual_quarter4_amount: 'Q4',
                                                    actual_postquarter_amount: 'POSTQ')
          end
          let!(:data_import_rule_set) do
            build(:data_import_rule_set, rules: [@rule1, @rule2, @rule3, @rule4, @rule5], create_new_deals: true,
                                         registration_column: registration_col)
          end
          let!(:file) { fixture_file_upload('dentsu_data_import_upload_by_quarter.csv', 'text/csv') }
          let!(:csv) { Api::Upload::Parsers::Csv.new(file).parse }

          it 'creates a new deal with uploaded quarters' do
            allow_any_instance_of(Pubsub::MappingRulesRpcClient).to receive(:call).and_return(data_import_rule_set)
            Deal.delete_all
            expect(Deal.count).to be_zero
            described_class.new(input: csv,
                                parent_agency_id: parent_agency.id,
                                marketplace_id: marketplace.id,
                                budget_year_id: budget_year.id,
                                current_user: user).perform
            expect(Deal.count).to eq(1)
            expect(Deal.first.budgets.count).to eq(1)

            budget = Deal.first.budgets.first

            # Uploaded quarter totals
            expect(budget.actual_prequarter_amount).to eq(100_555)
            expect(budget.actual_quarter1_amount).to eq(49_102)
            expect(budget.actual_quarter2_amount).to eq(10_392)
            expect(budget.actual_quarter3_amount).to eq(8_583)
            expect(budget.actual_quarter4_amount).to eq(19_392)
            expect(budget.actual_postquarter_amount).to eq(58_193)
          end

          context 'stealth enabled' do
            let!(:amy) do
              create(:agency_marketplace_year, agency: parent_agency, marketplace:,
                                               budget_year:, stealth_enabled: true)
            end

            it 'creates a new deal with a stealth budget' do
              allow_any_instance_of(Pubsub::MappingRulesRpcClient).to receive(:call).and_return(data_import_rule_set)
              Deal.delete_all
              expect(Deal.count).to be_zero
              described_class.new(input: csv,
                                  parent_agency_id: parent_agency.id,
                                  marketplace_id: marketplace.id,
                                  budget_year_id: budget_year.id,
                                  current_user: user).perform
              expect(Deal.count).to eq(1)
              expect(Deal.first.budgets.count).to eq(1)
              expect(Deal.first.budgets.first.stealth_mode_budget).not_to be_nil

              sm_budget = Deal.first.budgets.first.stealth_mode_budget

              # Uploaded quarter totals
              expect(sm_budget.actual_prequarter_amount).to eq(100_555)
              expect(sm_budget.actual_quarter1_amount).to eq(49_102)
              expect(sm_budget.actual_quarter2_amount).to eq(10_392)
              expect(sm_budget.actual_quarter3_amount).to eq(8_583)
              expect(sm_budget.actual_quarter4_amount).to eq(19_392)
              expect(sm_budget.actual_postquarter_amount).to eq(58_193)
            end
          end
        end
      end

      context 'no ruleset exists for the parent_agency_id' do
        it 'returns a hash with an errors array and a success_count number' do
          allow_any_instance_of(Pubsub::MappingRulesRpcClient).to receive(:call)
          upload = described_class.new(input: csv,
                                       parent_agency_id: parent_agency.id,
                                       marketplace_id: marketplace.id,
                                       budget_year_id: budget_year.id,
                                       current_user: user)
          expect(upload.valid?).to be(false)
          expect(upload.errors.full_messages[0]).to eq("Ruleset can't be blank")
        end
      end

      context 'direct mapping' do
        context 'and parsed by total registration' do
          let!(:registration_col) { build(:data_import_registration_column, total_dollars: 'Total') }
          let!(:direct_mapping_col) do
            build(:data_import_source_column, source_column_name: 'PamID', direct_mapping: true)
          end
          let!(:data_import_rule_set) do
            build(:data_import_rule_set, source_columns: [direct_mapping_col.as_json.symbolize_keys],
                                         registration_column: registration_col,
                                         rules: [])
          end
          let!(:file) { fixture_file_upload('dentsu_data_import_upload_direct_mapping.csv', 'text/csv') }
          let!(:csv) { Api::Upload::Parsers::Csv.new(file).parse }

          it 'directly updates deals based on specified budget year' do
            allow_any_instance_of(Pubsub::MappingRulesRpcClient).to receive(:call).and_return(data_import_rule_set)
            Deal.delete_all
            existing_deal = create(:deal, id: 198_986, property:, agency: parent_agency,
                                          advertiser: adv_att_cricket, marketplace:)
            create(:budget,
                   deal: existing_deal,
                   budget_year:,
                   actual_prequarter_amount: 0,
                   actual_quarter1_amount: 0,
                   actual_quarter2_amount: 0,
                   actual_quarter3_amount: 0,
                   actual_quarter4_amount: 0,
                   actual_postquarter_amount: 0)

            described_class.new(input: csv,
                                parent_agency_id: parent_agency.id,
                                marketplace_id: marketplace.id,
                                budget_year_id: budget_year.id,
                                current_user: user).perform
            existing_deal_budget = existing_deal.budgets.first

            expect(existing_deal_budget.actual_prequarter_amount).to be_zero
            expect(existing_deal_budget.actual_quarter1_amount).to eq(25_139)
            expect(existing_deal_budget.actual_quarter2_amount).to eq(25_139)
            expect(existing_deal_budget.actual_quarter3_amount).to eq(25_138)
            expect(existing_deal_budget.actual_quarter4_amount).to eq(25_139)
            expect(existing_deal_budget.actual_postquarter_amount).to be_zero
          end
        end
      end
    end

    describe 'validation' do
      let!(:bravo) { create(:property, name: 'Bravo') }
      let!(:carmax) { create(:advertiser, name: 'CarMax') }
      let!(:demo1) { create(:demographic, name: 'F25-54') }
      let!(:demo2) { create(:demographic, name: 'P35+') }
      let!(:integration_col) { build(:data_import_source_column, source_column_name: 'ID', integration: true) }
      let!(:registration_col) { build(:data_import_registration_column, total_dollars: 'Total') }

      before do
        rule1 = create_rule('Agency', 'Dentsu Aegis', 'agency_id', parent_agency.id)
        rule2 = create_rule('Property', 'Bravo', 'property_id', bravo.id)
        rule3 = create_rule('Advertiser', 'Carmax', 'advertiser_id', carmax.id)
        rule4 = create_rule('Demographic', 'F25-54', 'demographic_id', demo1.id)
        rule5 = create_rule('Demographic', 'P35+', 'demographic_id', demo2.id)
        @ruleset = build(:data_import_rule_set, rules: [rule1, rule2, rule3, rule4, rule5],
                                                registration_column: registration_col)
      end

      subject do
        described_class.new(input: csv,
                            parent_agency_id: parent_agency.id,
                            marketplace_id: marketplace.id,
                            budget_year_id: budget_year.id,
                            current_user: user)
      end

      context 'a unique identifier is given but the deal does not exist' do
        context 'and the agency is not given' do
          let!(:file) { fixture_file_upload('dentsu_data_import_upload_no_agency.csv', 'text/csv') }
          let!(:csv) { Api::Upload::Parsers::Csv.new(file).parse }
          it 'should be invalid' do
            allow_any_instance_of(Pubsub::MappingRulesRpcClient).to receive(:call)
            expect(subject).not_to be_valid
          end
        end
        context 'and all required fields are given' do
          let!(:file) { fixture_file_upload('dentsu_data_import_upload.csv', 'text/csv') }
          let!(:csv) { Api::Upload::Parsers::Csv.new(file).parse }
          it 'should be valid' do
            allow_any_instance_of(Pubsub::MappingRulesRpcClient).to receive(:call).and_return(@ruleset)
            expect(subject).to be_valid
          end
        end
        context 'and no data is uploaded' do
          let!(:file) { fixture_file_upload('dentsu_data_import_upload_no_data.csv', 'text/csv') }
          let!(:csv) { Api::Upload::Parsers::Csv.new(file).parse }

          it 'should raise an empty upload error' do
            allow_any_instance_of(Pubsub::MappingRulesRpcClient).to receive(:call).and_return(@ruleset)
            expect { subject.valid? }.to raise_error(StealthUpload::Error::EmptyUploadError)
          end
        end
      end
    end
  end
end
# rubocop:enable Metrics/ModuleLength
