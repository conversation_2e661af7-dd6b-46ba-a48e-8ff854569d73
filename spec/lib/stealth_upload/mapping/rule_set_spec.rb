# frozen_string_literal: true

require 'spec_helper'

module StealthUpload
  module Mapping
    describe RuleSet, type: :model do
      describe 'a new instance' do
        subject { build(:data_import_rule_set, rules: []) }

        it 'should call all instance methods without error' do
          expect { subject.headers }.not_to raise_error
          expect { subject.registration_column }.not_to raise_error
          expect { subject.integration_columns }.not_to raise_error
          expect { subject.non_integration_columns }.not_to raise_error
          expect { subject.has_integration_column? }.not_to raise_error
          expect { subject.has_registration_column? }.not_to raise_error
        end
      end

      describe '#apply_rules' do
        let!(:rule_set) { build(:data_import_rule_set, rules: []) }
        let!(:record) { { CLIENT: 'Hot Dog' } }

        context 'rule_set is empty (has no rules)' do
          it 'raises a RuleSetMissingRuleError' do
            expect { rule_set.apply_rules(record) }
              .to raise_error(StealthUpload::Error::RuleSetMissingRuleError)
          end
        end

        context 'rule_set has at least one rule' do
          let!(:rule) { instance_double('DataImport::Rule') }

          before :each do
            allow(rule_set).to receive(:rules).and_return([rule])
            allow(rule).to receive(:apply).and_return(record)
          end

          it 'calls rule.apply with the record' do
            expect(rule).to receive(:apply).with(record)
            rule_set.apply_rules(record)
          end
        end

        context 'rule_set has multiple rules' do
          let!(:rule) { instance_double('DataImport::Rule') }
          let!(:other_rule) { instance_double('DataImport::Rule') }

          before :each do
            allow(rule_set).to receive(:rules).and_return([rule, other_rule])
            allow(rule).to receive(:apply).and_return(record)
            allow(other_rule).to receive(:apply).and_return(record)
          end

          it 'calls rule.apply with the record for all rules' do
            expect(rule).to receive(:apply).with(record)
            expect(other_rule).to receive(:apply).with(record)
            rule_set.apply_rules(record)
          end
        end
      end
    end
  end
end
