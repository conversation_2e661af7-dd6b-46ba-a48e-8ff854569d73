# frozen_string_literal: true

require 'spec_helper'

module StealthUpload
  module Mapping
    describe Rule do
      shared_context 'rule_global' do
        let!(:rule) { build(:data_import_rule, actions_attributes: [], conditions_attributes: []) }
        let!(:record) do
          {
            'CLIENT' => 'Hot Dog?',
            'Agency Property' => 'NBC'
          }
        end
      end

      shared_context 'single_condition' do
        include_context 'rule_global'

        let!(:source_column) do
          build(
            :data_import_source_column,
            source_column_name: 'CLIENT'
          )
        end

        let!(:condition) do
          build(
            :data_import_condition,
            condition_index: 0,
            source_column_name: source_column.source_column_name,
            source_column_value: 'Hot Dog?',
            rule_id: 1
          )
        end
      end

      shared_context 'single_condition_and_action' do
        include_context 'single_condition'

        let!(:action) do
          build(
            :data_import_action,
            target_column_name: 'advertiser_name',
            sms_target_column_value: 'HOT DOG!'
          )
        end
      end

      shared_context 'single_condition_multiple_actions' do
        include_context 'single_condition_and_action'

        let!(:other_action) do
          build(
            :data_import_action,
            target_column_name: 'property_name',
            sms_target_column_value: 'Nightly News'
          )
        end
      end

      shared_context 'multiple_conditions_multiple_actions' do
        include_context 'single_condition_multiple_actions'

        let!(:source_column_ap) do
          build(
            :data_import_source_column,
            source_column_name: 'Agency Property'
          )
        end

        let!(:other_condition) do
          build(
            :data_import_condition,
            condition_index: 1,
            source_column_name: source_column_ap.source_column_name,
            source_column_value: 'NBC',
            rule_id: 1
          )
        end
      end

      describe '#apply' do
        context 'rule has no conditions' do
          include_context 'rule_global'

          it 'raises a RuleMissingConditionError' do
            expect { rule.apply(record) }
              .to raise_error(StealthUpload::Error::RuleMissingConditionError)
          end
        end

        context 'rule has no actions' do
          include_context 'single_condition'
          let!(:rule2) { build(:data_import_rule, actions_attributes: [], conditions_attributes: [condition.as_json]) }

          it 'raises a RuleMissingActionError' do
            expect { rule2.apply(record) }
              .to raise_error(StealthUpload::Error::RuleMissingActionError)
          end
        end

        context 'rule has actions and conditions' do
          context 'rule has a single condition and action' do
            include_context 'single_condition_and_action'
            let!(:rule2) do
              build(:data_import_rule, actions_attributes: [action.as_json.symbolize_keys],
                                       conditions_attributes: [condition.as_json.symbolize_keys])
            end

            context 'condition is met' do
              it 'applies the action and returns the result as a hash' do
                result = rule2.apply(record)
                actual_value = result[action.target_column_name.to_sym]
                expected_value = action.sms_target_column_value
                expect(actual_value).to eq(expected_value)
              end
            end

            context 'the condition is not met' do
              it 'returns the unaltered record' do
                record['CLIENT'] = 'Not Hot Dog'
                expect(rule2.apply(record)).to eq(record)
              end
            end
          end

          context 'rule has a single condition with multiple actions' do
            include_context 'single_condition_multiple_actions'
            let!(:rule2) do
              build(:data_import_rule, actions_attributes: [action.as_json.symbolize_keys,
                                                            other_action.as_json.symbolize_keys],
                                       conditions_attributes: [condition.as_json.symbolize_keys])
            end

            context 'condition is met' do
              it 'applies all actions and returns the result as a hash' do
                result = rule2.apply(record)
                actual_value = [
                  result[action.target_column_name.to_sym],
                  result[other_action.target_column_name.to_sym]
                ]
                expected_value = [
                  action.sms_target_column_value,
                  other_action.sms_target_column_value
                ]

                expect(actual_value).to match_array(expected_value)
              end
            end

            context 'the condition is not met' do
              it 'returns the unaltered record' do
                record['CLIENT'] = 'Not Hot Dog'
                expect(rule2.apply(record)).to eq(record)
              end
            end
          end

          context 'rule has a multiple conditions with multiple actions' do
            include_context 'multiple_conditions_multiple_actions'
            let!(:rule2) do
              build(:data_import_rule,
                    actions_attributes: [action.as_json.symbolize_keys, other_action.as_json.symbolize_keys],
                    conditions_attributes: [condition.as_json.symbolize_keys, other_condition.as_json.symbolize_keys])
            end

            context 'conditions are met' do
              it 'applies all actions and returns the result as a hash' do
                result = rule2.apply(record)
                actual_value = [
                  result[action.target_column_name.to_sym],
                  result[other_action.target_column_name.to_sym]
                ]
                expected_value = [
                  action.sms_target_column_value,
                  other_action.sms_target_column_value
                ]

                expect(actual_value).to match_array(expected_value)
              end
            end

            context 'one of the conditions is not met' do
              it 'returns the unaltered record' do
                record['CLIENT'] = 'Not Hot Dog'
                expect(rule2.apply(record)).to eq(record)
              end
            end
          end
        end
      end
    end
  end
end
