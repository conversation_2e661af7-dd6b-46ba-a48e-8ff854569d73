# frozen_string_literal: true

require 'spec_helper'

module StealthUpload
  module Mapping
    describe Condition do
      describe '.met_for?' do
        let!(:condition) do
          build(
            :data_import_condition,
            source_column_name: 'CLIENT',
            source_column_value: 'Hot Dog'
          )
        end

        let!(:record) do
          {
            'CLIENT' => 'Hot Dog'
          }
        end

        it 'returns true if the condition is met for the record provided' do
          expect(condition.met_for?(record)).to be_truthy
        end

        it 'returns false if the condition is not met for the record provided' do
          record[condition.source_column_name] = 'Not Hot Dog'
          expect(condition.met_for?(record)).to be_falsy
        end

        it 'is case-insensitive' do
          record[condition.source_column_name] = 'hot dog'
          expect(condition.met_for?(record)).to be_truthy
        end

        it 'strips leading and trailing whitespace when comparing' do
          record[condition.source_column_name] = ' Hot Dog'
          condition.source_column_value = 'Hot Dog '
          expect(condition.met_for?(record)).to be_truthy
          expect(condition.met_for?(record)).to be_truthy
        end
      end
    end
  end
end
