# frozen_string_literal: true

require 'spec_helper'

module StealthUpload
  module Mapping
    describe Action do
      describe '#apply' do
        let!(:action) do
          build(
            :data_import_action,
            target_column_name: 'vertical_id',
            sms_target_column_value: 10_001
          )
        end

        let!(:record) do
          {
            parent_agency_id: 10_816,
            parent_agency_name: 'O<PERSON>',
            vertical_id: -1
          }
        end

        it 'applies the sms_target_column_value to the targeted column field of the supplied record' do
          actual_value = action.apply(record)[action.target_column_name.to_sym]
          expected_value = action.sms_target_column_value
          expect(actual_value).to eq(expected_value)
        end

        it 'does not alter fields other than the target column' do
          actual_record = action.apply(record)
          actual_value = [
            actual_record[:parent_agency_name],
            actual_record[:parent_agency_id]
          ]
          expected_value = [record[:parent_agency_name], record[:parent_agency_id]]
          expect(actual_value).to match_array(expected_value)
        end
      end
    end
  end
end
