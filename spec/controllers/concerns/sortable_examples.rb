# frozen_string_literal: true

require 'spec_helper'

RSpec.shared_examples_for 'a Sortable' do
  describe '#sort_collection' do
    let!(:double_a1) { double('double a1', attr_1: 'a', attr_2: 1) }
    let!(:double_a2) { double('double a2', attr_1: 'a', attr_2: 2) }
    let!(:double_a3) { double('double a3', attr_1: 'a', attr_2: 3) }
    let!(:double_a4) { double('double a4', attr_1: 'a', attr_2: 4) }

    let!(:double_b8) { double('double b8', attr_1: 'b', attr_2: 8) }
    let!(:double_b9) { double('double b9', attr_1: 'b', attr_2: 9) }
    let!(:double_b10) { double('double b10', attr_1: 'b', attr_2: 10) }

    let!(:double_c6) { double('double c6', attr_1: 'c', attr_2: 6) }
    let!(:double_d2) { double('double d2', attr_1: 'd', attr_2: 2) }
    let!(:double_e4) { double('double e4', attr_1: 'e', attr_2: 4) }

    context 'one param' do
      let!(:collection) do
        [
          double_a1,
          double_a4,
          double_a2,
          double_a3
        ]
      end

      it 'sorts on it' do
        allow_any_instance_of(described_class).to receive(:sort_columns).and_return(['attr_2'])
        expect(controller.sort_collection(collection)).to eq(
          [
            double_a1,
            double_a2,
            double_a3,
            double_a4
          ]
        )
      end
    end

    context 'multiple params' do
      let!(:collection) do
        [
          double_b10,
          double_b8,
          double_e4,
          double_d2,
          double_a1
        ]
      end

      it 'sorts on each' do
        allow_any_instance_of(described_class).to receive(:sort_columns).and_return(%w[attr_1 attr_2])
        expect(controller.sort_collection(collection)).to eq(
          [
            double_a1,
            double_b8,
            double_b10,
            double_d2,
            double_e4
          ]
        )
      end
    end

    context 'reverse params' do
      let!(:collection) do
        [
          double_b10,
          double_b8,
          double_e4,
          double_d2,
          double_a1,
          double_b9
        ]
      end

      it 'sorts in reverse' do
        allow_any_instance_of(described_class).to receive(:sort_columns).and_return(['attr_1', '-attr_2'])
        controller.sort_collection(collection)
        expect(controller.sort_collection(collection)).to eq(
          [
            double_a1,
            double_b10,
            double_b9,
            double_b8,
            double_d2,
            double_e4
          ]
        )
      end
    end

    context 'invalid param' do
      let!(:collection) do
        [
          double_b10,
          double_b8,
          double_e4,
          double_d2,
          double_a1,
          double_b9
        ]
      end

      it 'skips that param' do
        allow_any_instance_of(described_class).to receive(:sort_columns).and_return(%w[attr_0 attr_2])
        controller.sort_collection(collection)

        expect(controller.sort_collection(collection)).to eq(
          [
            double_a1,
            double_d2,
            double_e4,
            double_b8,
            double_b9,
            double_b10
          ]
        )
      end
    end
  end
end
