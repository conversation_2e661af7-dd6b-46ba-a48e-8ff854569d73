# frozen_string_literal: true

require 'rails_helper'

RSpec.shared_examples 'send_email' do
  it 'sends an prompt_zero email notification' do
    expect_any_instance_of(BudgetChangeMailer).to receive(:notify_prompt_zero).once
    patch "/api/budgets/#{budget.id}?status_id=#{status.id}"
  end
end

RSpec.shared_examples 'not_send_email' do
  it 'does not send an email notification' do
    expect_any_instance_of(BudgetChangeMailer).not_to receive(:notify_prompt_zero)
    patch "/api/budgets/#{budget.id}?status_id=#{status.id}"
  end
end

RSpec.describe BudgetChangeNotification, type: :api do
  context 'changed to prompt_zero status' do
    let!(:status) { create(:status, prompt_zero: 1) }
    context 'agency status is locked' do
      let!(:agency_property_lock) { create(:agency_property_lock) }
      let!(:deal) do
        create(:deal,
               agency_id: agency_property_lock.agency_id,
               property_id: agency_property_lock.property_id,
               marketplace_id: agency_property_lock.marketplace_id)
      end

      context 'had actual values' do
        let!(:budget) do
          create(:budget, budget_year_id: agency_property_lock.budget_year_id, deal_id: deal.id,
                          actual_prequarter_amount: 10,
                          actual_quarter1_amount: 10,
                          actual_quarter2_amount: 10,
                          actual_quarter3_amount: 10,
                          actual_quarter4_amount: 10,
                          actual_postquarter_amount: 10,
                          projected_prequarter_amount: 0,
                          projected_quarter1_amount: 0,
                          projected_quarter2_amount: 0,
                          projected_quarter3_amount: 0,
                          projected_quarter4_amount: 0,
                          projected_postquarter_amount: 0)
        end
        include_examples('send_email')
      end

      context 'had projected values' do
        let!(:budget) do
          create(:budget, budget_year_id: agency_property_lock.budget_year_id, deal_id: deal.id,
                          actual_prequarter_amount: 0,
                          actual_quarter1_amount: 0,
                          actual_quarter2_amount: 0,
                          actual_quarter3_amount: 0,
                          actual_quarter4_amount: 0,
                          actual_postquarter_amount: 0,
                          projected_prequarter_amount: 10,
                          projected_quarter1_amount: 10,
                          projected_quarter2_amount: 10,
                          projected_quarter3_amount: 10,
                          projected_quarter4_amount: 10,
                          projected_postquarter_amount: 10)
        end
        include_examples('send_email')
      end

      context 'did not have actual or projected values' do
        let!(:budget) do
          create(:budget, budget_year_id: agency_property_lock.budget_year_id, deal_id: deal.id,
                          actual_prequarter_amount: 0,
                          actual_quarter1_amount: 0,
                          actual_quarter2_amount: 0,
                          actual_quarter3_amount: 0,
                          actual_quarter4_amount: 0,
                          actual_postquarter_amount: 0,
                          projected_prequarter_amount: 0,
                          projected_quarter1_amount: 0,
                          projected_quarter2_amount: 0,
                          projected_quarter3_amount: 0,
                          projected_quarter4_amount: 0,
                          projected_postquarter_amount: 0)
        end
        include_examples('not_send_email')
      end
    end

    context 'agency status is not locked' do
      let!(:agency_property_lock) { create(:agency_property_lock, ask: 0, registration: 0, projection: 0) }
      let!(:deal) do
        create(:deal,
               agency_id: agency_property_lock.agency_id,
               property_id: agency_property_lock.property_id,
               marketplace_id: agency_property_lock.marketplace_id)
      end

      context 'had actual or projected values' do
        let!(:budget) { create(:budget, budget_year_id: agency_property_lock.budget_year_id, deal_id: deal.id) }
        include_examples('not_send_email')
      end

      context 'did not have actual or projected values' do
        let!(:budget) do
          create(:budget, budget_year_id: agency_property_lock.budget_year_id, deal_id: deal.id,
                          actual_prequarter_amount: 0,
                          actual_quarter1_amount: 0,
                          actual_quarter2_amount: 0,
                          actual_quarter3_amount: 0,
                          actual_quarter4_amount: 0,
                          actual_postquarter_amount: 0,
                          projected_prequarter_amount: 0,
                          projected_quarter1_amount: 0,
                          projected_quarter2_amount: 0,
                          projected_quarter3_amount: 0,
                          projected_quarter4_amount: 0,
                          projected_postquarter_amount: 0)
        end
        include_examples('not_send_email')
      end
    end
  end

  context 'changed to non prompt_zero status' do
    let!(:status) { create(:status, prompt_zero: 0) }

    context 'agency status is locked' do
      let!(:agency_property_lock) { create(:agency_property_lock) }
      let!(:deal) do
        create(:deal,
               agency_id: agency_property_lock.agency_id,
               property_id: agency_property_lock.property_id,
               marketplace_id: agency_property_lock.marketplace_id)
      end

      context 'had actual values' do
        let!(:budget) do
          create(:budget, budget_year_id: agency_property_lock.budget_year_id, deal_id: deal.id,
                          actual_prequarter_amount: 10,
                          actual_quarter1_amount: 10,
                          actual_quarter2_amount: 10,
                          actual_quarter3_amount: 10,
                          actual_quarter4_amount: 10,
                          actual_postquarter_amount: 10,
                          projected_prequarter_amount: 0,
                          projected_quarter1_amount: 0,
                          projected_quarter2_amount: 0,
                          projected_quarter3_amount: 0,
                          projected_quarter4_amount: 0,
                          projected_postquarter_amount: 0)
        end
        include_examples('not_send_email')
      end

      context 'had projected values' do
        let!(:budget) do
          create(:budget, budget_year_id: agency_property_lock.budget_year_id, deal_id: deal.id,
                          actual_prequarter_amount: 0,
                          actual_quarter1_amount: 0,
                          actual_quarter2_amount: 0,
                          actual_quarter3_amount: 0,
                          actual_quarter4_amount: 0,
                          actual_postquarter_amount: 0,
                          projected_prequarter_amount: 10,
                          projected_quarter1_amount: 10,
                          projected_quarter2_amount: 10,
                          projected_quarter3_amount: 10,
                          projected_quarter4_amount: 10,
                          projected_postquarter_amount: 10)
        end
        include_examples('not_send_email')
      end

      context 'did not have actual or projected values' do
        let!(:budget) do
          create(:budget, budget_year_id: agency_property_lock.budget_year_id, deal_id: deal.id,
                          actual_prequarter_amount: 0,
                          actual_quarter1_amount: 0,
                          actual_quarter2_amount: 0,
                          actual_quarter3_amount: 0,
                          actual_quarter4_amount: 0,
                          actual_postquarter_amount: 0,
                          projected_prequarter_amount: 0,
                          projected_quarter1_amount: 0,
                          projected_quarter2_amount: 0,
                          projected_quarter3_amount: 0,
                          projected_quarter4_amount: 0,
                          projected_postquarter_amount: 0)
        end
        include_examples('not_send_email')
      end
    end

    context 'agency status is not locked' do
      let!(:agency_property_lock) { create(:agency_property_lock, ask: 0, registration: 0, projection: 0) }
      let!(:deal) do
        create(:deal,
               agency_id: agency_property_lock.agency_id,
               property_id: agency_property_lock.property_id,
               marketplace_id: agency_property_lock.marketplace_id)
      end

      context 'had actual or projected values' do
        let!(:budget) { create(:budget, budget_year_id: agency_property_lock.budget_year_id, deal_id: deal.id) }
        include_examples('not_send_email')
      end

      context 'did not have actual or projected values' do
        let!(:budget) do
          create(:budget, budget_year_id: agency_property_lock.budget_year_id, deal_id: deal.id,
                          actual_prequarter_amount: 0,
                          actual_quarter1_amount: 0,
                          actual_quarter2_amount: 0,
                          actual_quarter3_amount: 0,
                          actual_quarter4_amount: 0,
                          actual_postquarter_amount: 0,
                          projected_prequarter_amount: 0,
                          projected_quarter1_amount: 0,
                          projected_quarter2_amount: 0,
                          projected_quarter3_amount: 0,
                          projected_quarter4_amount: 0,
                          projected_postquarter_amount: 0)
        end
        include_examples('not_send_email')
      end
    end
  end
end
