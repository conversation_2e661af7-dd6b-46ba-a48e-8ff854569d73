# frozen_string_literal: true

require 'spec_helper'
require 'rails_helper'

RSpec.describe ApplicationController, type: :controller do
  controller(Api::BudgetManagementScenariosController) do
    def index
      render json: 'hello'
    end

    def permitted_params
      permitted_params_for(BudgetManagementScenario)
    end

    def unpermitted_params
      unpermitted_params_for(BudgetManagementScenario)
    end
  end

  describe '#cleanse_params' do
    it 'drops blank params' do
      get :index, params: { param1: '', param2: ' ', param3: nil, param4: 'hello' }
      expect(controller.params).to eq(ActionController::Parameters.new(param4: 'hello',
                                                                       controller: 'api/budget_management_scenarios',
                                                                       action: 'index'))
    end

    it 'translates "none" to nil' do
      get :index, params: { param1: '', param2: ' ', param3: nil, param4: 'hello', param5: 'none' }
      expect(controller.params).to eq(ActionController::Parameters.new(param4: 'hello',
                                                                       param5: nil,
                                                                       controller: 'api/budget_management_scenarios',
                                                                       action: 'index'))
    end
  end

  describe '#unpermitted_params_for' do
    it 'removes primary key and timestamps' do
      get :index, params: {}
      expect(controller.unpermitted_params).to match_array(%w[
                                                             budget_management_scenario_id
                                                             created_at
                                                             updated_at
                                                             last_updated_by_id
                                                           ])
    end
  end

  describe '#permitted_params_for' do
    it 'whitelists attributes minus unpermitted' do
      get :index, params: {}
      expect(controller).to receive(:unpermitted_params_for).with(BudgetManagementScenario) { ['id'] }
      expect(BudgetManagementScenario).to receive(:column_names) { %w[id name] }
      expect(controller.permitted_params).to match_array(['name'])
    end
  end
end
