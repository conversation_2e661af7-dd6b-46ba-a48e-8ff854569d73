# frozen_string_literal: true

require 'spec_helper'

RSpec.describe 'properties', type: :api do
  let!(:property) { create(:property, image_file_name: 'my_logo.jpg') }
  let(:user) { create(:user) }

  before do
    allow_any_instance_of(Api::PropertiesController).to receive(:user_by_sso_id).and_return(user)
  end

  context 'publish' do
    context 'unauthorized' do
      it 'returns 401' do
        post '/api/properties/publish'
        expect(last_response.status).to eq(401)
      end
    end

    context 'authorized' do
      before(:each) do
        user.user_roles << build(:user_role, user:, role: build(:role, :admin))
      end

      describe 'post /publish' do
        it 'can publish properties' do
          msg = Property.publishable
          expect(Pubsub::Publisher).to receive(:prop_sync).with(msg)
          post '/api/properties/publish'
          expect(last_response.status).to eq(200)
        end
      end
    end
  end

  context 'image' do
    describe 'get /properties/:id/image' do
      it 'should return the property image' do
        stub_request(:get,
                     'https://adsales-pam-assets-private.s3.amazonaws.com/my_logo.jpg').to_return(body: 'fake_image')

        get "api/properties/#{property.id}/image"
        expect(last_response.status).to eq(200)
        expect(last_response.body).to eq('fake_image')
      end

      it 'should return default if no image found' do
        stub_request(:get, 'https://adsales-pam-assets-private.s3.amazonaws.com/my_logo.jpg')
          .to_raise(Aws::S3::Errors::NoSuchKey.new(nil, nil))
        stub_request(:get, 'https://adsales-pam-assets-private.s3.amazonaws.com/default_logo.jpg')
          .to_return(body: 'fake_default_image', status: 200)

        get "api/properties/#{property.id}/image"
        expect(last_response.status).to eq(200)
        expect(last_response.body).to eq('fake_default_image')
      end
    end
  end

  describe 'fetch_default_image_from(bucket:)' do
    let(:controller) { Api::PropertiesController.new }
    let(:s3) { Aws::S3::Resource.new(region: 'us-east-1') }
    let(:bucket) { s3.bucket(ENV['PAM_ASSETS_S3_BUCKET_NAME']) }

    it 'should return default image' do
      stub_request(:get, 'https://adsales-pam-assets-private.s3.amazonaws.com/default_logo.jpg')
        .to_return(body: 'fake_default_image', status: 200)
      image = controller.send(:fetch_default_image_from, bucket:)

      expect(image).to eq('fake_default_image')
    end

    it 'should error out if there is no default image' do
      stub_request(:get, 'https://adsales-pam-assets-private.s3.amazonaws.com/default_logo.jpg')
        .to_raise(Aws::S3::Errors::NoSuchKey.new(nil, nil))

      expect { controller.send(:fetch_default_image_from, bucket:) }
        .to raise_error(Error::ApiError::BadRequestError,
                        "No image found in bucket with name #{Property.default_image_file_name}")
    end
  end
end
