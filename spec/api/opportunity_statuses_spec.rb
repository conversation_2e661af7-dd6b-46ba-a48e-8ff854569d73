# frozen_string_literal: true

require 'spec_helper'

RSpec.describe 'Api::OpportunityStatuses', type: :api do
  describe 'GET /opportunity_statuses' do
    it 'returns data' do
      opportunity_statuses = [
        {
          active: true
        },
        {
          active: false
        }
      ].to_json

      params = {
        display_order: true,
        include_attributes: 'sales_system_id_required,not_returning_reason_flag,active'
      }

      stub_request(:get, "http://#{ApiHelper::SMS_API_HOST}/api/dropdown_values/OpportunityStatus")
        .with(query: params.merge(access_token: SmsApiClient::PAM_ACCESS_TOKEN))
        .to_return(body: opportunity_statuses)

      get '/api/opportunity_statuses?display_order=true' \
          '&include_attributes=sales_system_id_required,not_returning_reason_flag,active'
      expect(last_response.body).to eq(opportunity_statuses)
    end
  end
end
