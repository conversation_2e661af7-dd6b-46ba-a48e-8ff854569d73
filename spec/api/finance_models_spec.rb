# frozen_string_literal: true

require 'spec_helper'

RSpec.describe 'finance_models', type: :api do
  before do
    5.times { create(:finance_model) }
  end

  describe 'GET' do
    it 'returns success status' do
      get '/api/finance_models'
      expect(last_response.status).to eq(200)
    end

    it 'returns data' do
      get '/api/finance_models'
      json_response = JSON.parse(last_response.body)
      expect(json_response.is_a?(Array)).to eq(true)
      expect(json_response.count).to eq(5)
    end

    it 'filters by name' do
      get "/api/finance_models?name=#{CGI.escape(FinanceModel.first(2).pluck(:finance_model_name).join(','))}"
      json_response = JSON.parse(last_response.body)
      expect(json_response.is_a?(Array)).to eq(true)
      expect(json_response.count).to eq(2)
    end
  end
end
