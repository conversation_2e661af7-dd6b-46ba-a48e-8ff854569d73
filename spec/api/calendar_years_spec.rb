# frozen_string_literal: true

require 'spec_helper'

RSpec.describe 'calendar_years', type: :api do
  before do
    2.times { create(:calendar_year, show_in_finance: false, show_in_pacing: false) }
    create(:calendar_year, show_in_finance: true, show_in_pacing: false)
    create(:calendar_year, show_in_finance: false, show_in_pacing: true)
    create(:calendar_year, default_calendar_year: true, show_in_finance: true, show_in_pacing: true)
  end

  describe 'GET' do
    it 'returns success status' do
      get '/api/calendar_years'
      expect(last_response.status).to eq(200)
    end

    it 'returns data' do
      get '/api/calendar_years'
      json_response = JSON.parse(last_response.body)
      expect(json_response.is_a?(Array)).to eq(true)
      expect(json_response.count).to eq(5)
    end

    it 'applies count param' do
      get '/api/calendar_years?count=3'
      json_response = JSON.parse(last_response.body)
      expect(json_response.count).to eq(3)
    end

    it 'applies show_in_finance param' do
      get '/api/calendar_years?show_in_finance=true'
      json_response = JSON.parse(last_response.body)
      expect(json_response.count).to eq(2)
    end

    it 'applies show_in_pacing param' do
      get '/api/calendar_years?show_in_pacing=true'
      json_response = JSON.parse(last_response.body)
      expect(json_response.count).to eq(2)
    end
  end
end
