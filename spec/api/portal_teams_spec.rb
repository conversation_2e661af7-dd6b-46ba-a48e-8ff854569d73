# frozen_string_literal: true

require 'spec_helper'

RSpec.describe 'PortalTeams', type: :api do
  before do
    create_list(:portal_team, 3, active: true)
    create_list(:portal_team, 3, active: false)
  end

  let!(:role) { create(:role, app_role_name: 'Portal') }
  let(:response_body) { JSON.parse(last_response.body) }

  describe 'get /portal_teams' do
    it 'works' do
      get '/api/portal_teams'
      expect(last_response.status).to eq(200)
    end

    it 'returns json array of PortalTeams' do
      get '/api/portal_teams'
      expect(response_body.is_a?(Array)).to eq(true)
    end

    it 'returns inactive and active portal teams' do
      get '/api/portal_teams'

      expect(PortalTeam.all.count).to eq(6)
      expect(response_body.count).to eq(6)
      expect(response_body.map do |portal_team|
               portal_team['portal_team_id']
             end).to eq(PortalTeam.all.map(&:portal_team_id))
    end

    it 'filters by agency_id, marketplace_id, budget_year_id' do
      portal_team = PortalTeam.last
      get '/api/portal_teams',
          marketplace_id: portal_team.marketplace_id,
          budget_year_id: portal_team.budget_year_id,
          agency_id: portal_team.agency_id

      expect(response_body.count).to eq(1)
      expect(response_body[0]['portal_team_id']).to eq(portal_team.id)
    end

    it 'returns only active portal teams with active users when active/filter_active_users = true' do
      inactive_user = create(:user, active: false)
      create_list(:portal_team, 3, active: true, user: inactive_user)

      get '/api/portal_teams', filter_active_users: true, active: true

      expect(PortalTeam.all.count).to eq(9)
      expect(response_body.count).to eq(3)
    end

    describe 'sorting' do
      let!(:create_agencies) do
        create(:agency, agency_id: 1)
        create(:agency, agency_id: 2)
        create(:agency, agency_id: 4)
      end

      let!(:create_users) do
        create(:user, app_user_id: 2)
        create(:user, app_user_id: 3)
        create(:user, app_user_id: 4)
        create(:user, app_user_id: 6)
        create(:user, app_user_id: 9)
      end

      it 'sorts portal team records based on the passed params' do
        PortalTeam.delete_all

        pt1 = create(:portal_team, agency_id: 1, app_user_id: 9)
        pt2 = create(:portal_team, agency_id: 4, app_user_id: 2)
        pt3 = create(:portal_team, agency_id: 4, app_user_id: 4)
        pt4 = create(:portal_team, agency_id: 2, app_user_id: 6)
        pt5 = create(:portal_team, agency_id: 4, app_user_id: 3)

        get '/api/portal_teams?sort=agency_id,-app_user_id'
        expect(response_body.map { |pt| pt['portal_team_id'] })
          .to eq([pt1, pt4, pt3, pt5, pt2].map(&:portal_team_id))
      end
    end
  end

  describe 'patch/put /portal_teams' do
    it 'works' do
      portal_team = PortalTeam.where(active: true).last

      patch "/api/portal_teams/#{portal_team.id}", active: false
      expect(last_response.status).to eq(200)
      expect(portal_team.reload.active).to be(false)
      expect(response_body['portal_team_id']).to eq(portal_team.id)
    end
  end

  describe 'POST /portal_teams' do
    let!(:agency) { create(:agency) }
    let!(:marketplace) { create(:marketplace) }
    let!(:budget_year) { create(:budget_year) }
    let!(:portal_type) { create(:portal_type) }
    let!(:user) { create(:user) }

    it 'creates new portal team records' do
      PortalTeam.delete_all

      body = {
        agency_id: agency.id,
        marketplace_id: marketplace.id,
        budget_year_id: budget_year.id,
        portal_type_id: portal_type.id,
        app_user_id: user.id,
        agency_gateway_notifications: true,
        unlock_request: false,
        edit_stealth_mode: true
      }

      post '/api/portal_teams', body

      expect(PortalTeam.count).to eq(1)
      first = PortalTeam.first

      expect(first.agency_id).to eq(agency.id)
      expect(first.marketplace_id).to eq(marketplace.id)
      expect(first.budget_year_id).to eq(budget_year.id)
      expect(first.portal_type_id).to eq(portal_type.id)
      expect(first.app_user_id).to eq(user.id)
      expect(first.agency_gateway_notifications).to eq(true)
      expect(first.unlock_request).to eq(false)
      expect(first.edit_stealth_mode).to eq(true)
    end
  end
end
