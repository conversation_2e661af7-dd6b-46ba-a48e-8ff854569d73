# frozen_string_literal: true

require 'spec_helper'
require 'rails_helper'

RSpec.describe 'AdvertiserSummaries', type: :api do
  let(:response_body) { JSON.parse(last_response.body) }
  let!(:prior_year) do
    create(:budget_year, budget_year_name: '2019/2020', fall_year: 2019,
                         prior_budget_year_id: create(:budget_year, budget_year_name: '2018/2019', fall_year: 2018).id)
  end
  let!(:budget_year) do
    create(:budget_year, budget_year_name: '2020/2021', fall_year: 2020, default_budget_year: true,
                         prior_budget_year_id: prior_year.id)
  end
  let!(:advertiser1) do
    create(:advertiser,
           advertiser_name: 'Amazon',
           active: true,
           target_account: true)
  end
  let!(:advertiser2) do
    create(:advertiser,
           advertiser_name: 'Stardust',
           active: true,
           target_account: true)
  end
  let!(:advertiser_summary1) do
    create(:advertiser_summary,
           advertiser: advertiser1,
           budget_year:,
           client_partnership_projection: 100)
  end

  let!(:external_sfdc_system) { create(:external_system, name: 'SFDC') }
  let!(:advertiser_key_type) { create(:external_key_type, external_key_type_id: 1) }
  let!(:system_key_association) do
    create(:system_key_association,
           external_key_type: advertiser_key_type,
           external_system: external_sfdc_system,
           pam_key: advertiser1.id,
           external_system_key: 'sfdcadvertiser',
           system_key_asc_type_id: 2)
  end
  let!(:system_key_association2) do
    create(:system_key_association,
           external_key_type: advertiser_key_type,
           external_system: external_sfdc_system,
           pam_key: advertiser2.id,
           external_system_key: 'sfdcadvertiser2',
           system_key_asc_type_id: 1)
  end

  before do
    create(:marketplace, marketplace_name: 'Upfront')
    create(:marketplace, marketplace_name: 'Calendar')
    create(:marketplace, marketplace_name: 'Scatter')
    create(:business_type, business_type_name: 'Current Contracts')
    create(:business_type, business_type_name: 'Working')
  end

  describe 'POST /api_advertiser_summaries_save' do
    context 'with valid params' do
      it 'returns status 200' do
        post '/api/advertiser_summaries/save',
             advertiser_id: advertiser1.id,
             budget_year_id: budget_year.id,
             client_partnership_projection: 200

        expect(last_response.status).to eq(200)
      end

      it 'saves the client partnership projections' do
        post '/api/advertiser_summaries/save',
             advertiser_id: advertiser1.id,
             budget_year_id: budget_year.id,
             client_partnership_projection: 600

        advertiser_summary = AdvertiserSummary.where(advertiser: advertiser1,
                                                     budget_year:)

        expect(AdvertiserSummary.count).to eq(1)
        expect(advertiser_summary.first.client_partnership_projection).to eq(600)
      end
    end

    context 'with invalid params' do
      it 'responds with status 422' do
        post '/api/advertiser_summaries/save',
             advertiser_id: 123,
             budget_year_id: 123,
             client_partnership_projection: 200

        expect(last_response.status).to eq(422)
      end
    end
  end

  describe 'GET /api_advertiser_summaries' do
    before do
      create(:advertiser_summary, advertiser: advertiser1, budget_year: prior_year, client_partnership_projection: 200)
    end

    it 'works' do
      get '/api/advertiser_summaries'
      expect(last_response.status).to eq(200)
      expect(response_body.count).to eq(2)
    end

    it 'filters on advertiser_id' do
      get '/api/advertiser_summaries', advertiser_id: advertiser1.id
      expect(response_body.count).to eq(1)
      expect(response_body.first['advertiser_id']).to eq(advertiser1.id)
    end

    it 'filters on default_category_id' do
      get '/api/advertiser_summaries', default_category_id: advertiser1.default_category_id
      expect(response_body.count).to eq(1)
      expect(response_body.first['advertiser_id']).to eq(advertiser1.id)

      get '/api/advertiser_summaries', default_category_id: create(:category).id
      expect(JSON.parse(last_response.body).count).to eq(0)
    end

    it 'filters on budget_year_id' do
      get '/api/advertiser_summaries', budget_year_id: prior_year.id
      expect(response_body.find do |adv|
               adv['advertiser_id'] == advertiser1.id
             end ['client_partnership_projection']).to eq(200)
    end

    describe 'filtering on classifications' do
      before do
        ['Client Partnership Category', 'Client Partnership SVP', 'Target Classification'].each do |type_name|
          classification_type = create(:advertiser_classification_type, advertiser_classification_type_name: type_name)
          classification = create(:advertiser_classification, advertiser_classification_type: classification_type)
          instance_variable_set(:"@#{type_name.parameterize.underscore}", classification)
          create(:advertiser_classification_association, advertiser: advertiser1,
                                                         advertiser_classification: classification)

          allow(AdvertiserClassificationType)
            .to receive(type_name.parameterize.underscore)
            .and_return(classification_type)
        end
      end

      it 'filters on client_partnership_category_id' do
        get '/api/advertiser_summaries',
            client_partnership_category_id: @client_partnership_category.id
        expect(response_body.count).to eq(1)
        expect(response_body.first['advertiser_id']).to eq(advertiser1.id)

        get '/api/advertiser_summaries',
            client_partnership_category_id: create(:advertiser_classification,
                                                   advertiser_classification_type: @client_partnership_category
                                                                                    .advertiser_classification_type).id
        expect(JSON.parse(last_response.body).count).to eq(0)
      end

      it 'filters on client_partnership_svp_id' do
        get '/api/advertiser_summaries',
            client_partnership_svp_id: @client_partnership_svp.id
        expect(response_body.count).to eq(1)
        expect(response_body.first['advertiser_id']).to eq(advertiser1.id)

        get '/api/advertiser_summaries',
            client_partnership_svp_id: create(:advertiser_classification,
                                              advertiser_classification_type: @client_partnership_svp
                                                                                .advertiser_classification_type).id
        expect(JSON.parse(last_response.body).count).to eq(0)
      end

      it 'filters on target_classification_id' do
        get '/api/advertiser_summaries',
            target_classification_id: @target_classification.id
        expect(response_body.count).to eq(1)
        expect(response_body.first['advertiser_id']).to eq(advertiser1.id)

        get '/api/advertiser_summaries',
            target_classification_id: create(:advertiser_classification,
                                             advertiser_classification_type: @target_classification
                                                                              .advertiser_classification_type).id
        expect(JSON.parse(last_response.body).count).to eq(0)
      end

      it 'filters on multiple classifications' do
        get '/api/advertiser_summaries',
            client_partnership_category_id: @client_partnership_category.id,
            client_partnership_svp_id: @client_partnership_svp.id,
            target_classification_id: @target_classification.id

        expect(response_body.count).to eq(1)
        expect(response_body.first['advertiser_id']).to eq(advertiser1.id)
      end

      context 'sfdc keys' do
        it 'includes sfdc advertiser id' do
          get '/api/advertiser_summaries'

          expect(response_body.count).to eq(2)
          expect(response_body[0]['sfdc_advertiser_id']).to eq('sfdcadvertiser')
          expect(response_body[1]['sfdc_advertiser_id']).to eq('sfdcadvertiser2')
        end

        it 'only includes outbound/combined mappings' do
          advertiser3 = create(:advertiser,
                               advertiser_name: 'Wendys',
                               active: true,
                               target_account: true)

          create(:system_key_association,
                 external_key_type: advertiser_key_type,
                 external_system: external_sfdc_system,
                 pam_key: advertiser3.id,
                 external_system_key: 'sfdcadvertiser3',
                 system_key_asc_type_id: 0)

          get '/api/advertiser_summaries'

          expect(response_body.count).to eq(3)
          expect(response_body[0]['sfdc_advertiser_id']).to eq('sfdcadvertiser')
          expect(response_body[1]['sfdc_advertiser_id']).to eq('sfdcadvertiser2')
          expect(response_body[2]['sfdc_advertiser_id']).to eq(nil)
        end
      end
    end
  end

  describe 'GET /api/advertiser_summaries/info/:advertiser_id' do
    before do
      ['Client Partnership Category', 'Client Partnership SVP', 'Target Classification']
        .each do |classification_type_name|
          classification =
            create(
              :advertiser_classification,
              advertiser_classification_type: create(:advertiser_classification_type,
                                                     advertiser_classification_type_name: classification_type_name)
            )
          instance_variable_set(:"@#{classification_type_name.parameterize.underscore}", classification)
          create(:advertiser_classification_association, advertiser: advertiser1,
                                                         advertiser_classification: classification)
        end

      py = create(:budget_year)
      create(:budget_year, default_budget_year: true, prior_budget_year_id: py.id)
    end

    it 'works' do
      get "api/advertiser_summaries/info/#{advertiser1.id}"
      expect(last_response.status).to eq(200)

      expected_response = {
        'default_category_id' => advertiser1.default_category_id,
        'default_category_name' => advertiser1.default_category.name,
        'client_partnership_category' => @client_partnership_category.name,
        'client_partnership_svp' => @client_partnership_svp.name,
        'target_classification' => @target_classification.name
      }
      expect(response_body).to eq(expected_response)
    end
  end

  describe 'GET /api_advertiser_summaries/selling_verticals/:advertiser_id' do
    it 'works' do
      get "/api/advertiser_summaries/selling_verticals/#{advertiser1.id}"
      expect(last_response.status).to eq(200)
      expect(JSON.parse(last_response.body).is_a?(Array)).to be true
    end
  end

  describe 'GET /api_advertiser_summaries/property_types/:advertiser_id' do
    it 'works' do
      get "/api/advertiser_summaries/property_types/#{advertiser1.id}"
      expect(last_response.status).to eq(200)
      expect(JSON.parse(last_response.body).is_a?(Array)).to be true
    end
  end

  describe 'GET /api_advertiser_summaries/pillars/:advertiser_id' do
    it 'works' do
      get "/api/advertiser_summaries/pillars/#{advertiser1.id}"
      expect(last_response.status).to eq(200)
      expect(JSON.parse(last_response.body).is_a?(Array)).to be true
    end
  end

  describe 'GET /api_advertiser_summaries/quintiles/:advertiser_id' do
    it 'works' do
      get "/api/advertiser_summaries/quintiles/#{advertiser1.id}"
      expect(last_response.status).to eq(200)
      expect(JSON.parse(last_response.body).is_a?(Array)).to be true
    end
  end

  describe 'GET /api_advertiser_summaries/quintile_chart' do
    it 'works' do
      get '/api/advertiser_summaries/quintile_chart'
      expect(last_response.status).to eq(200)
      expect(JSON.parse(last_response.body).is_a?(Array)).to be true
    end
  end

  describe 'GET /api_advertiser_summaries/marketplaces/:advertiser_id' do
    it 'works' do
      get "/api/advertiser_summaries/marketplaces/#{advertiser1.id}"
      expect(last_response.status).to eq(200)
      expect(JSON.parse(last_response.body).is_a?(Array)).to be true
    end
  end
end
