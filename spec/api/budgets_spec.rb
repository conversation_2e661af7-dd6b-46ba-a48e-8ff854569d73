# frozen_string_literal: true

require 'spec_helper'

RSpec.describe 'Budgets', type: :api do
  let!(:current_strategy) { create(:strategy) }
  let!(:updated_strategy) { create(:strategy) }
  let!(:deal) { create(:deal) }
  let!(:budget) do
    create(:budget, deal_id: deal.id, strategy: current_strategy,
                    budget_year: create(:budget_year, prior_budget_year_id: 321))
  end
  let(:additional_attributes) do
    %w[
      splits sales_systems py_actual_prequarter_amount py_actual_quarter1_amount py_actual_quarter2_amount
      py_actual_quarter3_amount py_actual_quarter4_amount py_actual_postquarter_amount
    ]
  end

  describe 'GET api/budgets/:id' do
    it 'returns budget successfully' do
      get "api/budgets/#{budget.id}"
      expect(last_response.status).to eq(200)
      expect(JSON.parse(last_response.body)['budget_id'].to_i).to eq(budget.id.to_i)
    end

    it 'does not return additional data by default' do
      get "api/budgets/#{budget.id}"
      expect(additional_attributes - JSON.parse(last_response.body).keys).to eq(additional_attributes)
    end

    it 'applies include_additional_data param correctly' do
      prior_budget = create(:budget,
                            deal: budget.deal,
                            budget_year: create(:budget_year, budget_year_id: 321),
                            actual_quarter1_amount: 999)
      get "api/budgets/#{budget.id}?include_additional_data=true"
      response = JSON.parse(last_response.body)

      expect(last_response.status).to eq(200)
      expect(additional_attributes - response.keys).to eq([])
      expect(response['py_actual_quarter1_amount'].to_i).to eq(prior_budget.actual_quarter1_amount)
    end
  end

  describe 'get api/budgets' do
    before do
      5.times { create(:budget, deal_id: deal.id) }
    end

    it 'requires deal_id' do
      post '/api/budgets'
      expect(last_response.status).to eq(400)
    end

    it 'works' do
      get '/api/budgets', deal_id: budget.deal_id
      expect(last_response.status).to eq(200)
    end

    it 'returns json array of budgets' do
      get '/api/budgets', deal_id: budget.deal_id
      json_response = JSON.parse(last_response.body)
      expect(json_response.is_a?(Array)).to eq(true)
      expect(json_response.count).to eq(6)
    end

    it 'does not return additional data by default' do
      get 'api/budgets', deal_id: deal.id
      json_response = JSON.parse(last_response.body)
      json_response.each do |budget|
        expect(additional_attributes - budget.keys).to eq(additional_attributes)
      end
    end

    it 'applies include_additional_data param correctly' do
      get 'api/budgets', deal_id: deal.id, include_additional_data: true
      json_response = JSON.parse(last_response.body)
      json_response.each do |budget|
        expect(additional_attributes - budget.keys).to eq([])
      end
    end
  end

  describe 'PUT api/budgets/:id' do
    let!(:budget1) do
      # total dollars: 1_200_000
      create(:budget,
             budget_id: 12_345,
             actual_prequarter_amount: 200_000,
             actual_quarter1_amount: 200_000,
             actual_quarter2_amount: 200_000,
             actual_quarter3_amount: 200_000,
             actual_quarter4_amount: 200_000,
             actual_postquarter_amount: 200_000,
             rate_card_cents: 2_000_000,
             guaranteed_impressions: 1_000,
             hh_impressions: 1_000,
             p_18_49_impressions: 1_000,
             p_25_54_impressions: 1_000,
             p2_plus_impressions: 1_000,
             p18_plus_impressions: 1_000)
    end

    it 'can update the available fields' do
      expect(budget.strategy).to eq(current_strategy)
      put "/api/budgets/#{budget.budget_id}?strategy_id=#{updated_strategy.strategy_id}"
      rsp_body = JSON.parse(last_response.body)
      updated_budget = Budget.find(budget.budget_id)

      expect(updated_budget.strategy).to eq(updated_strategy)
      expect(last_response.status).to eq(200)
      expect(rsp_body['strategy_id']).to eq(updated_budget.strategy_id)
    end

    it 'sends email notifications from email_queue after update' do
      expect_any_instance_of(Api::BudgetsController).to receive(:send_email_notification).once
      put "/api/budgets/#{budget.budget_id}?strategy_id=#{updated_strategy.strategy_id}"
    end

    it 'does not return additional data by default' do
      patch "api/budgets/#{budget.id}", actual_prequarter_amount: 1_000_000
      expect(additional_attributes - JSON.parse(last_response.body).keys).to eq(additional_attributes)
    end

    it 'applies include_additional_data param correctly' do
      patch "api/budgets/#{budget.id}", actual_prequarter_amount: 1_000_000, include_additional_data: true
      expect(additional_attributes - JSON.parse(last_response.body).keys).to eq([])
    end
  end

  describe 'POST api/budgets' do
    let!(:deal) { create(:deal) }
    let!(:budget_year) { create(:budget_year) }

    it 'creates successfully' do
      post '/api/budgets', deal_id: deal.id, budget_year_id: budget_year.id, actual_prequarter_amount: 999_999
      expect(last_response.status).to eq(201)
      expect(Budget.find_by(deal_id: deal.id,
                            budget_year_id: budget_year.id).actual_prequarter_amount.to_i).to eq(999_999)
    end

    it 'requires deal_id' do
      post '/api/budgets', budget_year_id: create(:budget_year).id, actual_prequarter_amount: 999_999
      expect(last_response.status).to eq(400)
    end

    it 'requires budget_year_id' do
      post '/api/budgets', deal_id: deal.id, actual_prequarter_amount: 999_999
      expect(last_response.status).to eq(400)
    end

    it 'returns additional data by default' do
      post '/api/budgets', deal_id: deal.id, budget_year_id: budget_year.id, actual_prequarter_amount: 999_999
      expect(additional_attributes - JSON.parse(last_response.body).keys).to eq([])
    end
  end

  describe 'PATCH api/budgets/batch_update' do
    it 'works' do
      patch 'api/budgets/batch_update', budgets: [{ budget_id: budget.id, actual_quarter1_amount: 123 }]
      expect(last_response.status).to eq(200)
      expect(Budget.find(budget.id).actual_quarter1_amount).to eq(123)
    end

    it 'fails if budget_id is not provided' do
      patch 'api/budgets/batch_update', budgets: [{ budget_id: nil, actual_quarter1_amount: 456 }]
      expect(last_response.status).to eq(400)
      expect(Budget.find(budget.id).actual_quarter1_amount).not_to eq(456)
    end

    it 'rolls back if one update fails' do
      patch 'api/budgets/batch_update', budgets: [
        { budget_id: budget.id, actual_quarter1_amount: 456 },
        { budget_id: nil, actual_quarter1_amount: 456 }
      ]
      expect(last_response.status).to eq(400)
      expect(Budget.find(budget.id).actual_quarter1_amount).not_to eq(456)
    end
  end
end
