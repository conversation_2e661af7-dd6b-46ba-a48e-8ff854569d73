# frozen_string_literal: true

require 'spec_helper'

RSpec.describe 'Marketplaces', type: :api do
  describe 'GET /api_v2_marketplaces' do
    it 'works' do
      get '/api/marketplaces'
      expect(last_response.status).to eq(200)
    end

    it 'returns only Marketplaces where show_in_pacing is true' do
      marketplaces_in_pacing = create_list(:marketplace, 3, show_in_pacing: true)
      create_list(:marketplace, 3, show_in_pacing: false)

      get '/api/marketplaces'
      response = last_response.body

      expect(Marketplace.all.count).to eq(6)
      expect(JSON.parse(response).count).to eq(3)

      expect(response).to eq(marketplaces_in_pacing.to_json)
    end
  end

  context 'publish' do
    let(:user) { create(:user) }
    let!(:marketplace) { create(:marketplace) }

    before do
      allow_any_instance_of(Api::MarketplacesController).to receive(:user_by_sso_id).and_return(user)
    end

    context 'unauthorized' do
      it 'returns 401' do
        post '/api/marketplaces/publish'
        expect(last_response.status).to eq(401)
      end
    end

    context 'authorized' do
      before(:each) do
        user.user_roles << build(:user_role, user:, role: build(:role, :admin))
      end

      describe 'post /publish' do
        it 'can publish marketplaces' do
          msg = Marketplace.publishable
          expect(Pubsub::Publisher).to receive(:marketplace).with(msg)
          post '/api/marketplaces/publish'
          expect(last_response.status).to eq(200)
        end
      end
    end
  end
end
