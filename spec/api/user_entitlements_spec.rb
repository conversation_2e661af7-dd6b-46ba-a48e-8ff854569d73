# frozen_string_literal: true

require 'spec_helper'

RSpec.describe 'UserEntitlements', type: :api do
  describe 'GET /user_entitlements' do
    it 'returns ok status' do
      get '/api/user_entitlements'
      expect(last_response.status).to eq(200)
    end

    it 'returns data' do
      get '/api/user_entitlements'
      json_response = JSON.parse(last_response.body)
      expect(json_response.is_a?(Array)).to eq(true)
    end
  end
end
