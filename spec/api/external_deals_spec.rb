# frozen_string_literal: true

require 'spec_helper'

RSpec.describe 'ExternalDeals', type: :api do
  let!(:user) { create(:user) }
  let(:quarter) { create(:quarter, calendar_year: create(:calendar_year, default_calendar_year: true)) }
  let(:cc_business_type) { create(:business_type, business_type_name: 'Current Contracts') }
  let(:wk_business_type) { create(:business_type, business_type_name: 'Working') }
  let(:external_deal) { create(:external_deal, sales_system_id: 123) }

  let(:pacing_budget_detail) do
    create(:pacing_budget_detail_ii,
           calendar_year: quarter.calendar_year,
           quarter:,
           property: external_deal.property,
           vertical: external_deal.vertical,
           sales_type: external_deal.sales_type)
  end

  before(:each) do
    create(:external_deal_detail, external_deal:, business_type: cc_business_type, quarter:)
    create(:pacing_budget_sales_association, pacing_budget_detail:,
                                             sales_system_id: external_deal.sales_system_id)
  end

  before do
    allow_any_instance_of(ApplicationController).to receive(:user_by_sso_id).and_return(user)
  end

  describe 'get /api/external_deals' do
    it 'works' do
      get '/api/external_deals'
      expect(last_response.status).to eq(200)
    end

    it 'returns json array of ExternalDeals' do
      get '/api/external_deals'
      response = last_response.body
      expect(JSON.parse(response).is_a?(Array)).to eq(true)
    end

    it 'includes ae split data' do
      create(:ae_split, property_id: external_deal.property_id)
      get '/api/external_deals'
      response = JSON.parse(last_response.body)
      expect(response.first['bae_split']).to eq(30)
      expect(response.first['cae_split']).to eq(30)
    end

    context 'filters' do
      let(:buying_ae) { create(:user) }
      let(:client_ae) { create(:user) }

      it 'returns all the data without any filters' do
        get '/api/external_deals'

        response = JSON.parse(last_response.body)
        expect(response.count).to eq(1)
        expect(response.first['external_deal_id']).to eq(external_deal.id)
      end

      it 'returns the filtered data by the passed in values' do
        get '/api/external_deals', {
          pacing_budget_id: pacing_budget_detail.pacing_budget_id,
          agency_id: pacing_budget_detail.agency_id,
          advertiser_id: pacing_budget_detail.advertiser_id,
          property_id: external_deal.property.id,
          sales_system_id: external_deal.sales_system_id,
          vertical_id: external_deal.vertical.id,
          marketplace_id: external_deal.marketplace.id,
          quarter_id: pacing_budget_detail.quarter.id,
          bae_app_user_id: pacing_budget_detail.bae_app_user_id,
          cae_app_user_id: pacing_budget_detail.cae_app_user_id
        }

        response = JSON.parse(last_response.body)

        expect(response.count).to eq(1)
        expect(response.first['external_deal_id']).to eq(external_deal.id)
      end

      it 'filters on a single value' do
        get '/api/external_deals', {
          agency_id: pacing_budget_detail.agency_id
        }

        response = JSON.parse(last_response.body)

        expect(response.count).to eq(1)
        expect(response.first['external_deal_id']).to eq(external_deal.id)
      end

      it 'filters on a comma separated list' do
        external_deal_2 = create(:external_deal, sales_system_id: 123)
        external_deal_3 = create(:external_deal, sales_system_id: 123)

        create(:external_deal_detail, external_deal: external_deal_2, business_type: cc_business_type, quarter:)
        create(:external_deal_detail, external_deal: external_deal_3, business_type: wk_business_type, quarter:)

        pacing_budget_detail_2 = create(:pacing_budget_detail_ii,
                                        quarter:,
                                        calendar_year: quarter.calendar_year,
                                        property: external_deal_2.property,
                                        vertical: external_deal_2.vertical,
                                        sales_type: external_deal_2.sales_type)
        pacing_budget_detail_3 = create(:pacing_budget_detail_ii,
                                        quarter:,
                                        calendar_year: quarter.calendar_year,
                                        property: external_deal_3.property,
                                        vertical: external_deal_3.vertical,
                                        sales_type: external_deal_3.sales_type)

        create(:pacing_budget_sales_association,
               pacing_budget_detail: pacing_budget_detail_2,
               sales_system_id: external_deal.sales_system_id)
        create(:pacing_budget_sales_association,
               pacing_budget_detail: pacing_budget_detail_3,
               sales_system_id: external_deal.sales_system_id)

        get '/api/external_deals', {
          agency_id: [
            pacing_budget_detail.agency_id,
            pacing_budget_detail_2.agency_id,
            pacing_budget_detail_3.agency_id
          ].join(',')
        }

        response = JSON.parse(last_response.body)

        expect(response.count).to eq(3)
        expect(response.map { |d| d['external_deal_id'] }.sort).to eq(ExternalDeal.pluck(:external_deal_id).sort)
      end

      it 'filters on calendar year' do
        calendar_year = quarter.calendar_year

        get '/api/external_deals', {
          calendar_year_id: calendar_year.id
        }

        response = JSON.parse(last_response.body)

        expect(response.count).to eq(1)
        expect(response.first['external_deal_id']).to eq(external_deal.id)
      end

      it 'does not return data which does not match the passed in values' do
        agency = create(:agency)
        get '/api/external_deals', {
          agency_id: agency.id
        }

        response = JSON.parse(last_response.body)
        expect(response).to eq([])
      end
    end
  end
end
