# frozen_string_literal: true

require 'spec_helper'

RSpec.describe 'user_preferences', type: :api do
  before(:all) do
    @user = create(:user)

    @finance_month_type = create(:user_preference_type, user_preference_type_name: 'Finance Month')
    @finance_year_type = create(:user_preference_type, user_preference_type_name: 'Finance Year')

    %w[finance_month finance_year].zip([111, 222]).each do |type_name, value|
      create(:user_preference, user: @user, user_preference_type: instance_variable_get(:"@#{type_name}_type"),
                               state: value)
    end
  end

  before(:each) do
    allow_any_instance_of(ApplicationController).to receive(:user_by_sso_id).and_return(@user)
  end

  describe 'index' do
    it 'returns success status' do
      get '/api/user_preferences'
      expect(last_response.status).to eq(200)
    end

    it 'returns data' do
      get '/api/user_preferences'
      json_response = JSON.parse(last_response.body)
      expect(json_response.is_a?(Array)).to eq(true)
      expect(json_response.count).to eq(2)
    end

    it 'filters by a single type' do
      get '/api/user_preferences?preference_type=finance_month'
      json_response = JSON.parse(last_response.body)
      expect(json_response.count).to eq(1)
      expect(json_response.first['preference_type']).to eq('FINANCE_MONTH')
    end

    it 'filters by multiple types' do
      get '/api/user_preferences?preference_type=finance_month,finance_year'
      json_response = JSON.parse(last_response.body)
      expect(json_response.count).to eq(2)
      expect(json_response.map { |r| r['preference_type'] }).to eq(%w[FINANCE_MONTH FINANCE_YEAR])
    end
  end

  describe 'update' do
    it 'requires preference_type and state' do
      patch '/api/user_preferences'
      expect(last_response.status).to eq(400)

      patch '/api/user_preferences?preference_type=finance_month'
      expect(last_response.status).to eq(400)

      patch '/api/user_preferences?state=1'
      expect(last_response.status).to eq(400)
    end

    it 'updates existing records' do
      patch '/api/user_preferences?state=1000&preference_type=finance_month'
      expect(UserPreference.preference_state_for(user: @user, type_name: 'Finance Month')).to eq('1000')
    end

    it 'creates new records' do
      UserPreference.find_by(app_user_id: @user.id, user_preference_type_id: @finance_month_type.id).delete
      expect(UserPreference.find_by(app_user_id: @user.id, user_preference_type_id: @finance_month_type.id)).to be(nil)
      patch '/api/user_preferences?state=1000&preference_type=finance_month'
      expect(UserPreference.preference_state_for(user: @user, type_name: 'Finance Month')).to eq('1000')
    end
  end
end
