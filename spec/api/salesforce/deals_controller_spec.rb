# frozen_string_literal: true

require 'spec_helper'

RSpec.describe 'DealsController', type: :api do
  let!(:deal) { create(:deal) }
  let!(:budget_year) { create(:budget_year, prior_budget_year_id: 321) }
  let!(:budget) do
    create(:budget, deal_id: deal.id, budget_year:)
  end

  describe 'GET api/salesforce/deals/:deal_id' do
    it 'returns budget successfully' do
      get "api/salesforce/deals/#{deal.id}?budget_year_id=#{budget_year.budget_year_id}"
      expect(last_response.status).to eq(200)
      expect(JSON.parse(last_response.body)['budget_id'].to_i).to eq(budget.id.to_i)
    end

    it 'returns 404 if budget not exist' do
      get 'api/salesforce/deals/abc'
      expect(last_response.status).to eq(404)
    end
  end
end
