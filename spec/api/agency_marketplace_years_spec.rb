# frozen_string_literal: true

require 'spec_helper'

RSpec.describe 'agency_marketplace_years', type: :api do
  let!(:agency_marketplace_year) { create(:agency_marketplace_year, stealth_enabled: false) }
  let(:response) { JSON.parse(last_response.body) }

  describe '#index' do
    before do
      5.times { create(:agency_marketplace_year) }
    end

    it 'requires agency_id, marketplace_id, budget_year_id' do
      get '/api/agency_marketplace_years'
      expect(last_response.status).to eq(400)
    end

    it 'returns the correct amy' do
      get '/api/agency_marketplace_years',
          agency_id: agency_marketplace_year.agency_id,
          marketplace_id: agency_marketplace_year.marketplace_id,
          budget_year_id: agency_marketplace_year.budget_year_id

      expect(response.count).to eq(1)
      expect(response.first['agency_marketplace_year_id']).to eq(agency_marketplace_year.id)
    end
  end

  describe '#update' do
    it 'updates existing record' do
      patch "/api/agency_marketplace_years/#{agency_marketplace_year.id}", stealth_enabled: true

      expect(agency_marketplace_year.reload.stealth_enabled).to be(true)
    end
  end

  describe '#create' do
    let!(:agency) { create(:agency) }
    let!(:marketplace) { create(:marketplace) }
    let!(:budget_year) { create(:budget_year) }

    it 'creates a new record' do
      post '/api/agency_marketplace_years',
           agency_id: agency.id,
           marketplace_id: marketplace.id,
           budget_year_id: budget_year.id,
           stealth_enabled: true

      expect(
        AgencyMarketplaceYear
          .find_by(agency_id: agency.id, marketplace_id: marketplace.id, budget_year_id: budget_year.id)
          .stealth_enabled
      ).to be(true)
    end
  end
end
