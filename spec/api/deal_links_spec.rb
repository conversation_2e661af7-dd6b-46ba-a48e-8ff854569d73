# frozen_string_literal: true

require 'spec_helper'

RSpec.describe 'deal_links', type: :api do
  let!(:advertiser) { create(:advertiser) }
  let!(:agency) { create(:agency) }
  let!(:demographic) { create(:demographic) }
  let!(:user) { create(:user) }
  let!(:rating_stream) { create(:rating_stream) }
  let!(:marketplace) { create(:marketplace) }
  let!(:sfdc_intg_vertical) { create(:sfdc_intg_vertical) }

  let!(:deal_link_type) { create(:deal_link_type) }
  let!(:deal_link) { create(:deal_link, base_advertiser: advertiser, agency:) }
  let!(:non_asc_deal_link) { create(:deal_link, base_advertiser: advertiser, agency:) }
  let!(:deal) { create(:deal, advertiser:, agency:) }
  let!(:deal_link_asc) { create(:deal_link_association, deal_link:, deal:) }
  let(:response_body) { JSON.parse(last_response.body) }

  describe '#index' do
    before do
      5.times { create(:deal_link) }
    end

    context 'filters by associated deal_id' do
      before do
        get '/api/deal_links', deal_id: deal.deal_id, associated: true
      end

      it 'succeeds' do
        expect(last_response.status).to eq(200)
      end

      it 'returns correct deal_links' do
        expect(response_body.count).to eq(1)
        expect(response_body[0]['deal_link_id']).to eq(deal_link.id)
      end

      it 'defaults associated to true' do
        get '/api/deal_links', deal_id: deal.deal_id
        expect(response_body.count).to eq(1)
        expect(response_body[0]['deal_link_id']).to eq(deal_link.id)
      end

      it 'returns deal_link_asc_ids' do
        expect(response_body[0]['deal_link_asc_id']).to eq(deal_link_asc.id)
      end
    end

    context 'filters by non-associated links' do
      context 'with deal_id' do
        before do
          get '/api/deal_links', deal_id: deal.id, associated: false
        end

        it 'succeeds' do
          expect(last_response.status).to eq(200)
        end

        it 'returns correct deal_links' do
          expect(response_body.count).to eq(1)
          expect(response_body[0]['deal_link_id']).to eq(non_asc_deal_link.id)
        end
      end

      context 'without deal_id' do
        let(:deal_link_type_sf) { create(:deal_link_type, send_to_salesforce: true) }
        let(:deal_link_sf) do
          create(:deal_link, base_advertiser: advertiser, agency:, rating_stream_id: rating_stream.id,
                             marketplace_id: marketplace.id, demographic_id: demographic.id, app_user_id: user.id,
                             cae_app_user_id: user.id, sfdc_intg_vertical_id: sfdc_intg_vertical.id,
                             deal_link_type: deal_link_type_sf)
        end
        let(:deal_sf) do
          create(:deal, advertiser:, agency:, demographic:, rating_stream:,
                        app_user_id: user.id, cae_app_user_id: user.id, sfdc_intg_vertical:)
        end
        let!(:deal_link_asc_sf) { create(:deal_link_association, deal_link: deal_link_sf, deal: deal_sf) }

        context 'without send to salesforce' do
          before do
            get '/api/deal_links', associated: false, advertiser_id: advertiser.id, agency_id: agency.id
          end

          it 'succeeds' do
            expect(last_response.status).to eq(200)
          end

          it 'returns correct deal_links' do
            expect(response_body.count).to eq(2)
            expect(response_body[0]['deal_link_id']).to eq(non_asc_deal_link.id)
            expect(response_body[1]['deal_link_id']).to eq(deal_link.id)
          end
        end

        context 'with send to salesforce' do
          before do
            get '/api/deal_links', associated: false, advertiser_id: advertiser.id, agency_id: agency.id,
                                   rating_stream_id: rating_stream.id, marketplace_id: marketplace.id,
                                   demographic_id: demographic.id, app_user_id: user.id, cae_app_user_id: user.id,
                                   sfdc_intg_vertical_id: sfdc_intg_vertical.id
          end

          it 'succeeds' do
            expect(last_response.status).to eq(200)
          end

          it 'returns correct deal_links' do
            expect(response_body.count).to eq(3)
            expect(response_body[0]['deal_link_id']).to eq(deal_link.id)
            expect(response_body[1]['deal_link_id']).to eq(deal_link_sf.id)
            expect(response_body[2]['deal_link_id']).to eq(non_asc_deal_link.id)
          end
        end
      end
    end
  end

  describe '#create' do
    it 'requires deal_link_name' do
      post '/api/deal_links',
           agency_id: agency.id,
           advertiser_id: advertiser.id,
           deal_link_type_id: deal_link_type.id

      expect(last_response.status).to eq(400)
    end

    it 'requires advertiser_id and agency_id' do
      post '/api/deal_links',
           deal_link_name: 'Original Glazed',
           deal_link_type_id: deal_link_type.id

      expect(last_response.status).to eq(400)
    end

    it 'requires deal_link_type_id' do
      post '/api/deal_links',
           deal_link_name: 'Original Glazed',
           advertiser_id: advertiser.id,
           agency_id: agency.id
      expect(last_response.status).to eq(400)
    end

    it 'creates a new deal_link' do
      expect(DealLink.find_by(advertiser_id: advertiser.id, deal_link_type_id: deal_link_type.id)).to be_nil

      post '/api/deal_links',
           deal_link_name: 'Original Glazed',
           agency_id: agency.id,
           advertiser_id: advertiser.id,
           deal_link_type_id: deal_link_type.id,
           demographic_id: demographic.id,
           app_user_id: user.id,
           cae_app_user_id: user.id,
           rating_stream_id: rating_stream.id,
           marketplace_id: marketplace.id,
           sfdc_intg_vertical_id: sfdc_intg_vertical.id

      expect(last_response.status).to eq(201)
      expect(
        DealLink.find_by(agency_id: agency.id, advertiser_id: advertiser.id, deal_link_type_id: deal_link_type.id)
      ).not_to be_nil
    end
  end

  describe '#update' do
    it 'updates a deal_link' do
      expect(deal_link.deal_link_name).not_to eq('Original Glazed')
      patch "/api/deal_links/#{deal_link.id}", deal_link_name: 'Original Glazed'
      expect(deal_link.reload.deal_link_name).to eq('Original Glazed')
    end
  end

  describe '#batch_create' do
    it 'creates successfully' do
      expect(DealLink.find_by(advertiser_id: advertiser.id, deal_link_type_id: deal_link_type.id)).to be_nil

      expect(deal_link.deal_link_name).not_to eq('Original Glazed')
      post '/api/deal_links/batch_create', deal_links: [{ deal_link_name: 'Original Glazed',
                                                          advertiser_id: advertiser.id,
                                                          deal_link_type_id: deal_link_type.id }]
      expect(last_response.status).to eq(201)
      expect(DealLink.find_by(advertiser_id: advertiser.id, deal_link_type_id: deal_link_type.id)).not_to be_nil
    end
  end

  describe '#batch_update' do
    it 'updates successfully' do
      expect(deal_link.deal_link_name).not_to eq('Original Glazed')
      patch '/api/deal_links/batch_update',
            deal_links: [{ 'id' => deal_link.id, 'deal_link_name' => 'Original Glazed' }]
      expect(deal_link.reload.deal_link_name).to eq('Original Glazed')
    end
  end
end
