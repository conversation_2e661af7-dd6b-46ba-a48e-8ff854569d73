# frozen_string_literal: true

require 'spec_helper'

RSpec.describe 'Api::SalesSystems', type: :api do
  describe 'GET /sales_systems' do
    before do
      2.times { create(:external_deal) }
    end

    it 'works' do
      get '/api/sales_systems'
      expect(last_response.status).to eq(200)
    end

    it 'groups results by sales_system_id & budget_year_id' do
      duplicate = ExternalDeal.last
      create(:external_deal, sales_system_id: duplicate.sales_system_id, budget_year_id: duplicate.budget_year_id)
      get '/api/sales_systems'
      json_response = JSON.parse(last_response.body)
      expect(json_response.count).to eq(2)
    end

    it 'returns json array of sales_systems' do
      get '/api/sales_systems'
      json_response = JSON.parse(last_response.body)
      expect(json_response.count).to eq(2)
    end

    it 'filters correctly' do
      get '/api/sales_systems', advertiser_id: ExternalDeal.last.advertiser_id
      json_response = JSON.parse(last_response.body)
      expect(json_response.count).to eq(1)
    end
  end
end
