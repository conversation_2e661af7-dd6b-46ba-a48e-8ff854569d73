# frozen_string_literal: true

require 'spec_helper'

RSpec.describe 'agency_property_locks', type: :api do
  before do
    default_budget_year = create(:budget_year, default_budget_year: true)
    @agency_property_lock = create(:agency_property_lock, budget_year: default_budget_year)
    5.times { create(:agency_property_lock) }
  end

  it 'works' do
    get '/api/agency_property_locks'
    expect(last_response.status).to eq(200)
    expect(JSON.parse(last_response.body).count).to eq(6)
  end

  it 'filters correctly' do
    get '/api/agency_property_locks',
        agency_id: @agency_property_lock.agency_id,
        property_id: @agency_property_lock.property_id,
        budget_year_id: @agency_property_lock.budget_year_id

    response = JSON.parse(last_response.body)
    expect(response.count).to eq(1)
    expect(response.first['agency_property_lock_id']).to eq(@agency_property_lock.id)
  end
end
