# frozen_string_literal: true

require 'spec_helper'

module PamClient
  shared_examples_for 'comments' do
    let!(:comment_type) { create(:comment_type, comment_type_name: 'Comment Type Donut') }
    let!(:comment) { create(commentary_type, comment_type:) }
    let!(:truth) { '<PERSON><PERSON> > Dunkin Donuts' }

    let!(:commentary_type) { :"#{type}_comment" }
    let!(:other_commentary_type) { :"#{other_type}_comment" }

    let(:response_body) { JSON.parse(last_response.body) }

    before do
      allow_any_instance_of(Api::CommentsController).to receive(:user_by_sso_id).and_return(create(:user))
    end

    describe 'GET api/comments' do
      it 'requires type' do
        get 'api/comments', comment_type_id: comment_type.id
        expect(last_response.status).to eq(400)

        get 'api/comments', comment_type_id: comment_type.id, commentary_type: type
        expect(last_response.status).to eq(200)
      end

      it 'works with or without comment_type_id or comment_type' do
        get 'api/comments', commentary_type: type
        expect(last_response.status).to eq(200)

        get 'api/comments', commentary_type: type, comment_type_id: comment_type.id
        expect(last_response.status).to eq(200)

        get 'api/comments', commentary_type: type, comment_type: comment_type.name.parameterize.underscore
        expect(last_response.status).to eq(200)
      end

      it 'filters by params' do
        5.times { create(commentary_type, comment_type: create(:comment_type), budget_year: create(:budget_year)) }

        get 'api/comments/',
            commentary_type: type,
            comment_type_id: comment_type.id,
            budget_year_id: comment.budget_year_id

        expect(response_body.count).to eq(1)
        expect(response_body.first['commentary_id'].to_i).to eq(comment.id.to_i)
      end

      it 'delegates commentary_type and source_id correctly' do
        create(other_commentary_type, comment_type: comment.comment_type, budget_year: comment.budget_year)

        get 'api/comments/',
            commentary_type: type,
            comment_type_id: comment_type.id,
            budget_year_id: comment.budget_year_id

        expect(response_body.count).to eq(1)
        expect(response_body.first['commentary_id'].to_i).to eq(comment.id.to_i)
        expect(response_body.first['source_id'].to_i).to eq(comment.send(source).id.to_i)
      end
    end

    describe 'PATCH api/comments/:id' do
      it 'updates successfully' do
        expect(comment.message).not_to eq(truth)
        patch "/api/comments/#{comment.id}", message: truth
        expect(comment.reload.message).to eq(truth)
      end

      it 'requires message' do
        patch "/api/comments/#{comment.id}"
        expect(last_response.status).to eq(400)
      end
    end

    describe 'PATCH api/comments/batch_update' do
      it 'updates successfully' do
        expect(comment.message).not_to eq(truth)
        patch '/api/comments/batch_update', comments: [{ 'id' => comment.id, 'message' => truth, 'unread' => 'false' }]
        expect(comment.reload.message).to eq(truth)
        expect(comment.reload.unread).to eq(false)
      end
    end

    describe 'POST api/comments' do
      it 'creates successfully' do
        post '/api/comments',
             commentary_type: type,
             comment_type_id: comment_type.id,
             "#{source}_id": create(source).id,
             budget_year_id: create(:budget_year).id,
             message: truth

        expect(last_response.status).to eq(201)
      end

      it 'requires type' do
        post '/api/comments',
             comment_type_id: comment_type.id,
             "#{source}_id": create(source).id,
             budget_year_id: create(:budget_year).id,
             message: truth

        expect(last_response.status).to eq(400)
      end

      it 'requires message' do
        post '/api/comments',
             commentary_type: type,
             comment_type_id: comment_type.id,
             "#{source}_id": create(source).id,
             budget_year_id: create(:budget_year).id

        expect(last_response.status).to eq(400)
      end

      it 'requires source_id' do
        post '/api/comments',
             commentary_type: type,
             comment_type_id: comment_type.id,
             budget_year_id: create(:budget_year).id,
             message: truth
        expect(last_response.status).to eq(400)
      end

      it 'delegates commentary_type and source_id correctly' do
        post '/api/comments',
             commentary_type: type,
             comment_type_id: comment_type.id,
             "#{source}_id": create(source).id,
             budget_year_id: create(:budget_year).id,
             message: truth

        expect(response_body['source_id'].to_i)
          .to eq(commentary_type.to_s.classify.constantize.last.send(source).id.to_i)
      end
    end

    describe 'DELETE api/comments/:id' do
      it 'deletes successfully' do
        id = comment.id
        delete "api/comments/#{id}"
        expect { commentary_type.to_s.classify.constantize.find(id) }.to raise_exception(ActiveRecord::RecordNotFound)
      end
    end
  end
end

RSpec.describe 'deal_comments', type: :api do
  let!(:type) { :deal }
  let!(:other_type) { :advertiser_summary }
  let!(:source) { :deal }

  it_behaves_like 'comments'

  let!(:commentary_type) { :"#{type}_comment" }
  let!(:comment_type) { create(:comment_type, comment_type_name: 'Comment Type Donut') }
  let!(:truth) { 'Krispy Kreme > Dunkin Donuts' }

  describe 'POST api/comments' do
    before do
      allow_any_instance_of(Api::CommentsController).to receive(:user_by_sso_id).and_return(create(:user))
    end

    it 'requires comment_type or comment_type_id' do
      post '/api/comments',
           commentary_type: type,
           "#{source}_id": create(source).id,
           budget_year_id: create(:budget_year).id,
           message: truth
      expect(last_response.status).to eq(422)

      post '/api/comments',
           commentary_type: type,
           comment_type_id: comment_type.id,
           "#{source}_id": create(source).id,
           budget_year_id: create(:budget_year).id,
           message: truth
      expect(last_response.status).to eq(201)

      post '/api/comments',
           commentary_type: type,
           comment_type: comment_type.name.parameterize.underscore,
           "#{source}_id": create(source).id,
           budget_year_id: create(:budget_year).id,
           message: truth
      expect(last_response.status).to eq(201)
    end

    it 'requires budget_year_id' do
      post '/api/comments',
           commentary_type: type,
           comment_type_id: comment_type.id,
           "#{source}_id": create(source).id,
           message: truth
      expect(last_response.status).to eq(422)
    end
  end
end

RSpec.describe 'advertiser_summary_comments', type: :api do
  let!(:type) { :advertiser_summary }
  let!(:other_type) { :deal }
  let!(:source) { :advertiser }

  it_behaves_like 'comments'
end
