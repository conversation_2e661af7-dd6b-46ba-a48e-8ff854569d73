# frozen_string_literal: true

require 'spec_helper'

RSpec.describe 'Quarters', type: :api do
  describe 'GET /api_v2_quarters' do
    before(:each) do
      # current year
      current_year = create(:calendar_year, calendar_year: 2018)
      allow(CalendarYear).to receive(:current_calendar_year).and_return(current_year)

      # non current year quarters
      create(:calendar_year, :with_quarters, calendar_year: 2000)

      # current year quarters
      create(:quarter, quarter: 1, year: current_year.calendar_year, calendar_year: current_year)
      create(:quarter, quarter: 2, year: current_year.calendar_year, calendar_year: current_year)

      # current year locked quarters
      create(:quarter, quarter: 3, year: current_year.calendar_year, calendar_year: current_year, lock_status: true)
      create(:quarter, quarter: 4, year: current_year.calendar_year, calendar_year: current_year, lock_status: true)
    end

    it 'works' do
      get '/api/quarters'
      expect(last_response.status).to eq(200)
    end

    it 'returns only current year quarters where lock_status is false' do
      expect(Quarter.all.count).to eq(8)

      get '/api/quarters'
      quarters = JSON.parse(last_response.body)

      expect(quarters.count).to eq(2)
      expect(quarters.map { |quarter| quarter['year'] }.uniq).to eq([2018])
      expect(quarters.map { |quarter| quarter['lock_status'] }.uniq).to eq([false])
    end
  end
end
