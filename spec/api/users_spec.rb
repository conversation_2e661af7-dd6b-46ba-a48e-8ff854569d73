# frozen_string_literal: true

require 'spec_helper'

RSpec.describe 'users', type: :api do
  include ActiveJob::TestHelper

  let(:response_body) { JSON.parse(last_response.body) }

  let!(:title) { create(:app_user_title, app_user_title_name: 'test title') }
  let!(:location) { create(:location) }

  describe '#index' do
    let!(:user1) { create(:user, first_name: 'Testuser', last_name: 'one', email: nil, sso_id: 1111) }
    let!(:user2) { create(:user, first_name: 'first', last_name: 'last', email: nil, sso_id: 12_345_678) }
    let!(:user3) { create(:user, first_name: 'first', last_name: 'Userlast', email: nil, sso_id: 2222) }
    let!(:user4) { create(:user, first_name: 'first', last_name: 'last', email: '<EMAIL>', sso_id: 3333) }

    it 'searches users by email name and sso with the provided query' do
      get '/api/users', { query: 'user' }
      expect(JSON.parse(last_response.body).count).to eq(2)
      get '/api/users', { query: 345_678 }
      expect(JSON.parse(last_response.body).count).to eq(1)
      get '/api/users', { query: 'nbcu' }
      expect(JSON.parse(last_response.body).count).to eq(1)
      get '/api/users', { query: 'testuser one' }
      expect(JSON.parse(last_response.body).count).to eq(1)
      get '/api/users', { query: 'oops' }
      expect(JSON.parse(last_response.body).count).to eq(0)
    end

    it 'returns no users when no query provided' do
      get '/api/users'
      expect(JSON.parse(last_response.body).count).to eq(0)
    end
  end

  describe '#show' do
    context 'success' do
      let!(:user) { create(:user) }
      let!(:deapartment) { create(:department, app_user_department_name: 'test department') }

      it 'succeeds with valid user_id' do
        get "/api/users/#{user.app_user_id}"
        expect(last_response.status).to eq(200)
      end

      it 'shows team members' do
        team_members = create_list(
          :user, 2, manager_id: user.app_user_id, app_user_title: title, department: deapartment, location:
        )

        get "/api/users/#{user.app_user_id}"

        expect(last_response.status).to eq(200)
        expect(response_body[0]['team_members'].map { |u| u['app_user_id'] })
          .to eq(team_members.map(&:app_user_id))
      end
    end
  end

  describe '#update' do
    let(:manager) { create(:user) }
    let(:user_to_update) { create(:user, manager_id: manager.id, email: '<EMAIL>') }
    let(:new_manager) { create(:user) }

    it 'can update permitted fields' do
      hash = {
        first_name: 'first',
        last_name: 'last',
        phone_number: '(*************',
        cell_phone_number: '(*************',
        location_id: location.location_id,
        app_user_title_id: title.app_user_title_id
      }

      patch "/api/users/#{user_to_update.app_user_id}", hash

      updated_user = User.find(user_to_update.app_user_id)

      hash.each do |key, value|
        expect(updated_user.send(key)).to eq(value)
      end
    end

    it 'can update the users manager' do
      hash = { manager_id: manager.app_user_id }
      patch "/api/users/#{user_to_update.app_user_id}", hash
      updated_user = User.find(user_to_update.app_user_id)
      expect(updated_user.manager_id).to eq(manager.app_user_id)
    end

    describe '#notify_team_member_change' do
      it 'triggers the team member mailer when manager is updated' do
        hash = { manager_id: new_manager.app_user_id }
        expect do
          perform_enqueued_jobs do
            patch "/api/users/#{user_to_update.app_user_id}", hash
          end
        end.to change { ActionMailer::Base.deliveries.size }.by(1)
      end
    end
  end
end
