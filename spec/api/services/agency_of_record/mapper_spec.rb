# frozen_string_literal: true

require 'spec_helper'

module Api
  module AgencyOfRecordServices
    RSpec.describe 'Mapper', type: :service do
      before do
        create(:system_key_association, external_key_type: agency_key_type, external_system: external_sfdc_system,
                                        pam_key: 111, external_system_key: 'agencyid1')
        create(:system_key_association, external_key_type: advertiser_key_type, external_system: external_sfdc_system,
                                        pam_key: 222, external_system_key: 'advertiserid1')
        create(:system_key_association, external_key_type: property_type_key_type,
                                        external_system: external_sfdc_system,
                                        pam_key: 333, external_system_key: 'propertytypeid1')
        create(:system_key_association, external_key_type: location_key_type, external_system: external_sfdc_system,
                                        pam_key: 444, external_system_key: 'locationid1')
        create(:system_key_association, external_key_type: category_key_type, external_system: external_sfdc_system,
                                        pam_key: 555, external_system_key: 'categoryid1')

        create(:agency, agency_id: 111)
        create(:advertiser, advertiser_id: 222)
        create(:property_type, property_type_id: 333)
        create(:location, location_id: 444)
        create(:default_category, product_category_id: 555)
      end

      let!(:mapper) { AgencyOfRecordServices::Mapper.new(sfdc_params) }

      let!(:external_sfdc_system) { create(:external_system, name: 'SFDC') }

      let!(:advertiser_key_type) { create(:external_key_type, external_key_type_id: 1) }
      let!(:agency_key_type) { create(:external_key_type, external_key_type_id: 2) }
      let!(:category_key_type) { create(:external_key_type, external_key_type_id: 3) }
      let!(:location_key_type) { create(:external_key_type, external_key_type_id: 63) }
      let!(:property_type_key_type) { create(:external_key_type, external_key_type_id: 64) }

      let!(:sfdc_params) do
        # param list containing sfdc ids
        ActionController::Parameters.new(
          sfdc_id: 'sfdcid1',
          agency_name: 'my_agency',
          advertiser_name: 'my_advertiser',
          sfdc_default_category_id: 'categoryid1',
          sfdc_agency_id: 'agencyid1',
          sfdc_advertiser_id: 'advertiserid1',
          sfdc_property_type_id: 'propertytypeid1',
          sfdc_location_id: 'locationid1',
          aor: false
        )
      end

      let!(:pam_params) do
        # pam params hash that sfdc ids should map to
        {
          sfdc_id: 'sfdcid1',
          agency_id: '111',
          aor: false,
          advertiser_id: '222',
          property_type_id: '333',
          location_id: '444'
        }.with_indifferent_access
      end

      it 'maps sfdc_ids to pam_ids' do
        expect(mapper.process).to eq(pam_params)
      end

      context 'when agency is not found' do
        let!(:sfdc_params) do
          ActionController::Parameters.new(
            agency_name: 'my_missing_agency',
            sfdc_agency_id: 'badagencyid'
          )
        end

        let!(:mapper) { AgencyOfRecordServices::Mapper.new(sfdc_params) }

        it 'creates missing agency' do
          expect_any_instance_of(Mapper).to receive(:create_missing_agency).and_call_original
          mapper.process

          expect(Agency.find_by(name: 'my_missing_agency')).not_to be_nil
        end

        it 'creates a system key association for the new agency' do
          mapper.process
          expect(SystemKeyAssociation.find_by(external_system_key: 'badagencyid')).not_to be_nil
        end
      end

      context 'when advertiser is not found' do
        let!(:sfdc_params) do
          ActionController::Parameters.new(
            advertiser_name: 'my_missing_advertiser',
            sfdc_default_category_id: 'categoryid1',
            sfdc_advertiser_id: 'badadvertiserid'
          )
        end

        let!(:mapper) { AgencyOfRecordServices::Mapper.new(sfdc_params) }

        it 'creates missing advertiser' do
          expect_any_instance_of(Mapper).to receive(:create_missing_advertiser).and_call_original
          mapper.process

          expect(Advertiser.find_by(name: 'my_missing_advertiser')).not_to be_nil
        end

        it 'creates a system key association for the new advertiser' do
          mapper.process
          expect(SystemKeyAssociation.find_by(external_system_key: 'badadvertiserid')).not_to be_nil
        end
      end
    end
  end
end
