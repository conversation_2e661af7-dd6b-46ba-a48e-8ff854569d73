# frozen_string_literal: true

require 'spec_helper'

RSpec.describe Api::MoveDealService do
  let!(:origin_parent_deal) { create(:parent_deal) }
  let!(:destination_parent_deal) do
    create(:parent_deal, agency: origin_parent_deal.agency, advertiser: origin_parent_deal.advertiser,
                         marketplace: origin_parent_deal.marketplace)
  end
  let!(:matching_deal1) do
    create(:deal, agency: origin_parent_deal.agency, advertiser: origin_parent_deal.advertiser,
                  marketplace: origin_parent_deal.marketplace, parent_deal: origin_parent_deal)
  end
  let!(:matching_deal2) do
    create(:deal, agency: origin_parent_deal.agency, advertiser: origin_parent_deal.advertiser,
                  marketplace: origin_parent_deal.marketplace, parent_deal: origin_parent_deal)
  end
  let!(:linked_deal) do
    create(:deal, agency: origin_parent_deal.agency, advertiser: origin_parent_deal.advertiser,
                  marketplace: origin_parent_deal.marketplace)
  end
  let!(:non_matching_deal) { create(:deal) }

  describe '#move' do
    context 'matching parent' do
      it 'moves the deal to the new parent' do
        expect(matching_deal1.parent_deal.id).to eq(origin_parent_deal.id)
        expect(matching_deal2.parent_deal.id).to eq(origin_parent_deal.id)

        Api::MoveDealService.new([matching_deal1.id, matching_deal2.id], destination_parent_deal.id).move

        expect(matching_deal1.reload.parent_deal.id).to eq(destination_parent_deal.id)
        expect(matching_deal2.reload.parent_deal.id).to eq(destination_parent_deal.id)
      end
    end

    context 'non matching parent' do
      it 'raises a FailedDependencyError and does not move the deals to the new parent' do
        origin_parent_deal_id = non_matching_deal.parent_deal_id

        expect { Api::MoveDealService.new([non_matching_deal.id], destination_parent_deal.id).move }
          .to raise_error(Error::ApiError::FailedDependencyError)

        expect(non_matching_deal.reload.parent_deal.id).to eq(origin_parent_deal_id)
      end
    end

    context 'linked deals' do
      it 'moves linked deals' do
        deal_link = create(:deal_link)
        create(:deal_link_association, deal: matching_deal1, deal_link:)
        create(:deal_link_association, deal: linked_deal, deal_link:)

        expect(linked_deal.parent_deal.id).not_to eq(origin_parent_deal.id)

        Api::MoveDealService.new([matching_deal1.id, matching_deal2.id], destination_parent_deal.id).move

        expect(linked_deal.reload.parent_deal.id).to eq(destination_parent_deal.id)
      end
    end
  end
end
