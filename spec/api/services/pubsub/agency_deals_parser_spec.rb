# frozen_string_literal: true

require 'spec_helper'

module Pubsub
  RSpec.describe 'AgencyDealsParser', type: :service do
    let!(:no_sponsorship_record) { create(:sponsorship_type, sponsorship_type_name: 'No Sponsorship') }
    let(:deal) { create(:deal) }
    let(:deal_id) { create(:deal).id }
    let(:payload) do
      {
        'parent_agency_id' => deal.agency.parent_agency_id,
        'agency_deals' => [
          'deal_id' => deal_id,
          'prior_budget_id' => 1,
          'spend_dollars' => 2,
          'current_budget_id' => 3,
          'agency_registration_type_id' => 4,
          'actual_prequarter_amount' => 1000,
          'actual_quarter4_amount' => 2000,
          'actual_quarter1_amount' => 3000,
          'actual_quarter2_amount' => 4000,
          'actual_quarter3_amount' => 5000,
          'actual_postquarter_amount' => 6000,
          'comments' => '[ {"created_at": "01-MAY-17 18:51:06",
                            "comment_type_name": "ag_pre_reg",
                            "text": "Test AG Comments 1",
                            "commenter_name": "Sandeep Nunna" } ]'
        ]
      }
    end

    let(:payload_without_reg_type) do
      {
        'parent_agency_id' => deal.agency.parent_agency_id,
        'agency_deals' => [
          'deal_id' => deal_id,
          'prior_budget_id' => 1,
          'spend_dollars' => 2,
          'current_budget_id' => 3,
          'agency_registration_type_id' => nil,
          'actual_prequarter_amount' => 1000,
          'actual_quarter4_amount' => 2000,
          'actual_quarter1_amount' => 3000,
          'actual_quarter2_amount' => 4000,
          'actual_quarter3_amount' => 5000,
          'actual_postquarter_amount' => 6000,
          'comments' => '[ {"created_at": "01-MAY-17 18:51:06",
                            "comment_type_name": "ag_pre_reg",
                            "text": "Test AG Comments 1",
                            "commenter_name": "Sandeep Nunna" } ]'
        ]
      }
    end

    describe '#parse' do
      let(:ag_deal) { AgencyDeal.find_by_deal_id(deal_id) }
      let(:file_header) { create(:file_header) }

      before do
        create(:user, app_user_id: 1234)
        create(:agency_deal_comment, comment_type: CommentType.ag_pre_registration)
      end

      context 'deal does not exist' do
        it 'doesnt fail if the deal does not exist in PAM' do
          payload_without_deal = {
            'parent_agency_id' => deal.agency.parent_agency_id,
            'agency_deals' => [
              'deal_id' => '',
              'prior_budget_id' => 1,
              'spend_dollars' => 2,
              'current_budget_id' => 3,
              'agency_registration_type_id' => nil,
              'actual_prequarter_amount' => 1000,
              'actual_quarter4_amount' => 2000,
              'actual_quarter1_amount' => 3000,
              'actual_quarter2_amount' => 4000,
              'actual_quarter3_amount' => 5000,
              'actual_postquarter_amount' => 6000,
              'comments' => '[ {"created_at": "01-MAY-17 18:51:06",
                                "comment_type_name": "ag_pre_reg",
                                "text": "Test AG Comments 1",
                                "commenter_name": "Sandeep Nunna" } ]'
            ]
          }
          expect { AgencyDealsParser.new(payload_without_deal, file_header).parse }.not_to raise_error
        end
      end

      context 'saves to all writable columns' do
        before do
          AgencyDealsParser.new(payload, file_header).parse
        end

        %w[prior_budget_id
           spend_dollars
           current_budget_id
           agency_registration_type_id].each_with_index do |attribute_name, idx|
          it "saves the #{attribute_name}" do
            expect(ag_deal.send(attribute_name)).to eq(idx + 1)
          end
        end

        it 'does not write the ag_agency_deal registration_type_id to the pam deal' do
          expect(Deal.find(deal_id).registration_type_id).to eq(-1)
        end

        it 'saves the comments' do
          expect(AgencyDealComment.ag_pre_reg.count).to eq(2)
        end

        %w[actual_prequarter_amount actual_quarter4_amount
           actual_quarter1_amount actual_quarter2_amount
           actual_quarter3_amount actual_postquarter_amount].each_with_index do |attribute_name, idx|
          it "saves the #{attribute_name}" do
            expect(ag_deal.send(attribute_name)).to eq((idx + 1) * 1000)
          end
        end
      end

      context 'delete' do
        it 'does not delete registration_type_id type on the pam deal' do
          deal = Deal.find(deal_id)

          deal.update!(registration_type_id: 123)
          expect(Deal.find(deal_id).registration_type_id).to eq(123)
          AgencyDealsParser.new(payload_without_reg_type, file_header).parse
          expect(Deal.find(deal_id).registration_type_id).to eq(123)
        end
      end
    end
  end
end
