# frozen_string_literal: true

require 'spec_helper'

RSpec.describe Api::PortalTeamsService do
  let!(:portal_team) { create(:portal_team, active: true) }
  let!(:role) { create(:role, app_role_name: 'Portal') }
  let!(:user) { portal_team.user }
  let!(:agency_id) { portal_team.agency_id }
  let!(:marketplace_id) { portal_team.marketplace_id }
  let!(:budget_year_id) { portal_team.budget_year_id }

  describe 'assignments and roles' do
    context 'edit_stealth_mode true' do
      it 'creates an assignment with portal_management true' do
        Api::PortalTeamsService.new(portal_team).set_assignment_and_role(true, 'update')

        expect(Assignment.where(app_user_id: user.id, agency_id:).count).to eq(1)
        expect(Assignment.find_by(app_user_id: user.id, agency_id:).portal_management).to eq(true)
      end

      it 'updates an existing assignment with portal_management true' do
        create(:assignment, app_user_id: user.id, agency_id:, portal_management: false)
        expect(Assignment.where(app_user_id: user.id, agency_id:).count).to eq(1)

        Api::PortalTeamsService.new(portal_team).set_assignment_and_role(true, 'update')

        expect(Assignment.where(app_user_id: user.id, agency_id:).count).to eq(1)
        expect(Assignment.find_by(app_user_id: user.id, agency_id:).portal_management).to eq(true)
      end

      it 'adds the user to Portal role' do
        Api::PortalTeamsService.new(portal_team).set_assignment_and_role(true, 'update')

        expect(UserRole.where(app_user_id: user.id, app_role_id: role.id).count).to eq(1)
      end
    end

    context 'edit_stealth_mode false' do
      let!(:assignment) do
        create(:assignment, app_user_id: user.id, agency_id:, portal_management: true)
      end

      it 'sets the assignment to portal_management = false if there are other true values' do
        allow(assignment).to receive(:any_true_values?).and_return(true)

        expect(Assignment.where(app_user_id: user.id, agency_id:).count).to eq(1)
        expect(Assignment.find_by(app_user_id: user.id, agency_id:).portal_management).to eq(true)

        Api::PortalTeamsService.new(portal_team).set_assignment_and_role(false, 'update')

        expect(Assignment.where(app_user_id: user.id, agency_id:).count).to eq(1)
        expect(Assignment.find_by(app_user_id: user.id, agency_id:).portal_management).to eq(false)
      end

      it 'removes the assignment if there are no other true values' do
        allow_any_instance_of(Assignment).to receive(:any_true_values?).and_return(false)

        expect(Assignment.where(app_user_id: user.id, agency_id:).count).to eq(1)
        expect(Assignment.find_by(app_user_id: user.id, agency_id:).portal_management).to eq(true)

        Api::PortalTeamsService.new(portal_team).set_assignment_and_role(false, 'update')

        expect(Assignment.where(app_user_id: user.id, agency_id:).count).to eq(0)
      end
    end
  end

  describe 'assignments and roles' do
    context 'edit_stealth_mode true' do
      it 'creates an assignment with portal_management true' do
        Api::PortalTeamsService.new(portal_team).set_assignment_and_role(true, 'create')

        expect(Assignment.where(app_user_id: user.id, agency_id:).count).to eq(1)
        expect(Assignment.find_by(app_user_id: user.id, agency_id:).portal_management).to eq(true)
      end

      it 'adds the user to Portal role' do
        Api::PortalTeamsService.new(portal_team).set_assignment_and_role(true, 'create')

        expect(UserRole.where(app_user_id: user.id, app_role_id: role.id).count).to eq(1)
      end
    end
  end
end
