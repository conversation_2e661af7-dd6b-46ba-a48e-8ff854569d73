# frozen_string_literal: true

require 'spec_helper'
require_relative 'common'
require_relative '../shared_examples'

module Api
  module Finance
    module UnitServices
      RSpec.describe 'Getter', type: :service, formatter_spec_helper: true do
        FORMAT = {
          property_id: nil,
          property_name: nil,
          finance_month_id: nil,
          finance_month_name: nil,
          calendar_year_id: nil,
          calendar_year: nil,
          data: [
            {
              finance_model_name: nil,
              locked: nil,
              data: [
                {
                  calendar_year: nil,
                  column_type: 'quarter',
                  data: [
                    {
                      finance_quarter: nil,
                      data: [
                        {
                          finance_unit_id: nil,
                          finance_header_id: nil,
                          property_id: nil,
                          calendar_year_id: nil,
                          finance_month_id: nil,
                          finance_model_id: nil,
                          allocation_id: nil,
                          finance_quarter_id: nil,
                          model_allocation_description: nil,
                          show_description: nil,
                          unit: nil,
                          allocation_name: nil,
                          locked: nil,
                          value_type: nil
                        }
                      ]
                    },
                    {
                      calendar_year: nil,
                      column_type: 'year',
                      data: [
                        {
                          calendar_year: nil,
                          data: [
                            {
                              property_id: nil,
                              calendar_year_id: nil,
                              finance_month_id: nil,
                              finance_model_id: nil,
                              allocation_id: nil,
                              unit: nil,
                              allocation_name: nil,
                              model_allocation_description: nil,
                              show_description: nil,
                              finance_unit_ids: nil,
                              finance_quarter_ids: nil,
                              finance_header_id: nil,
                              locked: nil,
                              value_type: nil
                            }
                          ]
                        }
                      ]
                    }
                  ]
                }
              ]
            }
          ]
        }.freeze

        include_context('finance_unit')
        let(:response_format) { FORMAT }
        let(:getter) { Getter }
        include_examples('finance_getter')
      end
    end
  end
end
