# frozen_string_literal: true

module Api
  module Finance
    module CpmServices
      RSpec.shared_context 'finance_cpm', finance_common_helper: true do
        before(:all) do
          finance_metric_type, finance_headers, finance_quarters = *fabricate_common_data('CPM')

          allocations = Array.new(5) do
            create(:model_allocation, finance_metric_type:, active: true).allocation
          end
          demo_daypart_defaults = Array.new(5) { create(:demo_daypart_default, property: @property) }

          finance_headers.product(allocations, demo_daypart_defaults,
                                  finance_quarters).each do |finance_header, allocation, demo_daypart, finance_quarter|
            create(:finance_cpm,
                   finance_header:,
                   finance_quarter:,
                   allocation:,
                   demographic: demo_daypart.demographic,
                   daypart: demo_daypart.daypart)
          end
        end

        after(:all) do
          cleanse_database
        end
      end
    end
  end
end
