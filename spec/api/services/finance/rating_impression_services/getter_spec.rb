# frozen_string_literal: true

require 'spec_helper'
require_relative 'common'
require_relative '../shared_examples'

module Api
  module Finance
    module RatingImpressionServices
      RSpec.describe 'Getter', type: :service, formatter_spec_helper: true do
        FORMAT = {
          property_id: nil,
          property_name: nil,
          finance_month_id: nil,
          finance_month_name: nil,
          calendar_year_id: nil,
          calendar_year: nil,
          data: [
            {
              finance_model_name: nil,
              locked: nil,
              data: [
                {
                  calendar_year: nil,
                  column_type: 'quarter',
                  data: [
                    {
                      finance_quarter: nil,
                      data: [
                        {
                          finance_rating_impression_id: nil,
                          finance_header_id: nil,
                          property_id: nil,
                          calendar_year_id: nil,
                          finance_month_id: nil,
                          finance_model_id: nil,
                          finance_quarter_id: nil,
                          demo_daypart_default_id: nil,
                          daypart_id: nil,
                          demographic_id: nil,
                          demo_daypart: nil,
                          rating_impression: nil,
                          locked: nil
                        }
                      ]
                    },
                    {
                      calendar_year: nil,
                      column_type: 'year',
                      data: [
                        {
                          calendar_year: nil,
                          data: [
                            {
                              finance_rating_impression_ids: nil,
                              finance_header_id: nil,
                              property_id: nil,
                              calendar_year_id: nil,
                              finance_month_id: nil,
                              finance_model_id: nil,
                              finance_quarter_ids: nil,
                              demo_daypart_default_id: nil,
                              daypart_id: nil,
                              demographic_id: nil,
                              demo_daypart: nil,
                              rating_impression: nil,
                              locked: nil
                            }
                          ]
                        }
                      ]
                    }
                  ]
                }
              ]
            }
          ]
        }.freeze

        include_context('finance_rating_impression')
        let(:response_format) { FORMAT }
        let(:getter) { Getter }
        include_examples('finance_getter')
      end
    end
  end
end
