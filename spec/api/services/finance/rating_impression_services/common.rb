# frozen_string_literal: true

module Api
  module Finance
    module RatingImpressionServices
      RSpec.shared_context 'finance_rating_impression', finance_common_helper: true do
        before(:all) do
          _finance_metric_type, finance_headers, finance_quarters = *fabricate_common_data('Rating Impression')

          demo_daypart_defaults = Array.new(5) { create(:demo_daypart_default, property: @property) }

          finance_headers.product(demo_daypart_defaults,
                                  finance_quarters).each do |finance_header, demo_daypart, finance_quarter|
            create(:finance_rating_impression,
                   finance_header:,
                   finance_quarter:,
                   demographic: demo_daypart.demographic,
                   daypart: demo_daypart.daypart)
          end
        end

        after(:all) do
          cleanse_database
        end
      end
    end
  end
end
