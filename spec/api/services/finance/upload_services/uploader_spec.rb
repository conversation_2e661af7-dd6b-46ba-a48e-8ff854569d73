# frozen_string_literal: true

require 'spec_helper'

module Api
  module Finance
    # rubocop: disable Metrics/ModuleLength
    module UploadServices
      RSpec.describe 'Uploader', type: :service, formatter_spec_helper: true do
        describe '#process' do
          before do
            @user = create(:user)
            @calendar_year = create(:calendar_year, show_in_finance: true)
            @last_year = create(:calendar_year, calendar_year: @calendar_year.calendar_year - 1, show_in_finance: true)
            @finance_month = create(:finance_month)
            @uploader = Uploader.new(@user, @calendar_year, @finance_month)

            ['Current Estimate', 'Budget', 'LRP', 'Actuals'].map { |name| create(:finance_model, name:) }
            ['Revenue', 'CPM', 'Risk And Opportunity', 'Revenue Variance'].each do |name|
              create(:finance_metric_type, name:)
            end
            [@calendar_year, @last_year].each do |year|
              5.times do
                create(:finance_quarter, calendar_year: year, finance_metric_type_id: -1)
              end
            end
            create(:finance_month, finance_month_name: 'Full-Year')
            5.times { create(:finance_month) }
            5.times { create(:allocation) }
            5.times { create(:daypart) }
            5.times { create(:demographic) }
            5.times { create(:finance_revenue_type) }
            5.times { create(:variance_type) }
            5.times { create(:variance_type_allocation) }
            @revenue_variance_mapping = create(:revenue_variance_mapping,
                                               associated_year: @calendar_year,
                                               associated_month: @finance_month,
                                               from_year: @last_year,
                                               from_finance_month: FinanceMonth.find_by_name('Full-Year'),
                                               from_finance_model: FinanceModel.find_by_name('Budget'),
                                               to_year: @calendar_year,
                                               to_finance_month: FinanceMonth.first,
                                               to_finance_model: FinanceModel.find_by_name('Current Estimate'))

            property = create(:property, include_finance_model: true)

            @valid_revenue = {
              finance_metric_type: 'Revenue',
              finance_model: 'Current Estimate',
              property: property.name,
              allocation: Allocation.first.name,
              finance_revenue_type: FinanceRevenueType.first.name,
              finance_quarter: FinanceQuarter.find_by(calendar_year: @calendar_year).name,
              model_allocation_description: 'Original glazed',
              quarter_value: 1_000
            }
            @valid_cpm = {
              finance_metric_type: 'CPM',
              finance_model: 'Current Estimate',
              property: property.name,
              allocation: Allocation.first.name,
              finance_revenue_type: FinanceRevenueType.first.name,
              finance_quarter: FinanceQuarter.find_by(calendar_year: @calendar_year).name,
              demographic: Demographic.first.name,
              daypart: Daypart.first.name,
              model_allocation_description: 'Original glazed',
              quarter_value: 1_000
            }
            @valid_risk_opp = {
              finance_metric_type: 'Risk And Opportunity',
              finance_model: 'Current Estimate',
              property: property.name,
              finance_quarter: FinanceQuarter.find_by(calendar_year: @calendar_year).name,
              variance_type: VarianceType.first.name,
              model_allocation_description: 'Original glazed',
              quarter_value: 1_000
            }
            @valid_revenue_variance = {
              finance_metric_type: 'Revenue Variance',
              property: property.name,
              from: [
                @revenue_variance_mapping.from_year.calendar_year,
                @revenue_variance_mapping.from_finance_model.name,
                @revenue_variance_mapping.from_finance_month.name
              ].join('_'),
              to: [
                @revenue_variance_mapping.to_year.name,
                @revenue_variance_mapping.to_finance_model.name,
                @revenue_variance_mapping.to_finance_month.name
              ].join('_'),
              variance_type: VarianceTypeAllocation.first.variance_type.name,
              variance_type_allocation: VarianceTypeAllocation.first.name,
              finance_quarter: FinanceQuarter.find_by(calendar_year: @calendar_year).name,
              model_allocation_description: 'Original glazed',
              quarter_value: 1_000
            }

            allow_any_instance_of(Api::Finance::Updater).to receive(:process)
            allow(Property).to receive(:for_finance_user).with(@user).and_return([property])
          end

          it 'processes multiple sheets' do
            data = [
              {
                sheet_name: 'Sheet1',
                sheet_data: [@valid_revenue]
              },
              {
                sheet_name: 'Sheet2',
                sheet_data: [@valid_revenue_variance]
              }
            ]
            result = @uploader.process_file(data)

            expect(result.status).to eq(:ok)
            expect(result.result.count).to eq(2)
          end

          it 'returns ok status_code if succeeded' do
            data = [{
              sheet_name: 'Sheet1',
              sheet_data: [@valid_revenue]
            }]
            result = @uploader.process_file(data)

            expect(result.status).to eq(:ok)
          end

          it 'processes different metric_types' do
            data = [{
              sheet_name: 'Sheet1',
              sheet_data: [@valid_revenue, @valid_cpm, @valid_risk_opp]
            }]
            result = @uploader.process_file(data)

            expect(result.status).to eq(:ok)
          end

          it 'returns bad_request status_code if any errors' do
            data = [{
              sheet_name: 'Sheet1',
              sheet_data: [
                @valid_revenue,
                @valid_revenue.merge(finance_metric_type: 'Fake Metric Type')
              ]
            }]
            result = @uploader.process_file(data)

            expect(result.status).to eq(:bad_request)
          end

          describe '#validate_row' do
            it 'validates finance_metric_type' do
              data = [{
                sheet_name: 'Sheet1',
                sheet_data: [
                  @valid_revenue,
                  @valid_revenue.merge(finance_metric_type: 'DonutMetricType')
                ]
              }]
              result = @uploader.process_file(data)

              expect(result.status).to eq(:bad_request)
              expect(result.result.first[:sheet_data].first[:error_types].include?('InvalidValueError')).to be(false)
              expect(result.result.first[:sheet_data].last[:error_types].include?('InvalidValueError')).to be(true)
            end

            it 'validates fields based on metric_type' do
              data = [{
                sheet_name: 'Sheet1',
                sheet_data: [
                  @valid_revenue,
                  @valid_revenue.merge(finance_metric_type: 'CPM')
                ]
              }]
              result = @uploader.process_file(data)

              expect(result.status).to eq(:bad_request)
              expect(result.result.first[:sheet_data].first[:error_types].include?('MissingRequiredValueError'))
                .to be(false)
              expect(result.result.first[:sheet_data].last[:error_types].include?('MissingRequiredValueError'))
                .to be(true)
            end

            it 'validates property' do
              data = [{
                sheet_name: 'Sheet1',
                sheet_data: [
                  @valid_revenue,
                  @valid_revenue.merge(property: 'DonutCompany'),
                  @valid_revenue.merge(property: create(:property).name),
                  @valid_revenue.merge(property: create(:property, include_finance_model: true).name)
                ]
              }]
              result = @uploader.process_file(data)

              expect(result.status).to eq(:bad_request)
              expect(result.result.first[:sheet_data].first[:error_types].include?('InvalidValueError')).to be(false)
              expect(result.result.first[:sheet_data].second[:error_types].include?('InvalidValueError')).to be(true)
              expect(result.result.first[:sheet_data].third[:error_types].include?('NotFinancePropertyError'))
                .to be(true)
              expect(result.result.first[:sheet_data].last[:error_types].include?('UserUnauthorizedError')).to be(true)
            end

            it 'validates finance_model' do
              data = [
                {
                  sheet_name: 'Sheet1',
                  sheet_data: [
                    @valid_revenue,
                    @valid_revenue.merge(finance_model: 'DonutModel'),
                    @valid_revenue.merge(finance_model: 'Actuals')
                  ]
                },
                {
                  sheet_name: 'Sheet2',
                  sheet_data: [
                    @valid_revenue_variance,
                    @valid_revenue_variance.merge(from: [
                      @revenue_variance_mapping.from_year.calendar_year,
                      'DonutModel',
                      @revenue_variance_mapping.from_finance_month.name
                    ].join('_')),
                    @valid_revenue_variance.merge(to: [
                      @revenue_variance_mapping.to_year.name,
                      'Actuals',
                      @revenue_variance_mapping.to_finance_month.name
                    ].join('_'))
                  ]
                }
              ]
              result = @uploader.process_file(data)

              expect(result.status).to eq(:bad_request)
              expect(result.result.first[:sheet_data].first[:error_types].include?('InvalidValueError'))
                .to be(false)
              expect(result.result.first[:sheet_data].second[:error_types].include?('InvalidValueError'))
                .to be(true)
              expect(result.result.first[:sheet_data].last[:error_types].include?('LockedFinanceModelError'))
                .to be(true)
              expect(result.result.second[:sheet_data].second[:error_types].include?('InvalidValueError'))
                .to be(true)
              expect(result.result.second[:sheet_data].last[:error_types].include?('LockedFinanceModelError'))
                .to be(false)
            end

            it 'validates finance_month' do
              data = [{
                sheet_name: 'Sheet1',
                sheet_data: [@valid_revenue]
              }]
              result = Uploader.new(@user, @calendar_year, FinanceMonth.find_by_name('Full-Year')).process_file(data)

              expect(result.status).to eq(:bad_request)
              expect(result.result.first[:sheet_data].first[:error_types].include?('InvalidFinanceMonthModelError'))
                .to be(true)
            end

            it 'validates finance_revenue_type' do
              data = [{
                sheet_name: 'Sheet1',
                sheet_data: [
                  @valid_revenue.merge(finance_revenue_type: nil),
                  @valid_revenue.merge(finance_revenue_type: 'Donut')
                ]
              }]
              result = @uploader.process_file(data)

              expect(result.status).to eq(:bad_request)
              expect(result.result.first[:sheet_data].first[:error_types].include?('MissingRequiredValueError'))
                .to be(true)
              expect(result.result.first[:sheet_data].second[:error_types].include?('InvalidValueError')).to be(true)
            end

            it 'validates demographic' do
              data = [{
                sheet_name: 'Sheet1',
                sheet_data: [
                  @valid_cpm.merge(demographic: nil),
                  @valid_cpm.merge(demographic: 'Donut')
                ]
              }]
              result = @uploader.process_file(data)

              expect(result.status).to eq(:bad_request)
              expect(result.result.first[:sheet_data].first[:error_types].include?('MissingRequiredValueError'))
                .to be(true)
              expect(result.result.first[:sheet_data].second[:error_types].include?('InvalidValueError')).to be(true)
            end

            it 'validates daypart' do
              data = [{
                sheet_name: 'Sheet1',
                sheet_data: [
                  @valid_cpm.merge(daypart: nil),
                  @valid_cpm.merge(daypart: 'Donut')
                ]
              }]
              result = @uploader.process_file(data)

              expect(result.status).to eq(:bad_request)
              expect(result.result.first[:sheet_data].first[:error_types].include?('MissingRequiredValueError'))
                .to be(true)
              expect(result.result.first[:sheet_data].second[:error_types].include?('InvalidValueError')).to be(true)
            end

            it 'validates variance_type' do
              data = [{
                sheet_name: 'Sheet1',
                sheet_data: [
                  @valid_risk_opp.merge(variance_type: nil),
                  @valid_risk_opp.merge(variance_type: 'Donut')
                ]
              }]
              result = @uploader.process_file(data)

              expect(result.status).to eq(:bad_request)
              expect(result.result.first[:sheet_data].first[:error_types].include?('MissingRequiredValueError'))
                .to be(true)
              expect(result.result.first[:sheet_data].second[:error_types].include?('InvalidValueError'))
                .to be(true)
            end

            it 'validates finance_quarter by finance_metric_type + calendar_year + finance_model' do
              data = [{
                sheet_name: 'Sheet1',
                sheet_data: [@valid_revenue.merge(finance_quarter: FinanceQuarter.last.name)]
              }]
              result = @uploader.process_file(data)

              expect(result.status).to eq(:bad_request)
              expect(result.result.first[:sheet_data].first[:error_types]
                    .include?('InvalidQuarterByMetricAndModelError')).to be(true)
            end

            it 'validates revenue_variance_mapping' do
              data = [{
                sheet_name: 'Sheet1',
                sheet_data: [
                  @valid_revenue_variance.merge(from: [
                    @revenue_variance_mapping.from_year.calendar_year,
                    'LRP',
                    @revenue_variance_mapping.from_finance_month.name
                  ].join('_'))
                ]
              }]
              result = @uploader.process_file(data)

              expect(result.status).to eq(:bad_request)
              expect(result.result.first[:sheet_data].first[:error_types].include?('InvalidVarianceMappingError'))
                .to be(true)
            end

            it 'validates variance_type_allocation' do
              data = [{
                sheet_name: 'Sheet1',
                sheet_data: [
                  @valid_revenue_variance.merge(variance_type: nil),
                  @valid_revenue_variance.merge(variance_type_allocation: nil),
                  @valid_revenue_variance.merge(variance_type_allocation: 'Donut')
                ]
              }]
              result = @uploader.process_file(data)

              expect(result.status).to eq(:bad_request)
              expect(result.result.first[:sheet_data].first[:error_types].include?('MissingRequiredValueError'))
                .to be(true)
              expect(result.result.first[:sheet_data].second[:error_types].include?('MissingRequiredValueError'))
                .to be(true)
              expect(result.result.first[:sheet_data].last[:error_types].include?('InvalidValueError'))
                .to be(true)
            end

            it 'validates format for value columns' do
              data = [{
                sheet_name: 'Sheet1',
                sheet_data: [
                  @valid_revenue,
                  @valid_revenue.merge(quarter_value: 'String and not text')
                ]
              }]

              result = @uploader.process_file(data)
              expect(result.status).to eq(:bad_request)
              expect(result.result.first[:sheet_data].first[:error_types].include?('InvalidFormatError')).to be(false)
              expect(result.result.first[:sheet_data].second[:error_types].include?('InvalidFormatError')).to be(true)
            end

            it 'adds attribute_id to data for update' do
              data = [{
                sheet_name: 'Sheet1',
                sheet_data: [@valid_revenue]
              }]
              result = @uploader.process_file(data)

              id_cols = %i[allocation_id finance_revenue_type_id finance_quarter_id property_id finance_model_id
                           finance_month_id calendar_year_id]
              expect(id_cols - result.result.first[:sheet_data].first.keys).to eq([])
            end
          end

          describe '#update_row' do
            it 'sends to updater service' do
              data = [{
                sheet_name: 'Sheet1',
                sheet_data: [@valid_revenue, @valid_cpm, @valid_risk_opp]
              }]
              expect_any_instance_of(Api::Finance::RevenueServices::Updater).to receive(:process).once
              expect_any_instance_of(Api::Finance::CpmServices::Updater).to receive(:process).once
              expect_any_instance_of(Api::Finance::RiskOpportunityServices::Updater).to receive(:process).once
              @uploader.process_file(data)
            end
          end
        end
      end
    end
    # rubocop: enable Metrics/ModuleLength
  end
end
