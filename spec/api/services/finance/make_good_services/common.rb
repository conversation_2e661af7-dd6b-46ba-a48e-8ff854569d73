# frozen_string_literal: true

module Api
  module Finance
    module MakeGoodServices
      RSpec.shared_context 'finance_make_good', finance_common_helper: true do
        before(:all) do
          finance_metric_type, finance_headers, finance_quarters = *fabricate_common_data('Make Good')

          allocations = Array.new(5) do
            create(:model_allocation, finance_metric_type:, active: true).allocation
          end

          finance_headers.product(allocations, finance_quarters).each do |finance_header, allocation, finance_quarter|
            create(:finance_make_good,
                   finance_header:,
                   finance_quarter:,
                   allocation:)
          end
        end

        after(:all) do
          cleanse_database
        end
      end
    end
  end
end
