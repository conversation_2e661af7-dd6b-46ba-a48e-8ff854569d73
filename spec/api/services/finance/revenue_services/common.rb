# frozen_string_literal: true

module Api
  module Finance
    module RevenueServices
      RSpec.shared_context 'finance_revenue', finance_common_helper: true do
        before(:all) do
          finance_metric_type, finance_headers, finance_quarters = *fabricate_common_data('Revenue')

          finance_revenue_types =
            (PROPERTY_TYPE == 'Linear' ? ['Linear'] : ['Platform', 'Sales Type', 'Marketplace'])
            .map do |finance_revenue_type_name|
              create(:finance_revenue_type,
                     finance_revenue_type_name:,
                     property_type: @property.property_type)
            end

          finance_revenue_types.each do |finance_revenue_type|
            Array.new(5) do
              create(:model_allocation,
                     finance_metric_type:,
                     finance_revenue_type:,
                     active: true)
            end
          end

          allocations = Allocation.all

          finance_headers
            .product(allocations, finance_revenue_types, finance_quarters)
            .each do |finance_header, allocation, finance_revenue_type, finance_quarter|
            create(:finance_revenue,
                   finance_header:,
                   finance_quarter:,
                   allocation:,
                   finance_revenue_type:)
          end
        end

        after(:all) do
          cleanse_database
        end
      end
    end
  end
end
