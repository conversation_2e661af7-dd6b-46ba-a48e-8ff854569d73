# frozen_string_literal: true

require 'spec_helper'
require_relative 'common'
require_relative '../shared_examples'

module Api
  module Finance
    module RevenueServices
      RSpec.describe 'Updater', type: :service, finance_common_helper: true do
        PROPERTY_TYPE = 'Linear'
        let(:updater) { Updater.new(create(:user)) }
        include_context('finance_revenue')
        include_examples('finance_updater')
      end
    end
  end
end
