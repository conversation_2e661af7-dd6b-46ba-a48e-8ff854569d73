# frozen_string_literal: true

require 'spec_helper'
require_relative 'common'
require_relative '../shared_examples'

module Api
  module Finance
    module RevenueServices
      RSpec.describe 'DigitalGetter', type: :service, formatter_spec_helper: true do
        FORMAT = {
          property_id: nil,
          property_name: nil,
          finance_month_id: nil,
          finance_month_name: nil,
          calendar_year_id: nil,
          calendar_year: nil,
          property_type_name: nil,
          data: [
            {
              finance_model_name: nil,
              locked: nil,
              data: [
                {
                  calendar_year: nil,
                  column_type: 'quarter',
                  data: [
                    {
                      finance_revenue_type_name: nil,
                      data: [
                        {
                          finance_quarter_name: nil,
                          data: [
                            {
                              finance_revenue_id: nil,
                              finance_header_id: nil,
                              calendar_year_id: nil,
                              finance_month_id: nil,
                              finance_model_id: nil,
                              property_id: nil,
                              finance_quarter_id: nil,
                              allocation_id: nil,
                              finance_revenue_type_id: nil,
                              show_description: nil,
                              allocation_name: nil,
                              model_allocation_description: nil,
                              locked: nil,
                              swing_dollars: nil,
                              quarter_dollars: nil,
                              value_type: nil
                            }
                          ]
                        }
                      ]
                    }
                  ]
                },
                {
                  calendar_year: nil,
                  column_type: 'year',
                  data: [
                    {
                      finance_revenue_type_name: nil,
                      data: [
                        {
                          calendar_year: nil,
                          data: [
                            {
                              finance_revenue_ids: nil,
                              finance_quarter_ids: nil,
                              finance_header_id: nil,
                              allocation_id: nil,
                              finance_revenue_type_id: nil,
                              calendar_year_id: nil,
                              finance_month_id: nil,
                              finance_model_id: nil,
                              property_id: nil,
                              show_description: nil,
                              model_allocation_description: nil,
                              quarter_dollars: nil,
                              locked: nil,
                              value_type: nil
                            }
                          ]
                        }
                      ]
                    }
                  ]
                }
              ]
            }
          ]
        }.freeze

        PROPERTY_TYPE = 'Digital'

        include_context('finance_revenue')
        let(:response_format) { FORMAT }
        let(:getter) { DigitalGetter }
        include_examples('finance_getter')
      end
    end
  end
end
