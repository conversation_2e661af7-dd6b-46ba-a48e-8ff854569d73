# frozen_string_literal: true

RSpec.shared_examples 'finance_getter' do
  let(:result) { getter.new(@property, @finance_month, @calendar_year).process }

  describe 'index' do
    it 'returns data for the correct property' do
      expect(result[:property_id]).to eq(@property.id)
      expect(result[:property_name]).to eq(@property.name)
    end

    it 'returns data for the correct month' do
      expect(result[:finance_month_id]).to eq(@finance_month.id)
      expect(result[:finance_month_name]).to eq(@finance_month.name)
    end

    it 'returns data for the correct year' do
      expect(result[:calendar_year_id]).to eq(@calendar_year.id)
      expect(result[:calendar_year]).to eq(@calendar_year.calendar_year)
    end

    it 'returns data in expected format' do
      expect(result[:data].count).to eq(4)
      check_structure(response_format, result)
    end

    it 'filters by finance_model' do
      result = getter.new(@property, @finance_month, @calendar_year, finance_model_id: FinanceModel.first.id).process
      expect(result[:data].count).to eq(1)
    end
  end
end

RSpec.shared_examples 'finance_updater' do
  describe 'process' do
    before do
      @updater = updater
      @base_model = @updater.base_model
      @pk = @updater.base_model_pk
      @composite_pks = @updater.base_model_composite_pks
      @update_attr = @updater.editable_attributes.first

      allow_any_instance_of(@base_model).to receive(:must_not_be_locked).and_return(nil)
      allow(FinanceModel).to receive(:current_estimate).and_return(FinanceModel.find_by_name('Current Estimate'))
      allow(FinanceModel).to receive(:actuals).and_return(FinanceModel.find_by_name('Actuals'))
      allow(FinanceMonth).to receive(:yearly_month).and_return(FinanceMonth.find_by_name('Full-Year'))
    end

    it 'returns error if missing required fields' do
      record = @base_model.last

      expect { @updater.process([{ @update_attr => 1_000 }]) }.to raise_error(Error::ApiError::BadRequestError)
      expect { @updater.process([{ @pk => record.id, @update_attr => 1_000 }]) }.to_not raise_error
      expect { @updater.process([construct_composite_update_hash(record)]) }.to_not raise_error
    end

    it 'updates an existing record' do
      record = @base_model.last

      @updater.process([{ @pk => record.id, @update_attr => 111 }])
      expect(@base_model.find(record.id).send(@update_attr).to_i).to eq(111)

      @updater.process([construct_composite_update_hash(record, 222)])
      expect(@base_model.find(record.id).send(@update_attr).to_i).to eq(222)
    end

    it 'creates a new record' do
      record = @base_model.last
      new_property = create(:property)
      before_count = @base_model.count

      @updater.process([construct_composite_update_hash(record).merge(property_id: new_property.id)])
      expect(@base_model.count).to eq(before_count + 1)
      expect(@base_model.last.finance_header.property_id).to eq(new_property.id)
    end
  end
end
