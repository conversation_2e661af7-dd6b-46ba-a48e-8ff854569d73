# frozen_string_literal: true

require 'spec_helper'
require_relative 'common'
require_relative '../shared_examples'

module Api
  module Finance
    module RiskOpportunityServices
      RSpec.describe 'Updater', type: :service, finance_common_helper: true do
        let(:updater) { Updater.new(create(:user)) }
        include_context('finance_risk_opportunity')
        include_examples('finance_updater')

        describe 'process' do
          before do
            @updater = updater
            @pk = @updater.base_model_pk
            @composite_pks = @updater.base_model_composite_pks
          end

          it 'updates existing finance_comment' do
            record = FinanceRiskOpportunity.last

            @updater.process([{ @pk => record.id, user_comment: 'Buy me donuts',
                                user_comment_updated_at: Time.now.strftime('%Y-%m-%d %H:%M:%S') }])
            expect(record.finance_comment.user_comment).to eq('Buy me donuts')
          end

          it 'creates new finance_comment' do
            record = FinanceRiskOpportunity.last
            new_property = create(:property)

            @updater.process([construct_composite_update_hash(record)
              .merge(property_id: new_property.id,
                     user_comment: 'Buy me pretzels',
                     user_comment_updated_at: Time.now.strftime('%Y-%m-%d %H:%M:%S'))])
            expect(FinanceRiskOpportunity.last.finance_comment.user_comment).to eq('Buy me pretzels')
          end
        end
      end
    end
  end
end
