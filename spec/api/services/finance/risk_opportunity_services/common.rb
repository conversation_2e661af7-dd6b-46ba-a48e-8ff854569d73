# frozen_string_literal: true

module Api
  module Finance
    module RiskOpportunityServices
      RSpec.shared_context 'finance_risk_opportunity', finance_common_helper: true do
        before(:all) do
          _finance_metric_type, finance_headers, finance_quarters = *fabricate_common_data('Risk And Opportunity')

          variance_types = Array.new(5) { create(:variance_type) }

          finance_headers.product(variance_types,
                                  finance_quarters).each do |finance_header, variance_type, finance_quarter|
            create(:finance_risk_opportunity,
                   finance_header:,
                   finance_quarter:,
                   variance_type:)
          end
        end

        after(:all) do
          cleanse_database
        end
      end
    end
  end
end
