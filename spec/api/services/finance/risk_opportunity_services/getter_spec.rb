# frozen_string_literal: true

require 'spec_helper'
require_relative 'common'
require_relative '../shared_examples'

module Api
  module Finance
    module RiskOpportunityServices
      RSpec.describe 'Getter', type: :service, formatter_spec_helper: true do
        FORMAT = {
          property_id: nil,
          property_name: nil,
          finance_month_id: nil,
          finance_month_name: nil,
          calendar_year_id: nil,
          calendar_year: nil,
          data: [
            {
              finance_model_name: nil,
              locked: nil,
              data: [
                {
                  calendar_year: nil,
                  column_type: 'quarter',
                  data: [
                    {
                      finance_quarter: nil,
                      data: [
                        {
                          finance_risk_opportunity_id: nil,
                          finance_header_id: nil,
                          property_id: nil,
                          calendar_year_id: nil,
                          finance_month_id: nil,
                          finance_model_id: nil,
                          finance_quarter_id: nil,
                          variance_type_id: nil,
                          quarter_dollars: nil,
                          variance_type_name: nil,
                          locked: nil,
                          user_comment: nil,
                          finance_comment_id: nil,
                          user_comment_updated_at: nil,
                          user_comment_updated_by: nil,
                          show_description: nil,
                          model_allocation_description: nil
                        }
                      ]
                    },
                    {
                      calendar_year: nil,
                      column_type: 'year',
                      data: [
                        {
                          calendar_year: nil,
                          data: [
                            {
                              finance_risk_opportunity_ids: nil,
                              finance_header_id: nil,
                              property_id: nil,
                              calendar_year_id: nil,
                              finance_month_id: nil,
                              finance_model_id: nil,
                              finance_quarter_ids: nil,
                              variance_type_id: nil,
                              quarter_dollars: nil,
                              variance_type_name: nil,
                              locked: nil,
                              finance_comment_id: nil,
                              user_comment_updated_at: nil,
                              user_comment_updated_by: nil,
                              show_description: nil,
                              model_allocation_description: nil
                            }
                          ]
                        }
                      ]
                    }
                  ]
                }
              ]
            }
          ]
        }.freeze

        include_context('finance_risk_opportunity')
        let(:response_format) { FORMAT }
        let(:getter) { Getter }
        include_examples('finance_getter')
      end
    end
  end
end
