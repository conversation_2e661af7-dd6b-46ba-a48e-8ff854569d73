# frozen_string_literal: true

module Api
  module Finance
    module RevenueVarianceServices
      RSpec.shared_context 'finance_revenue_variance' do
        before(:all) do
          @property = create(:property, :linear)
          @calendar_year = create(:calendar_year)
          @finance_month = create(:finance_month)
          ['Current Estimate', 'Budget', 'LRP', 'Actuals'].map { |name| create(:finance_model, name:) }
          4.times.each_with_index.map do |index|
            create(:quarter_date,
                   finance_quarter: create(:finance_quarter,
                                           calendar_year: @calendar_year,
                                           quarter: create(:quarter,
                                                           calendar_year: @calendar_year,
                                                           quarter: index + 1)))
          end
          finance_quarters = FinanceQuarter.all

          from_finance_model = FinanceModel.first
          to_finance_model = FinanceModel.last
          from_finance_header = create(:finance_header, property: @property,
                                                        calendar_year: @calendar_year,
                                                        finance_month: @finance_month,
                                                        finance_model: from_finance_model)
          to_finance_header = create(:finance_header, property: @property, calendar_year: @calendar_year,
                                                      finance_month: @finance_month, finance_model: to_finance_model)

          [from_finance_header, to_finance_header].product(finance_quarters).each do |finance_header, quarter|
            create(:finance_revenue, finance_header:, finance_quarter: quarter)
          end

          @revenue_variance_mapping = create(:revenue_variance_mapping,
                                             from_year: @calendar_year,
                                             from_finance_model:,
                                             from_finance_month: @finance_month,
                                             to_year: @calendar_year,
                                             to_finance_model:,
                                             to_finance_month: @finance_month,
                                             associated_month: @finance_month,
                                             associated_year: @calendar_year)
          finance_variance_header = create(:finance_variance_header, from_finance_header:,
                                                                     to_finance_header:)

          finance_quarters.each do |quarter|
            create(:finance_variance_detail, finance_variance_header:,
                                             finance_quarter: quarter, variance_dollars: 10_000)
          end
        end

        after(:all) do
          DatabaseCleaner.clean_with(:truncation, except: ActiveRecord::Base.connection.views + [:ar_internal_metadata])
        end
      end
    end
  end
end
