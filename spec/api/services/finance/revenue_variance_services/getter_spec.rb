# frozen_string_literal: true

require 'spec_helper'
require_relative 'common'

module Api
  module Finance
    module RevenueVarianceServices
      RSpec.describe 'Getter', type: :service, formatter_spec_helper: true do
        FORMAT = {
          property_id: nil,
          property_name: nil,
          finance_month_id: nil,
          finance_month_name: nil,
          calendar_year_id: nil,
          calendar_year: nil,
          data: [
            {
              summary: {
                from: {
                  revenue_variance_mapping_id: nil,
                  year: nil,
                  finance_month_name: nil,
                  finance_model_name: nil,
                  data: [
                    {
                      finance_quarter_name: nil,
                      net_dollars: nil
                    },
                    {
                      finance_quarter_name: nil,
                      net_dollars: nil
                    },
                    {
                      finance_quarter_name: nil,
                      net_dollars: nil
                    },
                    {
                      finance_quarter_name: nil,
                      net_dollars: nil
                    }
                  ]
                },
                to: {
                  revenue_variance_mapping_id: nil,
                  year: nil,
                  finance_month_name: nil,
                  finance_model_name: nil,
                  data: [
                    {
                      finance_quarter_name: nil,
                      net_dollars: nil
                    },
                    {
                      finance_quarter_name: nil,
                      net_dollars: nil
                    },
                    {
                      finance_quarter_name: nil,
                      net_dollars: nil
                    },
                    {
                      finance_quarter_name: nil,
                      net_dollars: nil
                    }
                  ]
                }
              },
              details: {
                revenue_variance_mapping_id: nil,
                data: [
                  {
                    variance_type_name: nil,
                    data: [
                      {
                        finance_quarter_name: nil,
                        data: [
                          {
                            finance_variance_detail_id: nil,
                            revenue_variance_mapping_id: nil,
                            finance_variance_header_id: nil,
                            variance_type_allocation_id: nil,
                            finance_quarter_id: nil,
                            from_finance_header_id: nil,
                            from_property_id: nil,
                            from_calendar_year_id: nil,
                            from_finance_month_id: nil,
                            from_finance_model_id: nil,
                            to_finance_header_id: nil,
                            to_property_id: nil,
                            to_calendar_year_id: nil,
                            to_finance_month_id: nil,
                            to_finance_model_id: nil,
                            model_allocation_description: nil,
                            variance_dollars: nil,
                            variance_type_allocation_name: nil
                          }
                        ]
                      }
                    ]
                  }
                ]
              }
            }
          ]
        }.freeze

        include_context('finance_revenue_variance')
        let(:result) { Getter.new(@property, @finance_month, @calendar_year).process }

        it 'returns data for the correct property' do
          expect(result[:property_id]).to eq(@property.id)
          expect(result[:property_name]).to eq(@property.name)
        end

        it 'returns data for the correct month' do
          expect(result[:finance_month_id]).to eq(@finance_month.id)
          expect(result[:finance_month_name]).to eq(@finance_month.name)
        end

        it 'returns data for the correct year' do
          expect(result[:calendar_year_id]).to eq(@calendar_year.id)
          expect(result[:calendar_year]).to eq(@calendar_year.calendar_year)
        end

        it 'returns data in expected format' do
          check_structure(FORMAT, result)
        end
      end
    end
  end
end
