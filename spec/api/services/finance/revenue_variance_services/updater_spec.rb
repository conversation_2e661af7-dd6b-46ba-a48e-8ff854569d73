# frozen_string_literal: true

require 'spec_helper'
require_relative 'common'

module Api
  module Finance
    module RevenueVarianceServices
      RSpec.describe 'Updater', type: :service do
        describe 'process' do
          include_context('finance_revenue_variance')

          before do
            @servicer = Updater.new(create(:user))
          end

          it 'throws error if missing required fields' do
            finance_variance_detail = FinanceVarianceDetail.last

            expect { @servicer.process([{ variance_dollars: 1_000 }]) }.to raise_error(Error::ApiError::BadRequestError)
            expect do
              @servicer.process([{ finance_variance_detail_id: finance_variance_detail.id,
                                   variance_dollars: 1_000 }])
            end.not_to raise_error

            property = create(:property, :linear)
            expect do
              @servicer.process([
                                  {
                                    finance_variance_detail_id: nil,
                                    variance_type_allocation_id: finance_variance_detail.variance_type_allocation_id,
                                    finance_quarter_id: finance_variance_detail.finance_quarter_id,
                                    from_property_id: property.id,
                                    from_calendar_year_id: @revenue_variance_mapping.from_year_id,
                                    from_finance_month_id: @revenue_variance_mapping.from_finance_month_id,
                                    from_finance_model_id: @revenue_variance_mapping.from_model_id,
                                    to_property_id: property.id,
                                    to_calendar_year_id: @revenue_variance_mapping.to_year_id,
                                    to_finance_month_id: @revenue_variance_mapping.to_finance_month_id,
                                    to_finance_model_id: @revenue_variance_mapping.to_model_id,
                                    model_allocation_description: 'Pretzels'
                                  }
                                ])
            end.not_to raise_error
          end

          it 'updates existing record' do
            before = FinanceVarianceDetail.last
            @servicer.process([{ finance_variance_detail_id: before.id, model_allocation_description: 'Donuts',
                                 variance_dollars: 9_999 }])
            after = FinanceVarianceDetail.find(before.id)
            expect(after.model_allocation_description).to eq('Donuts')
            expect(after.variance_dollars).to eq(9_999)
          end

          it 'creates new record' do
            property = create(:property, :linear)

            @servicer.process([
                                {
                                  variance_type_allocation_id: FinanceVarianceDetail.last.variance_type_allocation_id,
                                  finance_quarter_id: FinanceVarianceDetail.last.finance_quarter_id,
                                  from_property_id: property.id,
                                  from_calendar_year_id: @revenue_variance_mapping.from_year_id,
                                  from_finance_month_id: @revenue_variance_mapping.from_finance_month_id,
                                  from_finance_model_id: @revenue_variance_mapping.from_model_id,
                                  to_property_id: property.id,
                                  to_calendar_year_id: @revenue_variance_mapping.to_year_id,
                                  to_finance_month_id: @revenue_variance_mapping.to_finance_month_id,
                                  to_finance_model_id: @revenue_variance_mapping.to_model_id,
                                  model_allocation_description: 'Pretzels'
                                }
                              ])

            finance_variance_header = FinanceVarianceHeader.last
            finance_variance_detail = FinanceVarianceDetail.last
            from_finance_header = finance_variance_header.from_finance_header
            to_finance_header = finance_variance_header.to_finance_header
            expect(finance_variance_header.from_finance_header_id).to eq(from_finance_header.id)
            expect(finance_variance_header.to_finance_header_id).to eq(to_finance_header.id)
            expect(from_finance_header.property_id).to eq(property.id)
            expect(to_finance_header.property_id).to eq(property.id)
            expect(finance_variance_detail.finance_variance_header_id).to eq(finance_variance_header.id)
            expect(finance_variance_detail.model_allocation_description).to eq('Pretzels')
          end
        end
      end
    end
  end
end
