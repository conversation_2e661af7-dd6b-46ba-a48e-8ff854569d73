# frozen_string_literal: true

require 'spec_helper'

RSpec.describe 'PendingShiftRequestService', type: :service do
  let(:comment) { 'writing tests is so boring' }

  before(:each) do
    @user = create(:user)
    @shift_request = create(:shift_request)

    @servicer = Api::PendingShiftRequestService.new(@user, shift_request: @shift_request, comment:)
    stub_request(:post,
                 "http://#{ApiHelper::SMS_API_HOST}/api/shift_requests/#{@shift_request.id}/" \
                 'send_status_change_emails?access_token=abf76e0980e5109b9a3b53c073272593ee629eee')
      .to_return(status: 200)
  end

  describe '#reject' do
    context 'user not allowed to change status' do
      it 'throws error if user isnt allowed to reject' do
        allow(@user).to receive(:has_entitlement?).with(:sic_approve_reject).and_return(false)
        expect { @servicer.reject }.to raise_error(Error::ApiError::ForbiddenError)
      end
    end

    context 'sic allowed to change status' do
      before(:each) do
        allow(@user).to receive(:has_entitlement?).with(:sic_approve_reject).and_return(true)
        allow(ShiftRequestHistory).to receive(:reject_shift_request!).with(@shift_request, @user,
                                                                           comment).and_return(true)
      end

      it 'succeeds' do
        expect { @servicer.reject }.to_not raise_error
      end

      it 'updates shift request status' do
        @servicer.reject
        expect(@shift_request.shift_request_status).to eq(ShiftRequestStatus.sic_rejected)
      end

      it 'updates history' do
        @servicer.reject
        expect(ShiftRequestHistory).to have_received(:reject_shift_request!).with(@shift_request, @user, comment).once
      end
    end

    context 'ae allowed to change status' do
      before(:each) do
        allow(@user).to receive(:account_executive).and_return(true)
        allow(ShiftRequestHistory).to receive(:reject_shift_request!).with(@shift_request, @user,
                                                                           comment).and_return(true)
      end

      it 'succeeds' do
        expect { @servicer.reject }.to_not raise_error
      end

      it 'updates shift request status' do
        @servicer.reject
        expect(@shift_request.shift_request_status).to eq(ShiftRequestStatus.ae_rejected)
      end

      it 'updates history' do
        @servicer.reject
        expect(ShiftRequestHistory).to have_received(:reject_shift_request!).with(@shift_request, @user, comment).once
      end
    end
  end

  describe '#approve' do
    context 'user not allowed to change status' do
      it 'throws error if user isnt allowed to approve' do
        allow(@user).to receive(:has_entitlement?).with(:sic_approve_reject).and_return(false)
        expect { @servicer.approve }.to raise_error(Error::ApiError::ForbiddenError)
      end
    end

    context 'sic allowed to change status' do
      before(:each) do
        allow(@user).to receive(:has_entitlement?).with(:sic_approve_reject).and_return(true)
        allow(ShiftRequestHistory).to receive(:approve_shift_request!).with(@shift_request, @user,
                                                                            comment).and_return(true)
      end

      it 'succeeds' do
        expect { @servicer.approve }.to_not raise_error
      end

      it 'updates shift request status' do
        @servicer.approve
        expect(@shift_request.shift_request_status).to eq(ShiftRequestStatus.sic_approved)
      end

      it 'updates history' do
        @servicer.approve
        expect(ShiftRequestHistory).to have_received(:approve_shift_request!).with(@shift_request, @user, comment).once
      end

      it 'sends status change email' do
        expect(@servicer).to receive(:send_status_change_email).once
        @servicer.approve
      end

      it 'throws error if status change email sending failed' do
        stub_request(:post,
                     "http://#{ApiHelper::SMS_API_HOST}/api/shift_requests/#{@shift_request.id}" \
                     '/send_status_change_emails?access_token=abf76e0980e5109b9a3b53c073272593ee629eee')
          .to_return(status: 400)
        expect { @servicer.approve }.to raise_error(Error::ApiError::FailedDependencyError)
      end
    end

    context 'ae allowed to change status' do
      before(:each) do
        allow(@user).to receive(:account_executive).and_return(true)
        allow(ShiftRequestHistory).to receive(:approve_shift_request!).with(@shift_request, @user,
                                                                            comment).and_return(true)
      end

      it 'succeeds' do
        expect { @servicer.approve }.to_not raise_error
      end

      it 'updates shift request status' do
        @servicer.approve
        expect(@shift_request.shift_request_status).to eq(ShiftRequestStatus.ae_approved)
      end

      it 'updates history' do
        @servicer.approve
        expect(ShiftRequestHistory).to have_received(:approve_shift_request!).with(@shift_request, @user, comment).once
      end

      it 'sends status change email' do
        expect(@servicer).to receive(:send_status_change_email).once
        @servicer.approve
      end

      it 'throws error if status change email sending failed' do
        stub_request(:post,
                     "http://#{ApiHelper::SMS_API_HOST}/api/shift_requests/#{@shift_request.id}" \
                     '/send_status_change_emails?access_token=abf76e0980e5109b9a3b53c073272593ee629eee')
          .to_return(status: 400)
        expect { @servicer.approve }.to raise_error(Error::ApiError::FailedDependencyError)
      end
    end
  end
end
