# frozen_string_literal: true

require 'spec_helper'

RSpec.describe 'BudgetManagementScenarios', type: :api do
  let!(:calendar_year) { create(:calendar_year, default_calendar_year: true) }

  describe 'GET /budget_management_scenarios' do
    it 'works' do
      get '/api/budget_management_scenarios'
      expect(last_response.status).to eq(200)
    end

    it 'returns json array of BudgetManagementScenarios' do
      get '/api/budget_management_scenarios'
      json_response = JSON.parse(last_response.body)
      expect(json_response.is_a?(Array)).to eq(true)
    end

    it 'applies for_user filter depending on buying_ae_only param' do
      expect(BudgetManagementScenario).to receive(:for_user).with(anything, 'true')
      get '/api/budget_management_scenarios?buying_ae_only=true'

      expect(BudgetManagementScenario).to receive(:for_user).with(anything, nil)
      get '/api/budget_management_scenarios'
    end
  end
end
