# frozen_string_literal: true

require 'spec_helper'

RSpec.describe 'deal_link_associations', type: :api do
  let(:response_body) { JSON.parse(last_response.body) }

  describe '#create' do
    let!(:deal) { create(:deal) }
    let!(:deal_link) { create(:deal_link) }

    it 'requires deal_id' do
      post '/api/deal_link_associations', deal_id: deal.id
      expect(last_response.status).to eq(400)
    end

    it 'requires deal_link_id' do
      post '/api/deal_link_associations', deal_link_id: deal_link.id
      expect(last_response.status).to eq(400)
    end

    it 'creates a new record' do
      post '/api/deal_link_associations', deal_id: deal.id, deal_link_id: deal_link.id
      expect(last_response.status).to eq(201)
    end

    it 'requires budget_year_id when returning deals' do
      post '/api/deal_link_associations', deal_id: deal.id, deal_link_id: deal_link.id, return_deals: true
      expect(last_response.status).to eq(400)
    end

    it 'successfully returns deals' do
      post '/api/deal_link_associations',
           deal_id: create(:deal).id,
           deal_link_id: create(:deal_link).id,
           return_deals: true,
           budget_year_id: create(:budget_year).id
      expect(last_response.status).to eq(201)
      expect(response_body.is_a?(Array)).to be(true)
    end
  end

  describe '#batch_create' do
    let!(:advertiser) { create(:advertiser) }
    let!(:agency) { create(:agency) }
    let!(:deal_link_type) { create(:deal_link_type) }
    let!(:deal_link) { create(:deal_link, base_advertiser: advertiser, agency:) }
    let!(:deal) { create(:deal, advertiser:, agency:) }
    let!(:deal2) { create(:deal, advertiser:, agency:) }

    it 'creates successfully' do
      expect(DealLinkAssociation.count).to eq(0)

      post '/api/deal_link_associations/batch_create', deal_link_associations: [
        { deal_link_id: deal_link.deal_link_id, deal_id: deal.id },
        { deal_link_id: deal_link.deal_link_id, deal_id: deal2.id }
      ]
      expect(last_response.status).to eq(201)
      expect(DealLinkAssociation.where(deal_link_id: deal_link.deal_link_id).count).to eq(2)
      expect(DealLinkAssociation.where(deal_link_id: deal_link.deal_link_id).map(&:deal_id)).to eq([deal.id, deal2.id])
    end
  end

  describe '#destroy' do
    before(:each) do
      @assoc = create(:deal_link_association)
    end

    it 'deletes a deal_link_association' do
      expect(DealLinkAssociation.find_by(deal_link_asc_id: @assoc.id)).not_to be_nil
      delete "/api/deal_link_associations/#{@assoc.id}"
      expect(last_response.status).to eq(200)
      expect(DealLinkAssociation.find_by(deal_link_asc_id: @assoc.id)).to be_nil
    end

    it 'requires budget_year_id when returning deals' do
      delete "/api/deal_link_associations/#{@assoc.id}", return_deals: true
      expect(last_response.status).to eq(400)
    end

    it 'successfully returns deals' do
      delete "/api/deal_link_associations/#{@assoc.id}", return_deals: true, budget_year_id: create(:budget_year).id
      expect(last_response.status).to eq(200)
      expect(response_body.is_a?(Array)).to be(true)
    end
  end
end
