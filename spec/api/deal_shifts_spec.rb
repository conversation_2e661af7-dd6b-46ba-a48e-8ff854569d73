# frozen_string_literal: true

require 'spec_helper'

RSpec.describe 'deal_shifts', type: :api do
  include ActiveJob::TestHelper

  let!(:new_agency) { create(:agency) }
  let!(:budget_year) { create(:budget_year) }
  let!(:app_user) { create(:user) }
  let!(:deal) { create(:deal) }
  let!(:user) { create(:user, email: FFaker::Internet.email) }

  before do
    [new_agency, deal.agency].each do |agency|
      create(
        :portal_team,
        unlock_request: true,
        budget_year:,
        agency:,
        user: create(:user, active: true, email: '<EMAIL>')
      )
    end

    ['Single Deal Shift', 'Portfolio Shift', 'Pillar Shift'].each do |type|
      opt = DealShiftOption.find_or_create_by(name: type)
      allow(DealShiftOption).to receive(type.parameterize.underscore.gsub('_shift', '')).and_return(opt)
    end
  end

  let!(:deal_shift) do
    create(:agency_deal_shift,
           deal:,
           shift_to: new_agency.id,
           requested_by: user,
           budget_year:,
           deal_shift_option: DealShiftOption.single_deal)
  end
  let!(:deal_shift_guid) { create(:deal_shift_guid, deal_shift:, user:) }

  describe '#create' do
    before do
      deal_shift.update_attribute(:approved, true)
      allow_any_instance_of(Api::DealShiftsController).to receive(:user_by_sso_id).and_return(user)
    end

    it 'checks required params' do
      post '/api/deal_shifts', budget_year_id: budget_year.id
      expect(last_response.status).to eq(400)
    end

    it 'checks portal teams' do
      allow_any_instance_of(AgencyDealShift).to receive(:check_portals).and_raise(DealShift::MissingPortalsError)
      post '/api/deal_shifts',
           deal_id: deal.id,
           budget_year_id: budget_year.id,
           shift_to: new_agency.id,
           shift_type: 'AgencyDealShift'
      expect(last_response.status).to eq(422)
    end

    context 'with no nested children' do
      it 'creates with required params' do
        post '/api/deal_shifts',
             deal_id: deal.id,
             budget_year_id: budget_year.id,
             shift_to: new_agency.id,
             shift_type: 'AgencyDealShift'

        expect(DealShift.count).to eq(2)
        expect(last_response.status).to eq(201)
        expect(JSON.parse(last_response.body)['requested_by_id'].to_i).to eq(user.id)
      end

      it 'can create comments with nested params' do
        post '/api/deal_shifts',
             deal_id: deal.id,
             budget_year_id: budget_year.id,
             shift_to: new_agency.id,
             shift_type: 'AgencyDealShift',
             deal_shift_comments_attributes: [
               {
                 message: 'hello',
                 app_user_id: user.id
               }
             ]
        expect(DealShift.count).to eq(2)
        expect(last_response.status).to eq(201)
        expect(DealShiftComment.count).to eq(1)
        expect(JSON.parse(last_response.body)['comments'][0]['message']).to eq('hello')
        expect(JSON.parse(last_response.body)['comments'][0]['app_user_id']).to eq(user.id)
        expect(JSON.parse(last_response.body)['comments'][0]['commenter_name']).to eq(user.name)
      end

      it 'notifies when there is an error during creation' do
        allow_any_instance_of(AgencyDealShift).to receive(:check_portals).and_raise(DealShift::MissingPortalsError)
        expect_any_instance_of(Api::DealShiftMailerService).to receive(:notify_missing_portals).and_call_original
        expect do
          perform_enqueued_jobs do
            post '/api/deal_shifts',
                 deal_id: deal.id,
                 budget_year_id: budget_year.id,
                 shift_to: new_agency.id,
                 shift_type: 'AgencyDealShift'
          end
        end.to change { ActionMailer::Base.deliveries.size }.by(1)
        expect(last_response.status).to eq(422)
      end

      it 'triggers the deal shift mailer when deal shift is created' do
        expect_any_instance_of(Api::DealShiftMailerService)
          .to receive(:notify_requestee_shift_created).and_call_original
        expect_any_instance_of(Api::DealShiftMailerService)
          .to receive(:notify_old_agency_shift_created).and_call_original

        expect do
          perform_enqueued_jobs do
            post '/api/deal_shifts',
                 deal_id: deal.id,
                 budget_year_id: budget_year.id,
                 shift_to: new_agency.id,
                 shift_type: 'AgencyDealShift'
          end
        end.to change { ActionMailer::Base.deliveries.size }.by(2)
        expect(last_response.status).to eq(201)
      end
    end

    context 'with nested children' do
      it 'can create children ae deal_shifts' do
        expect do
          post '/api/deal_shifts',
               deal_id: deal.id,
               budget_year_id: budget_year.id,
               shift_to: new_agency.id,
               shift_type: 'AgencyDealShift',
               children_attributes: [
                 {
                   deal_id: deal.id,
                   budget_year_id: budget_year.id,
                   shift_to: create(:user).id,
                   shift_type: 'BuyingAeDealShift'
                 },
                 {
                   deal_id: deal.id,
                   budget_year_id: budget_year.id,
                   shift_to: create(:user).id,
                   shift_type: 'ClientAeDealShift'
                 }
               ]
        end.to change { DealShift.count }.by(3)

        deal_shift = AgencyDealShift.last
        expect(last_response.status).to eq(201)
        expect(BuyingAeDealShift.last.parent.id).to eq(deal_shift.id)
        expect(ClientAeDealShift.last.parent.id).to eq(deal_shift.id)
      end

      it 'does not send notification for children deal_shifts' do
        expect_any_instance_of(Api::DealShiftMailerService)
          .to receive(:notify_requestee_shift_created).and_call_original
        expect_any_instance_of(Api::DealShiftMailerService)
          .to receive(:notify_old_agency_shift_created).and_call_original

        expect do
          perform_enqueued_jobs do
            post '/api/deal_shifts',
                 deal_id: deal.id,
                 budget_year_id: budget_year.id,
                 shift_to: new_agency.id,
                 shift_type: 'AgencyDealShift',
                 children_attributes: [
                   {
                     deal_id: deal.id,
                     budget_year_id: budget_year.id,
                     shift_to: create(:user).id,
                     shift_type: 'BuyingAeDealShift'
                   },
                   {
                     deal_id: deal.id,
                     budget_year_id: budget_year.id,
                     shift_to: create(:user).id,
                     shift_type: 'ClientAeDealShift'
                   }
                 ]
          end
        end.to change { ActionMailer::Base.deliveries.size }.by(2)
        expect(last_response.status).to eq(201)
      end
    end
  end

  describe '#show' do
    it 'raises error when guid is invalid' do
      get '/api/deal_shifts/krispy_kreme_better_than_dunkin'

      expect(last_response.status).to eq(400)
    end

    it 'successfully returns deal_shift when guid is valid' do
      get "/api/deal_shifts/#{deal_shift_guid.guid}"

      expect(last_response.status).to eq(200)
      expect(JSON.parse(last_response.body)['deal_shift_id'].to_i).to eq(deal_shift.id)
    end
  end

  describe '#approve' do
    before do
      allow(Pubsub::Publisher).to receive(:deal_headers)
    end

    it 'raises error when guid is invalid' do
      post '/api/deal_shifts/krispy_kreme_better_than_dunkin/approve'

      expect(last_response.status).to eq(400)
      expect(Pubsub::Publisher).not_to have_received(:deal_headers)
    end

    context 'with no children' do
      it 'successfully approves when guid is valid' do
        expect_any_instance_of(Api::DealShiftMailerService)
          .to receive(:notify_requester_shift_approved).and_call_original
        expect_any_instance_of(Api::DealShiftMailerService)
          .to receive(:notify_old_agency_shift_updated).and_call_original

        expect do
          perform_enqueued_jobs do
            post "/api/deal_shifts/#{deal_shift_guid.guid}/approve"
          end
        end.to change { ActionMailer::Base.deliveries.size }.by(2)

        expect(last_response.status).to eq(200)
        expect(deal_shift.reload.approved).to be true
        expect(deal_shift.reload.responder_id).to eq(user.id)
        expect(JSON.parse(last_response.body)['deal_shift_id'].to_i).to eq(deal_shift.id)
        expect(Pubsub::Publisher).to have_received(:deal_headers)
      end
    end

    context 'with children' do
      before do
        create(:buying_ae_deal_shift, parent: deal_shift)
        create(:client_ae_deal_shift, parent: deal_shift)
      end

      it 'successfully approves when guid is valid' do
        expect_any_instance_of(Api::DealShiftMailerService)
          .to receive(:notify_requester_shift_approved).and_call_original
        expect_any_instance_of(Api::DealShiftMailerService)
          .to receive(:notify_old_agency_shift_updated).and_call_original

        expect do
          perform_enqueued_jobs do
            post "/api/deal_shifts/#{deal_shift_guid.guid}/approve"
          end
        end.to change { ActionMailer::Base.deliveries.size }.by(4)

        expect(last_response.status).to eq(200)
        expect(deal_shift.reload.approved).to be true
        expect(deal_shift.reload.responder_id).to eq(user.id)
        expect(JSON.parse(last_response.body)['deal_shift_id'].to_i).to eq(deal_shift.id)
        expect(Pubsub::Publisher).to have_received(:deal_headers)
      end
    end
  end

  describe '#reject' do
    before do
      allow(Pubsub::Publisher).to receive(:deal_headers)
    end

    it 'raises error when guid is invalid' do
      post '/api/deal_shifts/krispy_kreme_better_than_dunkin/reject'

      expect(last_response.status).to eq(400)
      expect(Pubsub::Publisher).not_to have_received(:deal_headers)
    end

    it 'successfully rejects when guid is valid' do
      expect_any_instance_of(Api::DealShiftMailerService)
        .to receive(:notify_requester_shift_rejected).and_call_original
      expect_any_instance_of(Api::DealShiftMailerService)
        .to receive(:notify_old_agency_shift_updated).and_call_original

      expect do
        perform_enqueued_jobs do
          post "/api/deal_shifts/#{deal_shift_guid.guid}/reject"
        end
      end.to change { ActionMailer::Base.deliveries.size }.by(2)

      expect(last_response.status).to eq(200)
      expect(deal_shift.reload.approved).to be false
      expect(JSON.parse(last_response.body)['deal_shift_id'].to_i).to eq(deal_shift.id)
      expect(Pubsub::Publisher).not_to have_received(:deal_headers)
    end
  end
end
