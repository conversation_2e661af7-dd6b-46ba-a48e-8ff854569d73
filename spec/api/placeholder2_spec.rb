# frozen_string_literal: true

require 'spec_helper'

RSpec.describe 'placeholder2', type: :api do
  before do
    5.times { create(:placeholder2) }
  end

  describe 'GET' do
    it 'returns success status' do
      get '/api/placeholder2'
      expect(last_response.status).to eq(200)
    end

    it 'returns data' do
      get '/api/placeholder2'
      json_response = JSON.parse(last_response.body)
      expect(json_response.is_a?(Array)).to eq(true)
      expect(json_response.count).to eq(5)
    end
  end
end
