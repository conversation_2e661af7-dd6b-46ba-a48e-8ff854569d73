# frozen_string_literal: true

require 'spec_helper'

RSpec.describe 'Demographics', type: :api do
  describe 'get /demographics' do
    it 'works' do
      get '/api/demographics'
      expect(last_response.status).to eq(200)
    end

    it 'returns json array of Demographics' do
      create_list(:demographic, 6)

      get '/api/demographics'
      response = last_response.body

      expect(Demographic.all.count).to eq(6)
      expect(JSON.parse(response).count).to eq(6)
    end

    it 'orders by display_order column' do
      expect(Demographic).to receive(:order).with(:display_order).and_call_original
      get '/api/demographics'
    end
  end
end
