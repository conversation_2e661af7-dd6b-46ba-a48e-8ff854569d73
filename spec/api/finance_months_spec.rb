# frozen_string_literal: true

require 'spec_helper'

RSpec.describe 'finance_months', type: :api do
  before do
    5.times { create(:finance_month) }
  end

  describe 'GET' do
    it 'returns success status' do
      get '/api/finance_months'
      expect(last_response.status).to eq(200)
    end

    it 'returns data' do
      get '/api/finance_months'
      json_response = JSON.parse(last_response.body)
      expect(json_response.is_a?(Array)).to eq(true)
      expect(json_response.count).to eq(5)
    end
  end
end
