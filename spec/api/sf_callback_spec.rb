# frozen_string_literal: true

require 'spec_helper'

RSpec.describe 'SfCallback', type: :api do
  let!(:deal) { create(:deal) }
  let!(:deal2) { create(:deal) }
  let!(:default_budget_year) { create(:budget_year, default_budget_year: true) }
  let!(:budget) { create(:budget, deal_id: deal.id, budget_year: default_budget_year) }
  let!(:budget2) { create(:budget, deal_id: deal2.id, budget_year: default_budget_year) }
  let!(:parent_deal) { create(:parent_deal) }

  describe 'PUT api/sf_callback/update_budgets' do
    it 'can update sf_deal_id and sf_opportunity_id into budgets' do
      budget.update_columns(sf_deal_id: '', sf_opportunity_id: '')
      budget2.update_columns(sf_deal_id: '', sf_opportunity_id: '')
      put '/api/sf_callback/update_budgets', budget_ids: "#{budget.id},#{budget2.id}",
                                             sf_deal_id: 'test_sf_deal_idx',
                                             sf_opportunity_id: 'test_sf_opportunity_idx'
      expect(last_response.status).to eq(200)
      updated_budget = Budget.find(budget.budget_id)
      expect(updated_budget.sf_deal_id).to eq('test_sf_deal_idx')
      expect(updated_budget.sf_opportunity_id).to eq('test_sf_opportunity_idx')
      updated_budget2 = Budget.find(budget2.budget_id)
      expect(updated_budget2.sf_deal_id).to eq('test_sf_deal_idx')
      expect(updated_budget2.sf_opportunity_id).to eq('test_sf_opportunity_idx')
    end
  end

  describe 'PUT api/sf_callback/parent_deal' do
    it 'can update sf_deal_id into a parent_deal' do
      parent_deal.update_columns(sf_deal_id: '')
      put '/api/sf_callback/update_parent_deal', parent_deal_id: parent_deal.parent_deal_id.to_s,
                                                 sf_deal_id: 'test_sf_deal_idx'
      expect(last_response.status).to eq(200)
      updated_parent_deal = ParentDeal.find(parent_deal.parent_deal_id)
      expect(updated_parent_deal.sf_deal_id).to eq('test_sf_deal_idx')
    end
  end

  describe 'PUT api/sf_callback/update_special_event_details' do
    let!(:special_event) { create(:special_event) }
    let!(:special_event_deal) { create(:special_event_deal) }
    let!(:property) { create(:property, name: 'Test Property') }
    let!(:property2) { create(:property, name: 'Test Property 2') }
    let!(:special_event_detail) do
      PamClient::SpecialEventDealDetail.create!(
        sf_deal_id: '',
        sf_opportunity_id: '',
        sf_sync_status: nil,
        property:,
        budget_dollars: 1000,
        special_event_deal:,
        special_event:
      )
    end
    let!(:special_event_detail2) do
      PamClient::SpecialEventDealDetail.create!(
        sf_deal_id: '',
        sf_opportunity_id: '',
        sf_sync_status: nil,
        property: property2,
        budget_dollars: 2000,
        special_event_deal:,
        special_event:
      )
    end

    it 'can update sf_deal_id, sf_opportunity_id, and sf_sync_status for special event details' do
      put '/api/sf_callback/update_special_event_details',
          special_event_details_ids: [
            special_event_detail.special_event_deal_detail_id,
            special_event_detail2.special_event_deal_detail_id
          ].join(','),
          sf_deal_id: 'test_sf_deal_id',
          sf_opportunity_id: 'test_sf_opportunity_id',
          sf_sync_status: 'synced'

      expect(last_response.status).to eq(200)

      updated_detail = PamClient::SpecialEventDealDetail.find(special_event_detail.special_event_deal_detail_id)
      expect(updated_detail.sf_deal_id).to eq('test_sf_deal_id')
      expect(updated_detail.sf_opportunity_id).to eq('test_sf_opportunity_id')
      expect(updated_detail.sf_sync_status).to eq('synced')

      updated_detail2 = PamClient::SpecialEventDealDetail.find(special_event_detail2.special_event_deal_detail_id)
      expect(updated_detail2.sf_deal_id).to eq('test_sf_deal_id')
      expect(updated_detail2.sf_opportunity_id).to eq('test_sf_opportunity_id')
      expect(updated_detail2.sf_sync_status).to eq('synced')
    end
  end
end
