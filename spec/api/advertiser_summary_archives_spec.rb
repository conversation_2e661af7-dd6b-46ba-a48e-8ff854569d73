# frozen_string_literal: true

require 'spec_helper'
require 'rails_helper'

RSpec.describe 'AdvertiserSummaryArchives', type: :api do
  let(:response_body) { JSON.parse(last_response.body) }
  let!(:budget_year) { create(:budget_year, default_budget_year: true, prior_budget_year_id: create(:budget_year).id) }

  describe 'POST api/advertiser_summaries/archives' do
    before do
      create(:marketplace, marketplace_name: 'Upfront')
      create(:marketplace, marketplace_name: 'Calendar')
      create(:marketplace, marketplace_name: '<PERSON>atter')
      create(:business_type, business_type_name: 'Current Contracts')
      create(:business_type, business_type_name: 'Working')

      5.times { create(:advertiser, active: true, target_account: true) }
      5.times do
        create(:advertiser_summary,
               budget_year:,
               advertiser: create(:advertiser, active: true, target_account: true),
               client_partnership_projection: 999_999)
      end
    end

    it 'works' do
      expect(AdvertiserSummaryArchiveHeader.count).to eq(0)
      expect(AdvertiserSummaryArchiveDetail.count).to eq(0)
      post '/api/advertiser_summaries/archives'

      expect(AdvertiserSummaryArchiveHeader.count).to eq(1)
      expect(AdvertiserSummaryArchiveHeader.first.budget_year_id).to eq(budget_year.id)
      expect(AdvertiserSummaryArchiveDetail.count).to eq(10)
      expect(AdvertiserSummaryArchiveDetail.where(client_partnership_projection: 999_999).count).to eq(5)
    end
  end

  describe 'GET api/advertiser_summaries/archives' do
    before do
      5.times { create(:advertiser_summary_archive_header, budget_year:) }
      10.times { create(:advertiser_summary_archive_header, budget_year: budget_year.prior_budget_year) }
    end

    it 'works' do
      get '/api/advertiser_summaries/archives'
      expect(last_response.status).to eq(200)
      expect(response_body.count).to eq(5)
    end

    it 'filters by budget_year_id' do
      get '/api/advertiser_summaries/archives', budget_year_id: budget_year.prior_budget_year_id
      expect(last_response.status).to eq(200)
      expect(response_body.count).to eq(10)
    end
  end
end
