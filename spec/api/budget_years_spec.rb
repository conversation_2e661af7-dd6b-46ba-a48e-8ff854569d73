# frozen_string_literal: true

require 'spec_helper'

RSpec.describe 'budget_years', type: :api do
  context 'publish' do
    let(:user) { create(:user) }
    let!(:budget_year) { create(:budget_year) }

    before do
      allow_any_instance_of(Api::BudgetYearsController).to receive(:user_by_sso_id).and_return(user)
    end

    context 'unauthorized' do
      it 'returns 401' do
        post '/api/budget_years/publish'
        expect(last_response.status).to eq(401)
      end
    end

    context 'authorized' do
      before(:each) do
        user.user_roles << build(:user_role, user:, role: build(:role, :admin))
      end

      describe 'post /publish' do
        it 'can publish budget years' do
          msg = BudgetYear.all.to_json
          expect(Pubsub::Publisher).to receive(:budget_years).with(msg)
          post '/api/budget_years/publish'
          expect(last_response.status).to eq(200)
        end
      end
    end
  end
end
