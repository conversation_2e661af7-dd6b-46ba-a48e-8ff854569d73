# frozen_string_literal: true

require 'spec_helper'

RSpec.describe 'AgencyOfRecords', type: :api do
  context 'update' do
    before do
      create(:default_category, product_category_id: 555)
      create(:system_key_association, external_key_type: category_key_type,
                                      external_system: external_sfdc_system,
                                      pam_key: 555, external_system_key: 'abc321',
                                      system_key_asc_type_id: combined_system_key_asc_type.id)
      create(:system_key_association, external_key_type: location_key_type,
                                      external_system: external_sfdc_system,
                                      pam_key: 777, external_system_key: 'jkaldfjkaldf',
                                      system_key_asc_type_id: combined_system_key_asc_type.id)
      create(:location, location_id: 777)
    end

    let!(:combined_system_key_asc_type) { create(:system_key_asc_type, system_key_asc_type_name: 'COMBINED') }
    let!(:advertiser_key_type) { create(:external_key_type, external_key_type_id: 1) }
    let!(:agency_key_type) { create(:external_key_type, external_key_type_id: 2) }
    let!(:category_key_type) { create(:external_key_type, external_key_type_id: 3) }
    let!(:location_key_type) { create(:external_key_type, external_key_type_id: 63) }
    let!(:property_type_key_type) { create(:external_key_type, external_key_type_id: 64) }
    let!(:external_sfdc_system) { create(:external_system, name: 'SFDC') }
    let!(:agency_of_record) { create(:agency_of_record, sfdc_id: 'salesforceid123') }

    let!(:body_existing) do
      {
        agency_of_records: [
          {
            sfdc_id: 'salesforceid123',
            agency_name: 'myagency',
            advertiser_name: 'myadvertiser',
            sfdc_default_category_id: 'abc321',
            sfdc_agency_id: '0017000000toJkPAAU',
            sfdc_advertiser_id: '0017000000nYJ9zAAG',
            sfdc_property_type_id: 'jkaldfjkadsf',
            sfdc_location_id: 'jkaldfjkaldf'
          }
        ]
      }
    end
    let!(:body_non_existing) do
      {
        agency_of_records: [
          {
            sfdc_id: 'newsalesforceid',
            agency_name: 'myagency',
            advertiser_name: 'myadvertiser',
            sfdc_default_category_id: 'abc321',
            sfdc_agency_id: '0017000000toJkPAAU',
            sfdc_advertiser_id: '0017000000nYJ9zAAG',
            sfdc_property_type_id: 'jkaldfjkadsf',
            sfdc_location_id: 'fdsfdswawer',
            end_date: Date.parse('2020-JAN-30'),
            aor: true
          }
        ]
      }
    end

    let!(:body_with_list) do
      {
        agency_of_records: [
          {
            sfdc_id: 'newsalesforce1',
            agency_name: 'myagency',
            advertiser_name: 'myadvertiser',
            sfdc_default_category_id: 'abc321',
            sfdc_agency_id: '0017000000toJkPAAU',
            sfdc_advertiser_id: '0017000000nYJ9zAAG',
            sfdc_property_type_id: 'jkaldfjkadsf',
            sfdc_location_id: 'jkaldfjkaldf'
          },
          {
            sfdc_id: 'newsalesforce2',
            agency_name: 'myagency',
            advertiser_name: 'myadvertiser',
            sfdc_default_category_id: 'abc321',
            sfdc_agency_id: '0017000000toJkPAAU',
            sfdc_advertiser_id: '0017000000nYJ9zAAG',
            sfdc_property_type_id: 'jkaldfjkadsf',
            sfdc_location_id: 'jkaldfjkaldf'
          }
        ]
      }
    end

    let!(:body_with_list_for_bulk_updates) do
      {
        agency_of_records: [
          {
            sfdc_id: 'salesforceid123',
            agency_name: 'myagency',
            advertiser_name: 'myadvertiser',
            sfdc_default_category_id: 'abc321',
            sfdc_agency_id: '0017000000toJkPAAU',
            sfdc_advertiser_id: '0017000000nYJ9zAAG',
            sfdc_property_type_id: 'jkaldfjkadsf',
            sfdc_location_id: 'jkaldfjkaldf'
          },
          {
            sfdc_id: 'newsalesforce2',
            agency_name: 'myagency',
            advertiser_name: 'myadvertiser',
            sfdc_default_category_id: 'abc321',
            sfdc_agency_id: '0017000000toJkPAAU',
            sfdc_advertiser_id: '0017000000nYJ9zAAG',
            sfdc_property_type_id: 'jkaldfjkadsf',
            sfdc_location_id: 'jkaldfjkaldf'
          }
        ]
      }
    end

    let!(:body_without_aor_key) do
      {
        sfdc_id: 'newsalesforceid',
        agency_name: 'myagency',
        advertiser_name: 'myadvertiser',
        sfdc_default_category_id: 'abc321',
        sfdc_agency_id: '0017000000toJkPAAU',
        sfdc_advertiser_id: '0017000000nYJ9zAAG',
        sfdc_property_type_id: 'jkaldfjkadsf',
        sfdc_location_id: 'jkaldfjkaldf'
      }
    end

    describe 'PUT api/agency_of_records/' do
      it 'requires agency_of_records param' do
        put '/api/agency_of_records', body_without_aor_key
        expect(last_response.status).to eq(400)
      end

      context 'single update' do
        it 'can update the available fields' do
          put '/api/agency_of_records', body_existing

          updated_aor = AgencyOfRecord.find_by(sfdc_id: 'salesforceid123')

          expect(last_response.status).to eq(200)

          expect(updated_aor[:agency_id]).to eq(Agency.find_by(agency_name: 'myagency').id)
          expect(updated_aor[:advertiser_id]).to eq(Advertiser.find_by(advertiser_name: 'myadvertiser').id)
          expect(updated_aor[:location_id]).to eq(777)
        end

        it 'searches for existing record by advertiser agency and property type' do
          create(:system_key_association,
                 external_key_type: agency_key_type,
                 external_system: external_sfdc_system,
                 pam_key: 777,
                 external_system_key: '0017000000toJkPAAU',
                 system_key_asc_type_id: combined_system_key_asc_type.id)

          create(:system_key_association,
                 external_key_type: advertiser_key_type,
                 external_system: external_sfdc_system,
                 pam_key: 888,
                 external_system_key: '0017000000nYJ9zAAG',
                 system_key_asc_type_id: combined_system_key_asc_type.id)

          create(:system_key_association,
                 external_key_type: property_type_key_type,
                 external_system: external_sfdc_system,
                 pam_key: 999,
                 external_system_key: 'jkaldfjkadsf',
                 system_key_asc_type_id: combined_system_key_asc_type.id)

          create(:system_key_association,
                 external_key_type: location_key_type,
                 external_system: external_sfdc_system,
                 pam_key: 111,
                 external_system_key: 'fdsfdswawer',
                 system_key_asc_type_id: combined_system_key_asc_type.id)

          create(:agency, agency_id: 777)
          create(:advertiser, advertiser_id: 888)
          create(:agency_of_record, sfdc_id: nil, agency_id: 777, advertiser_id: 888, property_type_id: 999)

          put '/api/agency_of_records', body_non_existing

          existing_record = AgencyOfRecord
                            .find_by(agency_id: 777, advertiser_id: 888, property_type_id: 999)

          expect(existing_record.agency_id).to eq(777)
          expect(existing_record.advertiser_id).to eq(888)
          expect(existing_record.property_type_id).to eq(999)
          expect(existing_record.end_date).to eq(Date.parse('2020-JAN-30'))
          expect(existing_record.location_id).to eq(111)
          expect(existing_record.sfdc_id).to eq('newsalesforceid')
          expect(existing_record.aor).to eq(true)
        end

        it 'can create a new aor record' do
          put '/api/agency_of_records', body_non_existing
          created_aor = AgencyOfRecord.find_by(sfdc_id: 'newsalesforceid')

          expect(last_response.status).to eq(200)

          expect(created_aor).to_not be_nil

          expect(created_aor[:agency_id]).to eq(Agency.find_by(agency_name: 'myagency').id)
          expect(created_aor[:advertiser_id]).to eq(Advertiser.find_by(advertiser_name: 'myadvertiser').id)
        end
      end

      context 'bulk update' do
        it 'can accept a list of records for bulk create' do
          expect(AgencyOfRecord.count).to eq(1)
          put '/api/agency_of_records', body_with_list

          expect(last_response.status).to eq(200)

          expect(AgencyOfRecord.find_by(sfdc_id: 'newsalesforce1')).not_to be_nil
          expect(AgencyOfRecord.find_by(sfdc_id: 'newsalesforce2')).not_to be_nil
          expect(AgencyOfRecord.count).to eq(3)
        end

        it 'can accept a list of records for updates' do
          expect(AgencyOfRecord.count).to eq(1)
          put '/api/agency_of_records', body_with_list_for_bulk_updates

          expect(last_response.status).to eq(200)

          expect(AgencyOfRecord.count).to eq(2)

          first_aor = AgencyOfRecord.find_by(sfdc_id: 'salesforceid123')
          expect(first_aor).not_to be_nil
          expect(first_aor.agency).to eq(Agency.find_by(agency_name: 'myagency'))
          expect(first_aor.advertiser).to eq(Advertiser.find_by(advertiser_name: 'myadvertiser'))

          expect(AgencyOfRecord.find_by(sfdc_id: 'newsalesforce2')).not_to be_nil
        end
      end
    end
  end
end
