# frozen_string_literal: true

require 'spec_helper'

RSpec.describe 'Images', type: :api do
  describe 'api/images/#file_name' do
    it 'connects to s3 and returns an image by the provided name' do
      stub_request(:get,
                   'https://adsales-pam-assets-private.s3.amazonaws.com/my_logo.jpg').to_return(body: 'fake_image')

      get 'api/images/my_logo.jpg'

      expect(last_response.status).to eq(200)
      expect(last_response.body).to eq('fake_image')
    end

    it 'provides a default image when image not found in bucket by name' do
      stub_request(:get, 'https://adsales-pam-assets-private.s3.amazonaws.com/my_logo.jpg')
        .to_raise(Aws::S3::Errors::NoSuchKey.new(nil, nil))

      stub_request(:get, 'https://adsales-pam-assets-private.s3.amazonaws.com/default_logo.jpg')
        .to_return(body: 'successful_default_call')

      get 'api/images/my_logo.jpg'

      expect(last_response.status).to eq(200)
      expect(last_response.body).to eq('successful_default_call')
    end

    it 'can handle periods in the filename' do
      stub_request(:get, 'https://adsales-pam-assets-private.s3.amazonaws.com/my_logo.com.jpg')
        .to_return(body: 'fake_image')

      get 'api/images/my_logo.com.jpg'

      expect(last_response.status).to eq(200)
      expect(last_response.body).to eq('fake_image')
    end
  end
end
