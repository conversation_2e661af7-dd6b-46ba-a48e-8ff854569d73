# frozen_string_literal: true

require 'spec_helper'

RSpec.describe ApplicationController, type: :api do
  describe '#authenticate' do
    before(:each) do
      stub_request(:get,
                   "http://#{ApiHelper::SMS_API_HOST}/api/dropdown_values/OpportunityStatus" \
                   '?access_token=abf76e0980e5109b9a3b53c073272593ee629eee')
        .to_return(status: 200)
    end

    context 'with sso_id' do
      it 'returns 401 if user not found', no_auth: true do
        get '/api/opportunity_statuses?smuser=123'
        expect(last_response.status).to eq(401)
      end

      it 'it fulfills the request with status 200' do
        get '/api/opportunity_statuses?smuser=123'
        expect(last_response.status).to eq(200)
      end

      it 'allows lowercase smuser param' do
        get '/api/opportunity_statuses?smuser=123'
        expect(last_response.status).to eq(200)
      end

      it 'rejects inactive users' do
        allow_any_instance_of(ApplicationController)
          .to receive(:user_by_sso_id)
          .and_return(build_stubbed(:user, active: false, app_user_id: 123))
        get '/api/opportunity_statuses?smuser=123'
        expect(last_response.status).to eq(401)
      end

      it 'rejects external users' do
        allow_any_instance_of(ApplicationController)
          .to receive(:user_by_sso_id)
          .and_return(build_stubbed(:user, external_user: true, app_user_id: 123))
        get '/api/opportunity_statuses?smuser=123'
        expect(last_response.status).to eq(401)
      end
    end

    context 'without sso_id' do
      it 'returns 401', no_auth: true do
        get '/api/opportunity_statuses'
        expect(last_response.status).to eq(401)
      end
    end
  end
end
