# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'stealth_mode/budgets', type: :api do
  let(:user) { create(:user) }
  let(:agency) { create(:agency, parent_agency_id: -1, agency_type: 'ParentAgency') }
  let(:marketplace) { create(:marketplace) }
  let!(:deal) { create(:deal, agency:, marketplace:) }
  let!(:budget) { create(:budget, deal:) }
  let(:budget_payload) do
    {
      budgets: [
        {
          budget_id: budget.id,
          current_pricing: 2,
          rate_of_change: 3,
          agency_counter_roc: 4,
          parent_agency_id: 10_001,
          s_actual_prequarter_amount: 5,
          s_actual_quarter4_amount: 6,
          s_actual_quarter1_amount: 7,
          s_actual_quarter2_amount: 8,
          s_actual_quarter3_amount: 9,
          s_actual_postquarter_amount: 10,
          s_projected_prequarter_amount: 11,
          s_projected_quarter4_amount: 12,
          s_projected_quarter1_amount: 13,
          s_projected_quarter2_amount: 14,
          s_projected_quarter3_amount: 15,
          s_projected_postquarter_amount: 16,
          ask_prequarter_amount: 20,
          ask_quarter4_amount: 21,
          ask_quarter1_amount: 22,
          ask_quarter2_amount: 23,
          ask_quarter3_amount: 24,
          ask_postquarter_amount: 25,
          actual_prequarter_amount: 50,
          actual_quarter4_amount: 60,
          actual_quarter1_amount: 70,
          actual_quarter2_amount: 80,
          actual_quarter3_amount: 90,
          actual_postquarter_amount: 100,
          projected_prequarter_amount: 110,
          projected_quarter4_amount: 120,
          projected_quarter1_amount: 130,
          projected_quarter2_amount: 140,
          projected_quarter3_amount: 150,
          projected_postquarter_amount: 160,
          bycal: true
        }
      ]
    }
  end
  let(:batch_update_url) do
    "/api/stealth_mode/budgets/batch_update?agency_id=#{agency.id}" \
      "&budget_year_id=#{budget.budget_year.id}&marketplace_id=#{marketplace.id}"
  end

  before do
    allow_any_instance_of(Api::StealthMode::BudgetsController).to receive(:user_by_sso_id).and_return(user)
  end

  context 'batch_update' do
    context 'invalid paload' do
      it 'returns bad request' do
        post batch_update_url, budgets: [roc: 1]
        expect(last_response.status).to eq(400)
      end
    end

    context 'valid payload' do
      it 'returns 200' do
        post batch_update_url, budget_payload
        expect(last_response.status).to eq(200)
      end
    end
  end
end
