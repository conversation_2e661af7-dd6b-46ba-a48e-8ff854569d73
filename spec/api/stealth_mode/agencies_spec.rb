# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'stealth_mode/agencies', type: :api do
  include ActionDispatch::TestProcess::FixtureFile
  let(:agency) { create(:agency, parent_agency_id: -1, agency_type: 'ParentAgency') }
  let(:marketplace) { create(:marketplace) }
  let(:user) { create(:user) }
  let!(:deal) { create(:deal, agency:, marketplace:) }
  let!(:budget) { create(:budget, deal:) }
  let(:partnership) { create(:partnership, partnership_id: -1) }
  let!(:assignment) do
    create(
      :assignment,
      user:,
      agency:,
      advertiser_id: -1,
      property_id: nil,
      partnership:,
      portal_management: true,
      selling_vertical_id: -1,
      property_type_id: -1,
      product_category_id: -1,
      deal_tag_id: -1,
      division_id: -1,
      subdivision_id: -1,
      pillar_id: -1
    )
  end
  let!(:portal_team) do
    create(:portal_team, user:, budget_year: budget.budget_year, marketplace:, agency:)
  end
  let!(:ag_view) do
    build_stubbed(:agency_gateway_view,
                  marketplace_id: marketplace.id,
                  agency_id: agency.id,
                  current_budget_id: budget.budget_id,
                  budget_year: budget.budget_year)
  end

  before do
    allow_any_instance_of(Api::StealthMode::AgenciesController).to receive(:user_by_sso_id).and_return(user)
    allow(AgencyGatewayView).to receive(:data).and_return([ag_view])
  end

  context 'send_data_to_ag' do
    context 'unauthorized' do
      it 'returns 401' do
        post "/api/stealth_mode/agencies/#{agency.id}/send_data_to_ag?marketplace_id=#{marketplace.id}"
        expect(last_response.status).to eq(401)
      end
    end

    context 'authorized' do
      before(:each) do
        user.user_roles << build(:user_role, user:, role: build(:role, :portal_mgmt_stealth_mode))
      end

      describe 'post /send_data_to_ag' do
        it 'can publish deals data' do
          expect(Pubsub::Publisher).to receive(:deal).with([ag_view].to_json)
          expect(Pubsub::Publisher).to receive(:comments).with(AgencyDealComment.comments_json([budget.id]))
          post "/api/stealth_mode/agencies/#{agency.id}/send_data_to_ag?marketplace_id=#{marketplace.id}"
          expect(last_response.status).to eq(200)
        end
      end

      describe 'post /publish' do
        it 'publishes all agencies regardless of active flag' do
          expect(Pubsub::GenericPublisher).to receive(:Publish).with(:agencies, BaseAgency.all)
          post '/api/stealth_mode/agencies/publish'
          expect(last_response.status).to eq(200)
        end
      end

      describe 'post /upload' do
        before :each do
          retval = { success_count: 1, errors: [] }
          dbl = double('file_upload')
          allow(StealthUpload::FileUpload).to receive(:new).and_return(dbl)
          allow(dbl).to receive(:valid?).and_return(true)
          allow(dbl).to receive(:perform)
          allow(dbl).to receive(:result).and_return(retval)
        end

        context 'valid csv file' do
          it 'returns 200' do
            file = fixture_file_upload('upload.csv', 'text/csv')

            post("/api/stealth_mode/agencies/#{agency.id}/upload", file:)
            expect(last_response.status).to eq(200)
          end
        end

        context 'invalid file' do
          it 'returns 400' do
            file = fixture_file_upload('upload.xml', 'text/xml')

            post("/api/stealth_mode/agencies/#{agency.id}/upload", file:)
            expect(last_response.status).to eq(400)
          end
        end
      end
    end
  end
end
