# frozen_string_literal: true

require 'spec_helper'

RSpec.describe 'registration_types', type: :api do
  context 'publish' do
    let(:user) { create(:user) }
    let!(:registration_type) { create(:registration_type) }

    before do
      allow_any_instance_of(Api::RegistrationTypesController).to receive(:user_by_sso_id).and_return(user)
    end

    context 'unauthorized' do
      it 'returns 401' do
        post '/api/registration_types/publish'
        expect(last_response.status).to eq(401)
      end
    end

    context 'authorized' do
      before(:each) do
        user.user_roles << build(:user_role, user:, role: build(:role, :admin))
      end

      describe 'post /publish' do
        it 'can publish registration_types' do
          expect(Pubsub::GenericPublisher).to receive(:Publish).with(:registration_types, RegistrationType.all)
          post '/api/registration_types/publish'
          expect(last_response.status).to eq(200)
        end
      end
    end
  end
end
