# frozen_string_literal: true

require 'spec_helper'

RSpec.describe 'AorAeAssignments', type: :api do
  describe 'GET /aor_ae_assignments' do
    it 'works' do
      get '/api/aor_ae_assignments'
      expect(last_response.status).to eq(200)
    end

    it 'returns json array of SfdcAorAeAssignments' do
      get '/api/aor_ae_assignments'
      json_response = JSON.parse(last_response.body)
      expect(json_response.is_a?(Array)).to eq(true)
    end

    it 'calls filter for updated in the last_n_hours' do
      expect(SfdcAorAeAssignment).to receive(:last_n_hours).with(24).and_call_original
      get '/api/aor_ae_assignments?last_n_hours=24'

      expect(SfdcAorAeAssignment).to receive(:last_n_hours).with(nil).and_call_original
      get '/api/aor_ae_assignments'
    end
  end
end
