# frozen_string_literal: true

require 'spec_helper'

RSpec.describe 'Deals', type: :api do
  describe 'GET api/deals' do
    let!(:py) { create(:budget_year) }
    let!(:cy) { create(:budget_year, default_budget_year: true, prior_budget_year_id: py.id) }

    before do
      10.times { create(:budget, budget_year: cy) }
      allow_any_instance_of(Api::ApiDealService).to receive(:index).and_return(ApiDeal.all)
    end

    it 'works' do
      get '/api/deals'
      expect(last_response.status).to eq(200)
      expect(JSON.parse(last_response.body).count).to eq(10)
    end

    it 'returns the correct page_size' do
      get 'api/deals?per_page=5'
      expect(JSON.parse(last_response.body).count).to eq(5)

      get 'api/deals?per_page=10'
      expect(JSON.parse(last_response.body).count).to eq(10)
    end

    it 'returns the correct page' do
      get 'api/deals?per_page=5'
      expect(
        JSON.parse(last_response.body).map { |deal| deal['deal_id'] }
      ).to match_array(ApiDeal.first(5).pluck(:deal_id))

      get 'api/deals?per_page=5&page=1'
      expect(
        JSON.parse(last_response.body).map { |deal| deal['deal_id'] }
      ).to match_array(ApiDeal.first(5).pluck(:deal_id))

      get 'api/deals?per_page=5&page=2'
      expect(
        JSON.parse(last_response.body).map { |deal| deal['deal_id'] }
      ).to match_array(ApiDeal.all.to_a.last(5).pluck(:deal_id))

      get 'api/deals?per_page=5&page=3'
      expect(JSON.parse(last_response.body)).to match_array([])
    end

    it 'can filter on content_id' do
      user = create(:user, app_user_id: 111)
      allow_any_instance_of(ApplicationController).to receive(:user_by_sso_id).and_return(user)

      expect(Api::ApiDealService.new(user).send(:attribute_filters)).to include(:content_id)

      expect(Api::ApiDealService).to receive(:new).with(
        user,
        ActionController::Parameters.new(content_id: '4').permit!
      ).and_call_original

      get '/api/deals?content_id=4'
    end
  end

  describe 'GET api/deals/budget_totals' do
    let!(:py) { create(:budget_year) }
    let!(:cy) { create(:budget_year, default_budget_year: true, prior_budget_year_id: py.id) }

    before do
      5.times { create(:budget, budget_year: cy) }
      allow_any_instance_of(Api::ApiDealService).to receive(:base_index).and_return(ApiDeal.all)
    end

    it 'returns correct totals' do
      get 'api/deals/budget_totals'
      expect(JSON.parse(last_response.body).count).to eq(1)
      expect(JSON.parse(last_response.body)[0]['registration_total'])
        .to eq(ApiDeal.all.map(&:registration_total).reduce(:+))
    end
  end

  describe 'GET api/deals/status_counts' do
    let!(:py) { create(:budget_year) }
    let!(:cy) { create(:budget_year, default_budget_year: true, prior_budget_year_id: py.id) }
    let!(:status0) { create(:status) }
    let!(:status1) { create(:status) }

    before do
      2.times { create(:budget, budget_year: cy, status: status0) }
      3.times { create(:budget, budget_year: cy, status: status1) }
      allow_any_instance_of(Api::ApiDealService).to receive(:base_index).and_return(ApiDeal.all)
    end

    it 'returns correct totals' do
      get 'api/deals/status_counts'
      json_response = JSON.parse(last_response.body)
      expect(json_response.find { |count| count['status_name'] == status0.name }['count']).to eq(2)
      expect(json_response.find { |count| count['status_name'] == status1.name }['count']).to eq(3)
    end
  end

  describe 'GET api/deals/:id' do
    let!(:deal) { create(:deal) }

    it 'works' do
      allow_any_instance_of(Api::ApiDealService).to receive(:no_access?).and_return(false)
      get "api/deals/#{deal.id}"
      expect(last_response.status).to eq(200)
    end

    it 'throws error when user does not have access to deal' do
      allow_any_instance_of(Api::ApiDealService).to receive(:no_access?).and_return(true)
      get "api/deals/#{deal.id}"
      expect(last_response.status).to eq(401)
    end
  end

  describe 'create' do
    it 'works without budgets' do
      params = {
        deal_name: 'Pretzel Deal',
        agency_id: create(:agency).id,
        advertiser_id: create(:advertiser).id,
        demographic_id: create(:demographic).id,
        marketplace_id: create(:marketplace).id,
        rating_stream_id: create(:rating_stream).id,
        property_id: create(:property).id
      }

      post 'api/deals', params

      expect(Deal.find_by(deal_name: 'Pretzel Deal')).not_to be_nil
    end

    it 'works with budgets' do
      params = {
        deal_name: 'Kolache Deal',
        agency_id: create(:agency).id,
        advertiser_id: create(:advertiser).id,
        demographic_id: create(:demographic).id,
        marketplace_id: create(:marketplace).id,
        rating_stream_id: create(:rating_stream).id,
        property_id: create(:property).id,
        budgets_attributes: [
          {
            budget_year_id: create(:budget_year).id,
            actual_prequarter_amount: 100_000
          },
          {
            budget_year_id: create(:budget_year).id,
            actual_prequarter_amount: 200_000
          }
        ]
      }

      post 'api/deals', params

      expect(Deal.find_by(deal_name: 'Kolache Deal').budgets.count).to eq(2)
      expect(Deal.find_by(deal_name: 'Kolache Deal').budgets.last.actual_prequarter_amount).to eq(200_000)
    end

    it 'works with budgets and stealth mode budgets' do
      params = {
        deal_name: 'Van Nostrand Deal',
        agency_id: create(:agency).id,
        advertiser_id: create(:advertiser).id,
        demographic_id: create(:demographic).id,
        marketplace_id: create(:marketplace).id,
        rating_stream_id: create(:rating_stream).id,
        registration_type_id: create(:registration_type).id,
        property_id: create(:property).id,
        budgets_attributes: [
          {
            budget_year_id: create(:budget_year).id,
            actual_prequarter_amount: 100_000,
            stealth_mode_budget_attributes: {
              stealth_projected: 5_000
            }
          },
          {
            budget_year_id: create(:budget_year).id,
            actual_prequarter_amount: 200_000,
            stealth_mode_budget_attributes: {
              stealth_projected: 10_000
            }
          }
        ]
      }

      post 'api/deals', params

      expect(Deal.find_by(deal_name: 'Van Nostrand Deal').budgets.flat_map(&:stealth_mode_budget).count).to eq(2)
      expect(
        Deal.find_by(deal_name: 'Van Nostrand Deal').budgets.flat_map(&:stealth_mode_budget).last.stealth_projected
      ).to eq(10_000)
    end
  end

  describe 'batch_create' do
    let(:advertiser) { create(:advertiser) }
    let!(:advertiser_brand) { create(:advertiser_brand, advertiser:) }
    let!(:parent_deal) { create(:parent_deal) }

    it 'requires deals' do
      post 'api/deals/batch_create'
      expect(last_response.status).to eq(400)
    end

    it 'works without budgets' do
      params = {
        deals: [
          {
            deal_name: 'Krispy Kreme Deal',
            agency_id: create(:agency).id,
            advertiser_id: advertiser.id,
            advertiser_brand_id: advertiser_brand.id,
            demographic_id: create(:demographic).id,
            marketplace_id: create(:marketplace).id,
            rating_stream_id: create(:rating_stream).id,
            property_id: create(:property).id
          },
          {
            deal_name: 'Dunkin Donuts Deal',
            agency_id: create(:agency).id,
            advertiser_id: advertiser.id,
            advertiser_brand_id: advertiser_brand.id,
            demographic_id: create(:demographic).id,
            marketplace_id: create(:marketplace).id,
            rating_stream_id: create(:rating_stream).id,
            property_id: create(:property).id
          }
        ]
      }

      post 'api/deals/batch_create', params

      expect(Deal.find_by(deal_name: 'Krispy Kreme Deal')).not_to be_nil
      expect(Deal.find_by(deal_name: 'Dunkin Donuts Deal')).not_to be_nil

      expect(Deal.find_by(deal_name: 'Krispy Kreme Deal').advertiser_brand_id).to eq(advertiser_brand.id)
      expect(Deal.find_by(deal_name: 'Dunkin Donuts Deal').advertiser_brand_id).to eq(advertiser_brand.id)
    end

    it 'assigns a parent deal' do
      params = {
        deals: [
          {
            deal_name: 'Krispy Kreme Deal',
            agency_id: create(:agency).id,
            advertiser_id: advertiser.id,
            advertiser_brand_id: advertiser_brand.id,
            demographic_id: create(:demographic).id,
            marketplace_id: create(:marketplace).id,
            rating_stream_id: create(:rating_stream).id,
            property_id: create(:property).id,
            parent_deal_id: -1,
            parent_deal_name: 'Krispy Kreme Parent',
            parent_deal_type_id: create(:parent_deal_type).id
          }
        ]
      }

      post 'api/deals/batch_create', params

      expect(Deal.find_by(deal_name: 'Krispy Kreme Deal').parent_deal).to eq(ParentDeal.last)
      expect(ParentDeal.last.parent_deal_type_id).to eq(params[:deals].first[:parent_deal_type_id])
    end

    it 'works with budgets' do
      params = {
        deals: [
          {
            deal_name: 'Krispy Kreme Deal',
            agency_id: create(:agency).id,
            advertiser_id: create(:advertiser).id,
            demographic_id: create(:demographic).id,
            marketplace_id: create(:marketplace).id,
            rating_stream_id: create(:rating_stream).id,
            property_id: create(:property).id,
            budgets_attributes: [
              {
                budget_year_id: create(:budget_year).id,
                actual_prequarter_amount: 100_000
              },
              {
                budget_year_id: create(:budget_year).id,
                actual_prequarter_amount: 200_000
              }
            ]
          },
          {
            deal_name: 'Dunkin Donuts Deal',
            agency_id: create(:agency).id,
            advertiser_id: create(:advertiser).id,
            demographic_id: create(:demographic).id,
            marketplace_id: create(:marketplace).id,
            rating_stream_id: create(:rating_stream).id,
            property_id: create(:property).id,
            budgets_attributes: [
              {
                budget_year_id: create(:budget_year).id,
                actual_prequarter_amount: 300_000
              },
              {
                budget_year_id: create(:budget_year).id,
                actual_prequarter_amount: 400_000
              },
              {
                budget_year_id: create(:budget_year).id,
                actual_prequarter_amount: 500_000
              }
            ]
          }
        ]
      }

      post 'api/deals/batch_create', params

      expect(Deal.find_by(deal_name: 'Krispy Kreme Deal').budgets.count).to eq(2)
      expect(Deal.find_by(deal_name: 'Dunkin Donuts Deal').budgets.count).to eq(3)
      expect(Deal.find_by(deal_name: 'Krispy Kreme Deal').budgets.last.actual_prequarter_amount).to eq(200_000)
      expect(Deal.find_by(deal_name: 'Dunkin Donuts Deal').budgets.last.actual_prequarter_amount).to eq(500_000)
    end
  end

  describe 'batch_new' do
    let(:agency) { create(:agency) }
    let(:advertiser) { create(:advertiser) }
    let(:demographic) { create(:demographic) }
    let(:marketplace) { create(:marketplace) }
    let(:rating_stream) { create(:rating_stream) }
    let(:budget_year) { create(:budget_year) }
    let(:property) { create(:property) }
    let!(:user) { create(:user, first_name: 'AE', last_name: 'User') }

    let(:deal_params) do
      {
        deals: [{
          agency_id: agency.id,
          advertiser_id: advertiser.id,
          demographic_id: demographic.id,
          marketplace_id: marketplace.id,
          rating_stream_id: rating_stream.id,
          budget_year: budget_year.fall_year,
          properties: [
            {
              property_id: property.id,
              deal_name: 'Test Deal 1'
            }
          ]
        }]
      }
    end

    describe 'post /batch_new' do
      it 'can create new deals' do
        post 'api/deals/batch_new', deal_params
        expect(Deal.count).to eq(1)
        deal = Deal.first
        expect(deal.deal_name).to eq('Test Deal 1')
        expect(deal.agency).to eq(agency)
        expect(deal.advertiser).to eq(advertiser)
        expect(deal.demographic).to eq(demographic)
        expect(deal.marketplace).to eq(marketplace)
        expect(deal.rating_stream).to eq(rating_stream)
        expect(deal.property).to eq(property)
      end
    end
  end

  describe 'update' do
    it 'works' do
      deal = create(:deal, deal_name: 'Original Glazed Donut')
      expect(deal.deal_name).to eq('Original Glazed Donut')
      patch "api/deals/#{deal.id}", deal_name: 'Chocolate Donut'
      expect(Deal.find(deal.id).deal_name).to eq('Chocolate Donut')
    end
  end

  describe 'post /batch_update' do
    let!(:deal_1) { create(:deal, deal_name: 'Pretzel', bycal: true) }
    let!(:deal_2) { create(:deal, deal_name: 'Donut', bycal: true) }
    let!(:deal_3) { create(:deal, deal_name: 'Kolache', bycal: false) }
    let!(:deal_params) do
      {
        deals: [
          {
            deal_id: deal_1.deal_id,
            deal_name: 'Pretzel 2',
            bycal: false
          },
          {
            deal_id: deal_3.deal_id,
            bycal: true
          }
        ]
      }
    end

    it 'updates batches of deals with the correct values' do
      post 'api/deals/batch_update', deal_params
      deals = Deal.all
      results = [deals[0].bycal, deals[1].bycal, deals[2].bycal]
      expect(results).to match_array([false, true, true])
      expect(Deal.find(deal_1.id).deal_name).to eq('Pretzel 2')
    end

    it 'can update advertiser_brand field' do
      advertiser_brand1 = create(:advertiser_brand, advertiser: deal_1.advertiser)
      advertiser_brand2 = create(:advertiser_brand, advertiser: deal_2.advertiser)
      advertiser_brand3 = create(:advertiser_brand, advertiser: deal_3.advertiser)

      brand_update_params = {
        deals: [
          {
            deal_id: deal_1.deal_id,
            advertiser_brand_id: advertiser_brand1.id
          },
          {
            deal_id: deal_2.deal_id,
            advertiser_brand_id: advertiser_brand2.id
          },
          {
            deal_id: deal_3.deal_id,
            advertiser_brand_id: advertiser_brand3.id
          }
        ]
      }
      post 'api/deals/batch_update', brand_update_params
      deals = Deal.all
      results = [deals[0].advertiser_brand_id, deals[1].advertiser_brand_id, deals[2].advertiser_brand_id]
      expect(results).to match_array([advertiser_brand1.id, advertiser_brand2.id, advertiser_brand3.id])
    end
  end

  describe 'publish' do
    let(:user) { create(:user) }
    let(:deal) { create(:deal) }
    let(:demographic) { create(:demographic) }
    let(:vertical) { create(:vertical) }

    let(:publish_params) do
      {
        deal_ids: [deal.id],
        attributes: { demographic_id: demographic.id, vertical_id: vertical.id }
      }
    end

    let(:large_deal_set_params) do
      {
        deal_ids: (1..1100).to_a,
        attributes: {}
      }
    end

    before do
      allow_any_instance_of(Api::DealsController).to receive(:user_by_sso_id).and_return(user)
    end

    describe 'post /publish' do
      it 'can publish deals data' do
        dc = DealChanged.new(Deal.find(deal.id), publish_params[:attributes])
        expect(Pubsub::Publisher).to receive(:deal_headers).with(dc.ag_deals.to_json)
        expect(Pubsub::Publisher).to receive(:deal_delete).with(dc.marked_for_deletion.to_json)
        post '/api/deals/publish', publish_params
        expect(last_response.status).to eq(200)
      end

      context 'with params' do
        before(:each) do
          allow(Pubsub::Publisher).to receive(:deal_headers)
          allow(Pubsub::Publisher).to receive(:deal_delete)
        end

        it 'can accept more than 1000 deal_ids' do
          post '/api/deals/publish', large_deal_set_params
          expect(last_response.status).to eq(200)
        end

        it 'works without deals_ids' do
          post '/api/deals/publish'
          expect(last_response.status).to eq(200)
        end

        it 'can accept an empty deal_ids array' do
          post '/api/deals/publish', { deal_ids: [] }
          expect(last_response.status).to eq(200)
        end
      end
    end
  end

  describe 'destroy' do
    before(:each) do
      @deal = create(:deal)
    end

    it 'returns 200 when succeeds' do
      delete "api/deals/#{@deal.id}"
      expect(last_response.status).to eq(200)
    end

    it 'returns correct error code when fails due to constraint conflict' do
      create(:budget, deal: @deal)
      delete "api/deals/#{@deal.id}"
      expect(last_response.status).to eq(409)
    end
  end

  describe 'linked_deals' do
    let!(:user) { create(:user) }
    let!(:budget_year) { create(:budget_year) }
    let!(:marketplace) { create(:marketplace) }

    let!(:given_deal) { create(:deal, marketplace:) }
    let!(:budget_1) { create(:budget, deal: given_deal, budget_year:) }

    let!(:deal_2) { create(:deal, marketplace:) }
    let!(:budget_2) { create(:budget, deal: deal_2, budget_year:) }
    let!(:deal_3) { create(:deal, marketplace:) }
    let!(:budget_3) { create(:budget, deal: deal_3, budget_year:) }
    let!(:deal_4) { create(:deal, marketplace:) }
    let!(:budget_4) { create(:budget, deal: deal_4, budget_year:) }

    let!(:deal_link_1) { create(:deal_link) }
    let!(:deal_link_2) { create(:deal_link) }

    # first group
    let!(:deal_link_association) { create(:deal_link_association, deal: given_deal, deal_link: deal_link_1) }
    let!(:deal_link_association_2) { create(:deal_link_association, deal: deal_2, deal_link: deal_link_1) }
    let!(:deal_link_association_3) { create(:deal_link_association, deal: deal_3, deal_link: deal_link_1) }

    # second group
    let!(:deal_link_association_4) { create(:deal_link_association, deal: deal_4, deal_link: deal_link_2) }

    it 'returns deals that are linked to the given deal' do
      expect_any_instance_of(Api::DealsController).to receive(:servicer).with(
        deal_id: Deal.linked_deals(given_deal.id).select(:deal_id),
        budget_year_id: budget_year.id.to_s,
        marketplace_id: marketplace.id.to_s
      ).and_call_original

      get "api/deals/#{given_deal.id}/linked_deals?marketplace_id=#{marketplace.id}&budget_year_id=#{budget_year.id}"
      expect(last_response.status).to eq(200)

      linked_deals = Deal.linked_deals(given_deal.id)
      expect(linked_deals.map(&:deal_id)).to eq([given_deal.id, deal_2.id, deal_3.id])
    end
  end
end
