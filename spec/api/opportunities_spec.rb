# frozen_string_literal: true

require 'spec_helper'

RSpec.describe 'Opportunities', type: :api do
  describe 'GET /api_v2_opportunities' do
    it 'works' do
      get '/api/opportunities'
      expect(last_response.status).to eq(200)
    end

    it 'returns json array of Opportunities' do
      get '/api/opportunities'
      json_response = JSON.parse(last_response.body)
      expect(json_response.is_a?(Array)).to eq(true)
    end

    it 'returns only linear Opportunities' do
      @user = create(:user, sso_id: 123)
      allow_any_instance_of(ApplicationController).to receive(:user_by_sso_id).and_return(@user)

      quarter = create(:quarter)

      linear_property_type = create(:property_type, :linear, property_type_id: 1)
      linear_property = create(:property, property_type: linear_property_type)

      non_linear_pacing_budget_detail = create(:pacing_budget_detail_ii, buying_ae: @user, quarter:)
      linear_pacing_budget_detail = create(:pacing_budget_detail_ii, property: linear_property, buying_ae: @user,
                                                                     quarter:)

      opportunity_status = create(:opportunity_status, count_as_projected_finish: 1)

      create_list(:opportunity,
                  6,
                  pacing_budget_id: non_linear_pacing_budget_detail.pacing_budget_id,
                  opportunity_status:) # non linear

      create_list(:opportunity,
                  4,
                  pacing_budget_id: linear_pacing_budget_detail.pacing_budget_id,
                  opportunity_status:) # linear

      Opportunity.all.each do |opportunity|
        create(:opportunity_detail, quarter:, opportunity:)
      end

      get '/api/opportunities'
      json_response = JSON.parse(last_response.body)

      expect(Opportunity.all.count).to eq(10)
      expect(json_response.count).to eq(4)
    end
  end

  context 'create' do
    let!(:finance_quarter) { create(:finance_quarter) }
    let!(:quarter_date) do
      create(:quarter_date,
             finance_quarter:,
             quarter_start_date: Date.today,
             quarter_end_date: Date.tomorrow)
    end

    let!(:marketplace) { create(:marketplace) }
    let!(:demographic) { create(:demographic) }
    let!(:pacing_budget_detail) { create(:pacing_budget_detail_ii, quarter: finance_quarter.quarter) }
    let!(:opportunity_status) { create(:opportunity_status) }
    let!(:not_returning_reason) { create(:not_returning_reason) }

    let!(:data) do
      {
        opportunity_name: 'Opportunity 1',
        marketplace_id: marketplace.marketplace_id,
        demographic_id: demographic.demographic_id,
        opportunity_status_id: opportunity_status.opportunity_status_id,
        opportunity_dollars: 300,
        pacing_budget_detail_id: pacing_budget_detail.id,
        sales_system_id: 1234,
        opportunity_comment: 'hello',
        not_returning_reason_id: not_returning_reason.id,
        not_returning_custom_reason: 'on hold',
        flight_start: Date.today,
        flight_end: Date.tomorrow
      }
    end

    describe 'POST api/opportunities/', type: :api do
      it 'can create a new opportunity' do
        allow_any_instance_of(Opportunity).to receive(:user).and_return(create(:user))

        post 'api/opportunities', data
        expect(Opportunity.count).to eq(1)

        opportunity = Opportunity.first
        expect(opportunity.opportunity_name).to eq('Opportunity 1')
        expect(opportunity.marketplace).to eq(marketplace)
        expect(opportunity.demographic).to eq(demographic)
        expect(opportunity.opportunity_status).to eq(opportunity_status)
        expect(opportunity.sales_system_id).to eq(1234)
        expect(opportunity.not_returning_reason_id).to eq(not_returning_reason.id)
        expect(opportunity.not_returning_custom_reason).to eq('on hold')

        expect(opportunity.opportunity_details.count).to eq(1)

        opportunity_detail = opportunity.opportunity_details.first
        expect(opportunity_detail.opportunity_dollars).to eq(300)
        expect(opportunity_detail.flight_start).to eq(Date.today)
        expect(opportunity_detail.flight_end).to eq(Date.tomorrow)
      end

      it 'can use column defaults if non-required params not passed' do
        allow_any_instance_of(Opportunity).to receive(:user).and_return(create(:user))
        post 'api/opportunities', data.except!(:not_returning_reason_id, :demographic_id)

        expect(Opportunity.count).to eq(1)

        opportunity = Opportunity.first
        expect(opportunity.not_returning_reason_id).to eq(-1)
        expect(opportunity.demographic_id).to eq(-1)
      end

      it 'can find pacing_budget_id from pacing_budget_detail' do
        allow_any_instance_of(Opportunity).to receive(:user).and_return(create(:user))
        post 'api/opportunities', data

        expect(Opportunity.count).to eq(1)

        opportunity = Opportunity.first
        pacing_budget_id = PacingBudgetDetail.find(data[:pacing_budget_detail_id])&.pacing_budget_id

        expect(opportunity.pacing_budget_id).to eq(pacing_budget_id)
      end

      it 'creates an OpportunityComment when passed opportunity_comment param' do
        allow_any_instance_of(Opportunity).to receive(:user).and_return(create(:user))
        post 'api/opportunities', data
        expect(OpportunityComment.all.count).to eq(1)
        expect(OpportunityComment.first.message).to eq('hello')
      end

      it 'returns 400 if missing required params' do
        post 'api/opportunities', opportunity_name: 'Test Opportunity'
        expect(last_response.status).to eq(400)
      end
    end
  end

  context 'update' do
    let!(:pacing_budget_detail) { create(:pacing_budget_detail_ii) }
    let!(:opportunity) do
      create(:opportunity, opportunity_status_id: 2, opportunity_id: 123,
                           pacing_budget_id: pacing_budget_detail.pacing_budget_id)
    end
    let!(:opportunity_detail) { create(:opportunity_detail, opportunity_detail_id: 456, opportunity:) }

    describe 'PUT api/opportunities/:id', type: :api do
      it 'can update the available fields' do
        put '/api/opportunities/123?opportunity_status_id=4&opportunity_dollars=12305' \
            '&sales_system_id=-1&not_returning_reason_id=2&not_returning_custom_reason=on%20hold' \
            '&opportunity_detail_id=456'
        updated_opportunity = Opportunity.find(123)
        updated_opportunity_detail = OpportunityDetail.find(456)

        expect(updated_opportunity.opportunity_status_id).to eq(4)
        expect(updated_opportunity.sales_system_id).to eq(-1)
        expect(updated_opportunity.not_returning_reason_id).to eq(2)
        expect(updated_opportunity.not_returning_custom_reason).to eq('on hold')

        expect(updated_opportunity_detail.opportunity_dollars).to eq(12_305)
      end

      it 'requires the opportunity_detail_id param' do
        put '/api/opportunities/123?opportunity_status_id=4&opportunity_dollars=12305' \
            '&sales_system_id=-1&not_returning_reason_id=2&not_returning_custom_reason=on%20hold'
        expect(last_response.status).to eq(400)
      end
    end
  end
end
