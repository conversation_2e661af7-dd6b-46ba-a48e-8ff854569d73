# frozen_string_literal: true

require 'spec_helper'

RSpec.describe 'deal_placeholders', type: :api do
  before do
    3.times { create(:deal_placeholder, active: true) }
    3.times { create(:deal_placeholder, active: false) }
  end

  describe 'GET' do
    it 'returns success status' do
      get '/api/deal_placeholders'
      expect(last_response.status).to eq(200)
    end

    it 'returns data' do
      get '/api/deal_placeholders'
      json_response = JSON.parse(last_response.body)
      expect(json_response.is_a?(Array)).to eq(true)
      expect(json_response.count).to eq(6)
    end

    it 'applies filter' do
      get '/api/deal_placeholders', active: 1
      json_response = JSON.parse(last_response.body)
      expect(json_response.count).to eq(3)
    end
  end
end
