# frozen_string_literal: true

require 'spec_helper'

RSpec.describe 'finance_uploads', type: :api do
  describe 'index' do
    before do
      user = create(:user)
      allow_any_instance_of(Api::FinanceUploadsController).to receive(:user_by_sso_id).and_return(user)
      5.times { create(:finance_upload, user:, status: 'ok') }
      2.times { create(:finance_upload, user:, status: 'bad_request') }
    end

    it 'succeeds' do
      get '/api/finance_uploads'
      expect(last_response.status).to eq(200)
      expect(JSON.parse(last_response.body).is_a?(Array)).to eq(true)
      expect(JSON.parse(last_response.body).count).to eq(7)
    end

    it 'filters by status' do
      get '/api/finance_uploads?status=ok'
      expect(last_response.status).to eq(200)
      expect(JSON.parse(last_response.body).count).to eq(5)
    end

    it 'limits returned results' do
      get '/api/finance_uploads?limit=2'
      expect(last_response.status).to eq(200)
      expect(JSON.parse(last_response.body).count).to eq(2)
    end
  end
end
