# frozen_string_literal: true

require 'spec_helper'

RSpec.describe 'assignments', type: :api do
  let(:advertiser) { create(:advertiser) }
  let(:property) { create(:property) }
  let(:agency) { create(:agency) }
  let(:selling_vertical) { create(:selling_vertical) }
  let(:property_type) { create(:property_type) }

  let(:response_body) { JSON.parse(last_response.body) }
  let!(:user) { create(:user) }

  describe '#index' do
    it 'succeeds with valid user_id' do
      get "/api/users/#{user.app_user_id}"
      expect(last_response.status).to eq(200)
    end

    it 'lists user assignments by passed type' do
      assignments = create_list(
        :assignment, 2, user:, aor: true
      )
      create(:assignment, user:, aor: false)

      get "/api/users/#{user.app_user_id}/assignments?type=aor"

      expect(last_response.status).to eq(200)
      expect(response_body.map { |u| u['assignment_id'] })
        .to eq(assignments.map(&:assignment_id))
    end
  end

  describe '#create' do
    it 'creates an assignment on the given user' do
      expect(Assignment.count).to eq(0)

      create_payload = {
        property_id: property.id,
        agency_id: agency.id,
        advertiser_id: advertiser.id,
        selling_vertical_id: selling_vertical.id,
        property_type_id: property_type.id
      }

      post "/api/users/#{user.app_user_id}/assignments", create_payload

      expect(user.assignments.count).to eq(1)
      expect(user.assignments.first.property_id).to eq(property.id)
      expect(user.assignments.first.agency_id).to eq(agency.id)
      expect(user.assignments.first.advertiser_id).to eq(advertiser.id)
      expect(user.assignments.first.selling_vertical_id).to eq(selling_vertical.id)
      expect(user.assignments.first.property_type_id).to eq(property_type.id)
    end
  end

  describe '#update' do
    it 'updates the passed fields on an assignment' do
      assignment = create(:assignment)
      expect(Assignment.count).to eq(1)

      update_payload = {
        property_id: property.id,
        agency_id: agency.id,
        advertiser_id: advertiser.id,
        selling_vertical_id: selling_vertical.id,
        property_type_id: property_type.id
      }

      patch "/api/assignments/#{assignment.id}", update_payload

      expect(Assignment.first.property_id).to eq(property.id)
      expect(Assignment.first.agency_id).to eq(agency.id)
      expect(Assignment.first.advertiser_id).to eq(advertiser.id)
      expect(Assignment.first.selling_vertical_id).to eq(selling_vertical.id)
      expect(Assignment.first.property_type_id).to eq(property_type.id)

      expect(last_response.status).to eq(200)
    end
  end

  describe '#destroy' do
    it 'deletes an assignment by id' do
      assignment = create(:assignment)
      expect(Assignment.count).to eq(1)
      delete "/api/assignments/#{assignment.id}"
      expect(user.assignments.count).to eq(0)
      expect(last_response.status).to eq(200)
    end
  end
end
