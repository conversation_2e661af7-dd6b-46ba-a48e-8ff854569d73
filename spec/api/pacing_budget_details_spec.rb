# frozen_string_literal: true

require 'spec_helper'

RSpec.describe 'Pacing Budget Details', type: :api do
  let!(:advertiser) { create(:advertiser, name: 'PAM Advertiser') }
  let!(:advertiser2) { create(:advertiser, name: 'PAM Advertiser 2') }

  let!(:property_type) { create(:property_type, name: 'PAM Property Type') }

  let!(:property) { create(:property, name: 'PAM Property', selling_vertical:) }
  let!(:property2) do
    create(:property, name: 'PAM Property 2', property_type:, selling_vertical:)
  end
  let!(:property3) { create(:property, name: 'PAM Property 3', selling_vertical:) }

  let!(:selling_vertical) { create(:selling_vertical, name: 'PAM Selling Vertical') }
  let!(:calendar_year) { create(:calendar_year, calendar_year: 'PAM Calendar Year', default_calendar_year: true) }

  let!(:pacing_budget_detail) do
    create(:pacing_budget_detail_ii, advertiser:, property:, calendar_year:)
  end
  let!(:pacing_budget_detail2) do
    create(:pacing_budget_detail_ii, advertiser:, property: property3, calendar_year:)
  end
  let!(:pacing_budget_detail3) do
    create(:pacing_budget_detail_ii, advertiser: advertiser2, property:, calendar_year:)
  end
  let!(:pacing_budget_detail4) do
    create(:pacing_budget_detail_ii, advertiser:, property: property2, calendar_year:)
  end

  let!(:external_sfdc_system) { create(:external_system, name: 'SFDC') }

  let!(:inbound_sys_key_asc_type) { create(:system_key_asc_type, system_key_asc_type_name: 'INBOUND') }
  let!(:outbound_sys_key_asc_type) { create(:system_key_asc_type, system_key_asc_type_name: 'OUTBOUND') }
  let!(:combined_sys_key_asc_type) { create(:system_key_asc_type, system_key_asc_type_name: 'COMBINED') }

  let!(:advertiser_key_type) do
    create(:external_key_type, external_key_type_id: 1, external_key_type_name: 'Advertiser')
  end
  let!(:property_key_type) do
    create(:external_key_type, external_key_type_id: 4, external_key_type_name: 'Property')
  end
  let!(:property_type_key_type) do
    create(:external_key_type, external_key_type_id: 64, external_key_type_name: 'Property Type')
  end
  let!(:selling_vertical_key_type) do
    create(:external_key_type, external_key_type_id: 44, external_key_type_name: 'Selling Vertical')
  end

  let!(:advertiser_association) do
    create(:system_key_association,
           external_key_type: advertiser_key_type,
           external_system: external_sfdc_system,
           pam_key: advertiser.id,
           external_system_key: 'sfdcadvertiser',
           system_key_asc_type: inbound_sys_key_asc_type)
  end
  let!(:outbound_association) do
    create(:system_key_association,
           external_key_type: advertiser_key_type,
           external_system: external_sfdc_system,
           pam_key: advertiser2.id,
           external_system_key: 'sfdcadvertiser',
           system_key_asc_type: outbound_sys_key_asc_type)
  end

  let(:returned_pacing_budget_ids) { JSON.parse(last_response.body).map { |p| p['pacing_budget_id'] } }

  describe 'GET /pacing_budget_details' do
    it 'requires the appropriate params' do
      get '/api/pacing_budget_details'
      expect(last_response.status).to eq(400)
    end

    context 'with selling_vertical mapping' do
      let!(:selling_vertical_association) do
        create(:system_key_association,
               external_key_type: selling_vertical_key_type,
               external_system: external_sfdc_system,
               pam_key: selling_vertical.id,
               external_system_key: 'sfdcsellingvertical',
               system_key_asc_type: combined_sys_key_asc_type)
      end

      it 'finds by selling_vertical' do
        get '/api/pacing_budget_details' \
            '?sfdc_advertiser_id=sfdcadvertiser' \
            '&sfdc_property_id=sfdcproperty' \
            '&sfdc_property_type_id=sfdcpropertytype' \
            '&sfdc_selling_vertical_id=sfdcsellingvertical'
        expect(returned_pacing_budget_ids).to include(pacing_budget_detail.pacing_budget_id,
                                                      pacing_budget_detail2.pacing_budget_id,
                                                      pacing_budget_detail4.pacing_budget_id)
        expect(returned_pacing_budget_ids).to_not include(pacing_budget_detail3.pacing_budget_id)
      end

      context 'with selling_vertical and property_type mapping' do
        let!(:property_type_association) do
          create(:system_key_association,
                 external_key_type: property_type_key_type,
                 external_system: external_sfdc_system,
                 pam_key: property_type.id,
                 external_system_key: 'sfdcpropertytype',
                 system_key_asc_type: combined_sys_key_asc_type)
        end

        it 'finds by property_type' do
          get '/api/pacing_budget_details?sfdc_advertiser_id=sfdcadvertiser' \
              '&sfdc_property_id=sfdcproperty' \
              '&sfdc_property_type_id=sfdcpropertytype' \
              '&sfdc_selling_vertical_id=sfdcsellingvertical'
          expect(returned_pacing_budget_ids).to include(pacing_budget_detail4.pacing_budget_id)
          expect(returned_pacing_budget_ids).to_not include(pacing_budget_detail3.pacing_budget_id)
        end

        context 'with selling_vertical property_type and property mapping' do
          it 'works with the required params' do
            get '/api/pacing_budget_details?sfdc_advertiser_id=sfdcadvertiser' \
                '&sfdc_property_id=sfdcproperty' \
                '&sfdc_property_type_id=sfdcpropertytype' \
                '&sfdc_selling_vertical_id=sfdcsellingvertical'
            expect(last_response.status).to eq(200)
          end

          it 'returns json array of Pacing Budget Details' do
            get '/api/pacing_budget_details?sfdc_advertiser_id=sfdcadvertiser' \
                '&sfdc_property_id=sfdcproperty' \
                '&sfdc_property_type_id=sfdcpropertytype' \
                '&sfdc_selling_vertical_id=sfdcsellingvertical'
            expect(JSON.parse(last_response.body).is_a?(Array)).to eq(true)
          end

          let!(:property_association) do
            create(
              :system_key_association,
              external_key_type: property_key_type,
              external_system: external_sfdc_system,
              pam_key: property2.id,
              external_system_key: 'sfdcproperty',
              system_key_asc_type: combined_sys_key_asc_type
            )
          end

          it 'filters on mapped params' do
            get '/api/pacing_budget_details?sfdc_advertiser_id=sfdcadvertiser' \
                '&sfdc_property_id=sfdcproperty' \
                '&sfdc_property_type_id=sfdcpropertytype' \
                '&sfdc_selling_vertical_id=sfdcsellingvertical'
            expect(returned_pacing_budget_ids).to eq([pacing_budget_detail4.pacing_budget_id])
          end

          it 'only includes system_key_associations with inbound and combined types' do
            get '/api/pacing_budget_details?sfdc_advertiser_id=sfdcadvertiser' \
                '&sfdc_property_id=sfdcproperty' \
                '&sfdc_property_type_id=sfdcpropertytype' \
                '&sfdc_selling_vertical_id=sfdcsellingvertical'
            expect(returned_pacing_budget_ids).to eq([pacing_budget_detail4.pacing_budget_id])
            expect(returned_pacing_budget_ids).to_not include(pacing_budget_detail3.pacing_budget_id)
          end
        end
      end
    end
  end
end
