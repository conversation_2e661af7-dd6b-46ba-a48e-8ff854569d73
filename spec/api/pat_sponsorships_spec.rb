# frozen_string_literal: true

require 'spec_helper'

RSpec.describe 'PatSponsorships', type: :api do
  let(:controller) { Api::PatSponsorshipsController }

  before(:each) do
    @user = create(:user)
    @current_budget_year = create(:budget_year, default_budget_year: true)
    @budget = create(:budget, budget_year: @current_budget_year)
    allow_any_instance_of(ApplicationController).to receive(:user_by_sso_id).and_return(@user)
  end

  describe 'GET /api_v2_pat_sponsorships' do
    before(:each) do
      create(:pat_sponsorship, budget: @budget)
      create(:pat_sponsorship, budget: create(:budget, budget_year: @current_budget_year))
    end

    it 'works' do
      get '/api/pat_sponsorships?smuser=123'
      json_response = JSON.parse(last_response.body)

      expect(last_response.status).to eq(200)
      expect(json_response.is_a?(Array)).to eq(true)
      expect(json_response.count).to eq(2)
    end

    it 'defaults to current budget_year' do
      create(:pat_sponsorship)
      get '/api/pat_sponsorships?smuser=123'
      json_response = JSON.parse(last_response.body)

      expect(json_response.count).to eq(2)
    end

    it 'filters by deal_id' do
      get "/api/pat_sponsorships?smuser=123&deal_id=#{@budget.deal.id}"
      json_response = JSON.parse(last_response.body)

      expect(json_response.count).to eq(1)
      expect(json_response.first['deal_id']).to eq(@budget.deal.id)
    end

    it 'filters by budget_id' do
      get "/api/pat_sponsorships?smuser=123&budget_id=#{@budget.id}"
      json_response = JSON.parse(last_response.body)

      expect(json_response.count).to eq(1)
      expect(json_response.first['budget_id']).to eq(@budget.id)
    end
  end

  describe 'POST /api_v2_pat_sponsorships' do
    it 'returns 200 when successfully saved' do
      post "/api/pat_sponsorships?smuser=123&budget_id=#{@budget.id}&pitch_id=1&pitch_name=Donuts"
      expect(last_response.status).to eq(201)
    end

    it 'returns 400 if missing required params' do
      post "/api/pat_sponsorships?smuser=123&budget_id=#{@budget.id}"
      expect(last_response.status).to eq(400)
      post '/api/pat_sponsorships?smuser=123&pitch_id=123'
      expect(last_response.status).to eq(400)
    end

    it 'successfully creates new record' do
      count = PatSponsorship.count
      post "/api/pat_sponsorships?smuser=123&budget_id=#{@budget.id}&pitch_id=1&pitch_name=Donuts"
      expect(PatSponsorship.count).to eq(count + 1)
    end
  end

  describe 'DELETE /api_v2_pat_sponsorships' do
    before(:each) do
      @pat_sponsorship = create(:pat_sponsorship, budget: @budget)
    end

    it 'works' do
      delete "/api/pat_sponsorships/#{@pat_sponsorship.id}?smuser=123"

      expect(last_response.status).to eq(200)
      expect(PatSponsorship.count).to eq(0)
    end
  end
end
