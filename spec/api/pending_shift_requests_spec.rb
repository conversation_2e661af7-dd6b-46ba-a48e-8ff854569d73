# frozen_string_literal: true

require 'spec_helper'

RSpec.describe 'PendingShiftRequests', type: :api do
  let(:controller) { Api::PendingShiftRequestsController }

  before(:each) do
    @user = create(:user)
    allow_any_instance_of(ApplicationController).to receive(:user_by_sso_id).and_return(@user)
  end

  describe 'GET /api_v2_pending_shift_requests' do
    it 'works' do
      get '/api/pending_shift_requests'
      expect(last_response.status).to eq(200)
    end

    it 'returns json array of PendingShiftRequests' do
      get '/api/pending_shift_requests'
      json_response = JSON.parse(last_response.body)
      expect(json_response.is_a?(Array)).to eq(true)
    end
  end

  describe 'PUT /api/pending_shift_requests/:id' do
    context 'shift request doesnt exist' do
      it 'throws error' do
        put 'api/pending_shift_requests/0?smuser=123&status=rejected&comment=comment'
        expect(last_response.status).to eq(422)
      end
    end

    context 'shift request exists' do
      before(:each) do
        @shift_request = create(:shift_request)
        allow_any_instance_of(Api::PendingShiftRequestsController).to receive(:shift_request).and_return(@shift_request)
      end

      context 'current shift_request_status isnt pending' do
        before(:each) do
          allow_any_instance_of(controller).to receive(:pending?).and_return(false)
        end

        it 'throws error' do
          put "api/pending_shift_requests/#{@shift_request.id}?smuser=123&status=rejected&comment=comment"
          expect(last_response.status).to eq(422)
        end
      end

      context 'current shift_request_status is pending' do
        before(:each) do
          allow_any_instance_of(controller).to receive(:pending?).and_return(true)
          allow(@user).to receive(:account_executive).and_return(true)
          stub_request(:post,
                       "http://#{ApiHelper::SMS_API_HOST}/api/shift_requests/#{@shift_request.id}/" \
                       'send_status_change_emails?access_token=abf76e0980e5109b9a3b53c073272593ee629eee')
            .to_return(status: 200)
        end

        it 'throws error if status param isnt valid' do
          put "api/pending_shift_requests/#{@shift_request.id}?smuser=123&status=donuts"
          expect(last_response.status).to eq(400)
        end

        context '?status=rejected' do
          it 'throws error if no comment is present' do
            put "api/pending_shift_requests/#{@shift_request.id}?smuser=123&status=rejected"
            expect(last_response.status).to eq(400)
          end

          it 'attempts to reject' do
            expect_any_instance_of(Api::PendingShiftRequestService).to receive(:reject).once
            put "api/pending_shift_requests/#{@shift_request.id}?smuser=123&status=rejected&comment=comment"
          end
        end

        context '?status=approved' do
          it 'does not throw error if no comment is present' do
            put "api/pending_shift_requests/#{@shift_request.id}?smuser=123&status=approved"
            expect(last_response.status).to eq(200)
          end

          it 'attempts to approve' do
            expect_any_instance_of(Api::PendingShiftRequestService).to receive(:approve).once
            put "api/pending_shift_requests/#{@shift_request.id}?smuser=123&status=approved&comment=comment"
          end
        end
      end
    end
  end
end
