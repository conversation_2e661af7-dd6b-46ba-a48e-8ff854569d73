# frozen_string_literal: true

require 'spec_helper'

RSpec.describe 'finance/upload', type: :api do
  xdescribe 'create' do
    let!(:upload_file) do
      Rack::Test::UploadedFile.new(Rails.root + 'spec/api/finance/sample_upload.xlsx',
                                   'multipart/form-data')
    end
    let(:expected_data) do
      [
        {
          sheet_name: 'Sheet1',
          sheet_headers: %i[header_1 header_2 header_3 header_4 errors],
          sheet_data: [
            { header_1: 'Glazed', header_2: 'Donut', header_3: 123, header_4: nil },
            { header_1: 'Cinnamon', header_2: 'Bagel', header_3: 99.5, header_4: 0.099 }
          ]
        },
        {
          sheet_name: 'Sheet2',
          sheet_headers: %i[header_4 header_5 errors],
          sheet_data: [
            { header_4: 'Pretzel', header_5: 99 }
          ]
        }
      ]
    end

    before do
      @user = create(:user)
      @calendar_year = create(:calendar_year)
      @finance_month = create(:finance_month)

      allow_any_instance_of(Api::Finance::UploadServices::Uploader)
        .to receive(:process_file)
        .and_return(Api::Finance::UploadServices::Uploader.new(@user, @calendar_year, @finance_month))
      allow_any_instance_of(Api::Finance::UploadServices::Uploader).to receive(:status).and_return(:ok)
      allow_any_instance_of(Api::Finance::UploadServices::Uploader).to receive(:result).and_return(expected_data)
    end

    it 'throws error if missing required params' do
      post '/api/finance/upload'
      expect(last_response.status).to eq(400)
    end

    it 'parses input file and sends to upload service' do
      expect_any_instance_of(Api::Finance::UploadServices::Uploader).to receive(:process_file).with(expected_data)
      post '/api/finance/upload', data: upload_file,
                                  calendar_year_id: @calendar_year.id,
                                  finance_month_id: @finance_month.id
    end

    it 'returns successful status' do
      post '/api/finance/upload', data: upload_file,
                                  calendar_year_id: @calendar_year.id,
                                  finance_month_id: @finance_month.id
      expect(last_response.status).to eq(200)
    end

    it 'returns failed status' do
      allow_any_instance_of(Api::Finance::UploadServices::Uploader).to receive(:status).and_return(:bad_request)
      post '/api/finance/upload', data: upload_file,
                                  calendar_year_id: @calendar_year.id,
                                  finance_month_id: @finance_month.id
      expect(last_response.status).to eq(400)
    end

    it 'returns result in json' do
      post '/api/finance/upload', data: upload_file,
                                  calendar_year_id: @calendar_year.id,
                                  finance_month_id: @finance_month.id
      expect(last_response.header['Content-Type']).to eq('application/json; charset=utf-8')
    end

    it 'returns result in xlsx' do
      post '/api/finance/upload?response_format=xlsx',
           data: upload_file,
           calendar_year_id: @calendar_year.id,
           finance_month_id: @finance_month.id
      expect(last_response.header['Content-Type'])
        .to eq('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    end

    it 'saves history' do
      post '/api/finance/upload', data: upload_file,
                                  calendar_year_id: @calendar_year.id,
                                  finance_month_id: @finance_month.id
      expect(FinanceUpload.last.output).to eq(expected_data.to_json)
    end
  end
end
