# frozen_string_literal: true

require 'spec_helper'

RSpec.describe 'finance/properties', type: :api do
  let(:calendar_year) { create(:calendar_year, default_calendar_year: true) }
  let(:user) { create(:user) }
  let(:agency) { create(:agency) }
  let(:property_1) { create(:property, include_finance_model: true) }
  let(:property_2) { create(:property, include_finance_model: false) }

  before do
    allow_any_instance_of(Api::Finance::PropertiesController).to receive(:user_by_sso_id).and_return(user)
    create(:assignment, user:, agency: nil, property: property_1,
                        selling_vertical_id: property_1.selling_vertical_id,
                        division_id: property_1.division_id,
                        subdivision_id: property_1.subdivision_id,
                        property_type_id: property_1.property_type_id,
                        pillar_id: -1)
    create(:assignment, user:, agency:, property: property_2, pillar_id: -1)
  end

  describe 'GET' do
    it 'requires calendar_year_id' do
      get '/api/finance/properties'
      expect(last_response.status).to eq(400)
    end

    it 'returns success status' do
      get "/api/finance/properties?calendar_year_id=#{calendar_year.id}"
      expect(last_response.status).to eq(200)
    end

    it 'returns only finance properties data' do
      get "/api/finance/properties?calendar_year_id=#{calendar_year.id}"
      json_response = JSON.parse(last_response.body)
      expect(json_response.is_a?(Array)).to eq(true)
    end

    it 'includes image_file_name' do
      get "/api/finance/properties?calendar_year_id=#{calendar_year.id}"
      json_response = JSON.parse(last_response.body)
      expect(json_response.first.keys).to include('image_file_name')
    end
  end
end
