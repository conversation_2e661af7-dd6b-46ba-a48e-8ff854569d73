# frozen_string_literal: true

require 'spec_helper'

RSpec.shared_examples 'finance_controller' do
  let(:calendar_year) { create(:calendar_year, default_calendar_year: true) }
  let(:finance_month) { create(:finance_month) }
  let(:linear_property) { create(:property, :linear) }
  let(:digital_property) { create(:property, :digital) }

  describe 'GET' do
    it 'requires finance_month_id, calendar_year_id && property_id' do
      get @endpoint.to_s
      expect(last_response.status).to eq(400)

      get "#{@endpoint}?finance_month_id=#{finance_month.id}"
      expect(last_response.status).to eq(400)

      get "#{@endpoint}?calendar_year_id=#{calendar_year.id}"
      expect(last_response.status).to eq(400)

      get "#{@endpoint}?property_id=#{linear_property.id}"
      expect(last_response.status).to eq(400)
    end

    it 'returns success status' do
      allow_any_instance_of("Api::Finance::#{@finance_metric_type}Services::Getter".constantize).to receive(:process)
      allow_any_instance_of(Api::Finance::RevenueVarianceServices::DetailGetter).to receive(:process)
      allow_any_instance_of(Api::Finance::RevenueVarianceServices::SummaryGetter).to receive(:process)
      get "#{@endpoint}?finance_month_id=#{finance_month.id}" \
          "&calendar_year_id=#{calendar_year.id}&property_id=#{linear_property.id}"
      expect(last_response.status).to eq(200)
    end

    it 'rejects digital property' do
      skip 'not applicable' if @allow_digital
      allow(PropertyType).to receive(:digital_type).and_return(digital_property.property_type)
      get "#{@endpoint}?finance_month_id=#{finance_month.id}&" \
          "calendar_year_id=#{calendar_year.id}&property_id=#{digital_property.id}"
      expect(last_response.status).to eq(400)
    end
  end

  describe 'PATCH' do
    it 'requires data' do
      patch @endpoint.to_s
      expect(last_response.status).to eq(400)
    end

    it 'returns success status' do
      allow_any_instance_of("Api::Finance::#{@finance_metric_type}Services::Updater".constantize).to receive(:process)
      patch @endpoint.to_s, data: [{ id: 1, value: 100 }]
      expect(last_response.status).to eq(200)
    end

    it 'returns bad_request status' do
      allow_any_instance_of("Api::Finance::#{@finance_metric_type}Services::Updater".constantize)
        .to receive(:process).and_raise(Error::ApiError::BadRequestError.new('TestError'))
      patch @endpoint.to_s, data: [{ id: 1, value: 100 }]
      expect(last_response.status).to eq(400)
    end

    it 'returns unprocessable_entity status' do
      allow_any_instance_of("Api::Finance::#{@finance_metric_type}Services::Updater".constantize)
        .to receive(:process).and_raise(Error::ApiError::UnprocessableEntityError.new('TestError'))
      patch @endpoint.to_s, data: [{ id: 1, value: 100 }]
      expect(last_response.status).to eq(422)
    end

    it 'returns internal_server status' do
      allow_any_instance_of("Api::Finance::#{@finance_metric_type}Services::Updater".constantize)
        .to receive(:process).and_raise(Error::ApiError.new('TestError'))
      patch @endpoint.to_s, data: [{ id: 1, value: 100 }]
      expect(last_response.status).to eq(500)
    end
  end
end
