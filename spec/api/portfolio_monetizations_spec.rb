# frozen_string_literal: true

require 'spec_helper'

RSpec.describe 'api/portfolio_monetizations', type: :api do
  describe 'POST api/portfolio_monetizations/upload' do
    let(:response_body) { JSON.parse(last_response.body) }
    let!(:deal1) { create(:deal) }
    let!(:deal2) { create(:deal) }
    let!(:budget_year) { create(:budget_year, default_budget_year: true) }
    let!(:other_budget_year) { create(:budget_year) }
    let!(:upload_file) do
      Rack::Test::UploadedFile.new(Rails.root + 'spec/support/portfolio_monetization_upload.xlsx',
                                   'multipart/form-data')
    end

    before do
      allow(BudgetYear).to receive(:current_year).and_return(budget_year)
      create(:portfolio_monetization, deal: deal1, budget_year:, projection: 0, registration: 0)
      create(:portfolio_monetization, deal: deal1, budget_year: other_budget_year, projection: 0, registration: 0)
    end

    describe 'parsing upload' do
      let(:expected_data) do
        {
          sheet_name: 'EXECUTIVE SUMMARY',
          sheet_headers: %i[deal pm_projections pm_registrations],
          sheet_data: [
            { deal: 11_111, pm_projections: 22_222, pm_registrations: 33_333 },
            { deal: 44_444, pm_projections: 55_555, pm_registrations: 66_666 }
          ]
        }
      end

      it 'parses file correctly' do
        expect_any_instance_of(Api::PortfolioMonetizationsController).to receive(:input).once.and_return(expected_data)
        post '/api/portfolio_monetizations/upload', data: upload_file
      end
    end

    describe 'processing upload' do
      context 'valid upload file' do
        before do
          allow_any_instance_of(Api::PortfolioMonetizationsController).to receive(:parse_file).and_return(
            [
              {
                sheet_name: 'EXECUTIVE SUMMARY',
                sheet_headers: %i[deal pm_projections pm_registrations],
                sheet_data: [
                  { deal: deal1.id, pm_projections: 1_000_000, pm_registrations: 2_000_000 },
                  { deal: deal2.id, pm_projections: 3_000_000, pm_registrations: 4_000_000 }
                ]
              }
            ]
          )
        end

        it 'uploads data correctly' do
          expect(PortfolioMonetization.find_by(deal_id: deal1.id, budget_year_id: budget_year.id).projection).to eq(0)
          expect(PortfolioMonetization.find_by(deal_id: deal2.id, budget_year_id: budget_year.id)).to be_nil

          post '/api/portfolio_monetizations/upload', data: upload_file
          expect(PortfolioMonetization.find_by(deal_id: deal1.id,
                                               budget_year_id: budget_year.id).projection).to eq(1_000_000)
          expect(PortfolioMonetization.find_by(deal_id: deal2.id,
                                               budget_year_id: budget_year.id).projection).to eq(3_000_000)
        end

        it "does not delete other years' data" do
          post '/api/portfolio_monetizations/upload', data: upload_file
          expect(PortfolioMonetization.find_by(deal_id: deal1.id,
                                               budget_year_id: other_budget_year.id).projection).to eq(0)
        end
      end

      context 'invalid upload file' do
        it 'rejects when missing deal column' do
          allow_any_instance_of(Api::PortfolioMonetizationsController).to receive(:parse_file).and_return(
            [
              {
                sheet_name: 'EXECUTIVE SUMMARY',
                sheet_headers: %i[pm_projections pm_registrations],
                sheet_data: [
                  { pm_projections: 1_000_000, pm_registrations: 2_000_000 },
                  { pm_projections: 3_000_000, pm_registrations: 4_000_000 }
                ]
              }
            ]
          )
          post '/api/portfolio_monetizations/upload', data: upload_file
          expect(last_response.status).to eq(400)
        end

        it 'rejects when missing pm_projections column' do
          allow_any_instance_of(Api::PortfolioMonetizationsController).to receive(:parse_file).and_return(
            [
              {
                sheet_name: 'EXECUTIVE SUMMARY',
                sheet_headers: %i[deal pm_registrations],
                sheet_data: [
                  { deal: deal1.id, pm_registrations: 2_000_000 },
                  { deal: deal2.id, pm_registrations: 4_000_000 }
                ]
              }
            ]
          )
          post '/api/portfolio_monetizations/upload', data: upload_file
          expect(last_response.status).to eq(400)
        end

        it 'rejects when missing pm_registrations column' do
          allow_any_instance_of(Api::PortfolioMonetizationsController).to receive(:parse_file).and_return(
            [
              {
                sheet_name: 'EXECUTIVE SUMMARY',
                sheet_headers: %i[deal pm_projections],
                sheet_data: [
                  { deal: deal1.id, pm_projections: 2_000_000 },
                  { deal: deal2.id, pm_projections: 4_000_000 }
                ]
              }
            ]
          )
          post '/api/portfolio_monetizations/upload', data: upload_file
          expect(last_response.status).to eq(400)
        end

        it 'rejects when deal_id is not valid' do
          allow_any_instance_of(Api::PortfolioMonetizationsController).to receive(:parse_file).and_return(
            [
              {
                sheet_name: 'EXECUTIVE SUMMARY',
                sheet_headers: %i[deal pm_projections pm_registrations],
                sheet_data: [
                  { deal: deal1.id, pm_projections: 1_000_000, pm_registrations: 2_000_000 },
                  { deal: nil, pm_projections: 3_000_000, pm_registrations: 4_000_000 }
                ]
              }
            ]
          )
          post '/api/portfolio_monetizations/upload', data: upload_file
          expect(last_response.status).to eq(422)
        end

        it 'rollbacks when runs into errs' do
          expect(PortfolioMonetization.find_by(deal_id: deal1.id, budget_year_id: budget_year.id).projection).to eq(0)
          expect(PortfolioMonetization.find_by(deal_id: deal2.id, budget_year_id: budget_year.id)).to be_nil

          allow_any_instance_of(Api::PortfolioMonetizationsController).to receive(:parse_file).and_return(
            [
              {
                sheet_name: 'EXECUTIVE SUMMARY',
                sheet_headers: %i[deal pm_projections pm_registrations],
                sheet_data: [
                  { deal: deal1.id, pm_projections: 1_000_000, pm_registrations: 2_000_000 },
                  { deal: nil, pm_projections: 3_000_000, pm_registrations: 4_000_000 }
                ]
              }
            ]
          )
          post '/api/portfolio_monetizations/upload', data: upload_file
          expect(last_response.status).to eq(422)
          expect(PortfolioMonetization.find_by(deal_id: deal1.id, budget_year_id: budget_year.id).projection).to eq(0)
          expect(PortfolioMonetization.find_by(deal_id: deal2.id, budget_year_id: budget_year.id)).to be_nil
        end
      end
    end
  end
end
