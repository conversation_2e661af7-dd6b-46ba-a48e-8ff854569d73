# frozen_string_literal: true

require 'spec_helper'
require 'rails_helper'

describe Api::Upload::Parsers::Csv do
  include ActionDispatch::TestProcess::FixtureFile
  describe '.perform' do
    context 'valid file' do
      it 'should strip out control characters' do
        csv = Api::Upload::Parsers::Csv.new(fixture_file_upload('file_with_excel_ctrl_chars.csv', 'text/csv'))
        expect(csv.parse[0].keys[0].bytes).to eq([65, 103, 101, 110, 99, 121])
      end
    end

    context 'invalid file' do
      it 'raises InvalidFileTypeError' do
        file = fixture_file_upload('invalid_upload', 'text/csv')
        expect { Api::Upload::Parsers::Csv.new(file).parse }.to raise_error(Api::InvalidFileTypeError)
      end
    end
  end
end
