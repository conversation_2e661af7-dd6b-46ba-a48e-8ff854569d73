# frozen_string_literal: true

require 'spec_helper'

describe Api::StealthMode::StealthModeBudgetService do
  subject { described_class }
  let(:budget) { create(:budget) }
  let(:budget_object) do
    {
      budget_id: budget.id,
      current_pricing: 2,
      rate_of_change: 3,
      agency_counter_roc: 4,
      parent_agency_id: 10_001,
      s_actual_prequarter_amount: 5,
      s_actual_quarter4_amount: 6,
      s_actual_quarter1_amount: 7,
      s_actual_quarter2_amount: 8,
      s_actual_quarter3_amount: 9,
      s_actual_postquarter_amount: 10,
      s_projected_prequarter_amount: 11,
      s_projected_quarter4_amount: 12,
      s_projected_quarter1_amount: 13,
      s_projected_quarter2_amount: 14,
      s_projected_quarter3_amount: 15,
      s_projected_postquarter_amount: 16,
      ask_prequarter_amount: 30,
      ask_quarter4_amount: 31,
      ask_quarter1_amount: 32,
      ask_quarter2_amount: 33,
      ask_quarter3_amount: 34,
      ask_postquarter_amount: 35,
      actual_prequarter_amount: 50,
      actual_quarter4_amount: 60,
      actual_quarter1_amount: 70,
      actual_quarter2_amount: 80,
      actual_quarter3_amount: 90,
      actual_postquarter_amount: 100,
      projected_prequarter_amount: 110,
      projected_quarter4_amount: 120,
      projected_quarter1_amount: 130,
      projected_quarter2_amount: 140,
      projected_quarter3_amount: 150,
      projected_postquarter_amount: 160,
      bycal: true,
      placeholder: true,
      registration_type_id: 20_001
    }
  end

  describe 'update' do
    context 'invalid budget object' do
      it 'throws an error if the budget object is invalid' do
        expect do
          subject.new(budget: budget_object.slice(:budget_id), stealth_enabled: true, update_by_stealth_status: false)
        end.to raise_error(Error::ApiError::BadRequestError)
      end
    end

    context 'deal attributes' do
      it 'updates budgets deal as per deal attributes' do
        subject.new(budget: budget_object, stealth_enabled: true, update_by_stealth_status: false).update

        budget.reload
        expect(budget.deal.bycal).to eq(budget_object[:bycal])
        expect(budget.deal.placeholder).to eq(budget_object[:placeholder])
        expect(budget.deal.registration_type_id).to eq(budget_object[:registration_type_id])
      end

      it 'does not update budgets deal if no deal attributes provided' do
        budget.deal.update(bycal: false)
        subject.new(budget: budget_object.except(*%i[bycal registration_type_id]),
                    stealth_enabled: true,
                    update_by_stealth_status: false).update

        budget.reload
        expect(budget.deal.bycal).to be false
        expect(budget.deal.registration_type_id).to eq(-1)
      end
    end

    context 'stealth enabled' do
      it 'updates budget attributes except for current_pricing as per budget object' do
        subject.new(budget: budget_object, stealth_enabled: true, update_by_stealth_status: false).update

        budget.reload
        expect(budget.rate_of_change).to eq(budget_object[:rate_of_change])
        expect(budget.agency_counter_roc).to eq(budget_object[:agency_counter_roc])
        expect(budget.new_business_pricing).to_not eq(budget_object[:current_pricing])
        expect(budget.actual_prequarter_amount).to eq(budget_object[:actual_prequarter_amount])
        expect(budget.actual_quarter4_amount).to eq(budget_object[:actual_quarter4_amount])
        expect(budget.actual_quarter1_amount).to eq(budget_object[:actual_quarter1_amount])
        expect(budget.actual_quarter2_amount).to eq(budget_object[:actual_quarter2_amount])
        expect(budget.actual_quarter3_amount).to eq(budget_object[:actual_quarter3_amount])
        expect(budget.actual_postquarter_amount).to eq(budget_object[:actual_postquarter_amount])
        expect(budget.projected_prequarter_amount).to eq(budget_object[:projected_prequarter_amount])
        expect(budget.projected_quarter4_amount).to eq(budget_object[:projected_quarter4_amount])
        expect(budget.projected_quarter1_amount).to eq(budget_object[:projected_quarter1_amount])
        expect(budget.projected_quarter2_amount).to eq(budget_object[:projected_quarter2_amount])
        expect(budget.projected_quarter3_amount).to eq(budget_object[:projected_quarter3_amount])
        expect(budget.projected_postquarter_amount).to eq(budget_object[:projected_postquarter_amount])
      end

      context 'no stealth mode budget' do
        it 'creates a stealth mode budget from the budget object' do
          expect(StealthModeBudget.find_by(budget_id: budget.id)).to be_nil
          subject.new(budget: budget_object, stealth_enabled: true, update_by_stealth_status: false).update

          smb = StealthModeBudget.find_by(budget_id: budget.id)

          expect(smb.stealth_new_business_pricing).to eq(budget_object[:current_pricing] * 100)
          expect(smb.actual_prequarter_amount).to eq(budget_object[:s_actual_prequarter_amount])
          expect(smb.actual_quarter4_amount).to eq(budget_object[:s_actual_quarter4_amount])
          expect(smb.actual_quarter1_amount).to eq(budget_object[:s_actual_quarter1_amount])
          expect(smb.actual_quarter2_amount).to eq(budget_object[:s_actual_quarter2_amount])
          expect(smb.actual_quarter3_amount).to eq(budget_object[:s_actual_quarter3_amount])
          expect(smb.actual_postquarter_amount).to eq(budget_object[:s_actual_postquarter_amount])
          expect(smb.projected_prequarter_amount).to eq(budget_object[:s_projected_prequarter_amount])
          expect(smb.projected_quarter4_amount).to eq(budget_object[:s_projected_quarter4_amount])
          expect(smb.projected_quarter1_amount).to eq(budget_object[:s_projected_quarter1_amount])
          expect(smb.projected_quarter2_amount).to eq(budget_object[:s_projected_quarter2_amount])
          expect(smb.projected_quarter3_amount).to eq(budget_object[:s_projected_quarter3_amount])
          expect(smb.projected_postquarter_amount).to eq(budget_object[:s_projected_postquarter_amount])
        end
      end

      context 'existing stealth mode budget' do
        let!(:smb) do
          create(:stealth_mode_budget, budget:)
        end
        it 'updates the stealth mode budget based on the budget object' do
          subject.new(budget: budget_object, stealth_enabled: true, update_by_stealth_status: false).update
          smb.reload

          expect(smb.stealth_new_business_pricing).to eq(budget_object[:current_pricing] * 100)
          expect(smb.actual_prequarter_amount).to eq(budget_object[:s_actual_prequarter_amount])
          expect(smb.actual_quarter4_amount).to eq(budget_object[:s_actual_quarter4_amount])
          expect(smb.actual_quarter1_amount).to eq(budget_object[:s_actual_quarter1_amount])
          expect(smb.actual_quarter2_amount).to eq(budget_object[:s_actual_quarter2_amount])
          expect(smb.actual_quarter3_amount).to eq(budget_object[:s_actual_quarter3_amount])
          expect(smb.actual_postquarter_amount).to eq(budget_object[:s_actual_postquarter_amount])
          expect(smb.projected_prequarter_amount).to eq(budget_object[:s_projected_prequarter_amount])
          expect(smb.projected_quarter4_amount).to eq(budget_object[:s_projected_quarter4_amount])
          expect(smb.projected_quarter1_amount).to eq(budget_object[:s_projected_quarter1_amount])
          expect(smb.projected_quarter2_amount).to eq(budget_object[:s_projected_quarter2_amount])
          expect(smb.projected_quarter3_amount).to eq(budget_object[:s_projected_quarter3_amount])
          expect(smb.projected_postquarter_amount).to eq(budget_object[:s_projected_postquarter_amount])
        end
      end

      context 'update by stealth status' do
        it 'only updates the stealth mode budget and budget roc' do
          original_budget = budget

          subject.new(budget: budget_object, stealth_enabled: true, update_by_stealth_status: true).update

          budget.reload
          expect(budget.rate_of_change).to eq(budget_object[:rate_of_change])
          expect(budget.agency_counter_roc).to eq(original_budget.agency_counter_roc)
          expect(budget.new_business_pricing).to eq(original_budget.new_business_pricing)
          expect(budget.actual_prequarter_amount).to eq(original_budget.actual_prequarter_amount)
          expect(budget.actual_quarter4_amount).to eq(original_budget.actual_quarter4_amount)
          expect(budget.actual_quarter1_amount).to eq(original_budget.actual_quarter1_amount)
          expect(budget.actual_quarter2_amount).to eq(original_budget.actual_quarter2_amount)
          expect(budget.actual_quarter3_amount).to eq(original_budget.actual_quarter3_amount)
          expect(budget.actual_postquarter_amount).to eq(original_budget.actual_postquarter_amount)
          expect(budget.projected_prequarter_amount).to eq(original_budget.projected_prequarter_amount)
          expect(budget.projected_quarter4_amount).to eq(original_budget.projected_quarter4_amount)
          expect(budget.projected_quarter1_amount).to eq(original_budget.projected_quarter1_amount)
          expect(budget.projected_quarter2_amount).to eq(original_budget.projected_quarter2_amount)
          expect(budget.projected_quarter3_amount).to eq(original_budget.projected_quarter3_amount)
          expect(budget.projected_postquarter_amount).to eq(original_budget.projected_postquarter_amount)

          smb = StealthModeBudget.find_by(budget_id: budget.id)

          expect(smb.stealth_new_business_pricing).to eq(budget_object[:current_pricing] * 100)
          expect(smb.actual_prequarter_amount).to eq(budget_object[:s_actual_prequarter_amount])
          expect(smb.actual_quarter4_amount).to eq(budget_object[:s_actual_quarter4_amount])
          expect(smb.actual_quarter1_amount).to eq(budget_object[:s_actual_quarter1_amount])
          expect(smb.actual_quarter2_amount).to eq(budget_object[:s_actual_quarter2_amount])
          expect(smb.actual_quarter3_amount).to eq(budget_object[:s_actual_quarter3_amount])
          expect(smb.actual_postquarter_amount).to eq(budget_object[:s_actual_postquarter_amount])
          expect(smb.projected_prequarter_amount).to eq(budget_object[:s_projected_prequarter_amount])
          expect(smb.projected_quarter4_amount).to eq(budget_object[:s_projected_quarter4_amount])
          expect(smb.projected_quarter1_amount).to eq(budget_object[:s_projected_quarter1_amount])
          expect(smb.projected_quarter2_amount).to eq(budget_object[:s_projected_quarter2_amount])
          expect(smb.projected_quarter3_amount).to eq(budget_object[:s_projected_quarter3_amount])
          expect(smb.projected_postquarter_amount).to eq(budget_object[:s_projected_postquarter_amount])
        end
      end
    end

    context 'stealth disabled' do
      it 'updates budget attributes as per budget object' do
        subject.new(budget: budget_object, stealth_enabled: false, update_by_stealth_status: false).update

        budget.reload
        expect(budget.rate_of_change).to eq(budget_object[:rate_of_change])
        expect(budget.agency_counter_roc).to eq(budget_object[:agency_counter_roc])
        expect(budget.new_business_pricing).to eq(budget_object[:current_pricing] * 100)
        expect(budget.actual_prequarter_amount).to eq(budget_object[:actual_prequarter_amount])
        expect(budget.actual_quarter4_amount).to eq(budget_object[:actual_quarter4_amount])
        expect(budget.actual_quarter1_amount).to eq(budget_object[:actual_quarter1_amount])
        expect(budget.actual_quarter2_amount).to eq(budget_object[:actual_quarter2_amount])
        expect(budget.actual_quarter3_amount).to eq(budget_object[:actual_quarter3_amount])
        expect(budget.actual_postquarter_amount).to eq(budget_object[:actual_postquarter_amount])
        expect(budget.projected_prequarter_amount).to eq(budget_object[:projected_prequarter_amount])
        expect(budget.projected_quarter4_amount).to eq(budget_object[:projected_quarter4_amount])
        expect(budget.projected_quarter1_amount).to eq(budget_object[:projected_quarter1_amount])
        expect(budget.projected_quarter2_amount).to eq(budget_object[:projected_quarter2_amount])
        expect(budget.projected_quarter3_amount).to eq(budget_object[:projected_quarter3_amount])
        expect(budget.projected_postquarter_amount).to eq(budget_object[:projected_postquarter_amount])
      end

      it 'does not create a stealth mode budget' do
        subject.new(budget: budget_object, stealth_enabled: false, update_by_stealth_status: false).update

        expect(budget.stealth_mode_budget).to be_nil
      end

      context 'update by stealth status' do
        it 'updates budget attributes as per budget object' do
          subject.new(budget: budget_object, stealth_enabled: false, update_by_stealth_status: true).update

          budget.reload
          expect(budget.rate_of_change).to eq(budget_object[:rate_of_change])
          expect(budget.agency_counter_roc).to eq(budget_object[:agency_counter_roc])
          expect(budget.new_business_pricing).to eq(budget_object[:current_pricing] * 100)
          expect(budget.actual_prequarter_amount).to eq(budget_object[:actual_prequarter_amount])
          expect(budget.actual_quarter4_amount).to eq(budget_object[:actual_quarter4_amount])
          expect(budget.actual_quarter1_amount).to eq(budget_object[:actual_quarter1_amount])
          expect(budget.actual_quarter2_amount).to eq(budget_object[:actual_quarter2_amount])
          expect(budget.actual_quarter3_amount).to eq(budget_object[:actual_quarter3_amount])
          expect(budget.actual_postquarter_amount).to eq(budget_object[:actual_postquarter_amount])
          expect(budget.projected_prequarter_amount).to eq(budget_object[:projected_prequarter_amount])
          expect(budget.projected_quarter4_amount).to eq(budget_object[:projected_quarter4_amount])
          expect(budget.projected_quarter1_amount).to eq(budget_object[:projected_quarter1_amount])
          expect(budget.projected_quarter2_amount).to eq(budget_object[:projected_quarter2_amount])
          expect(budget.projected_quarter3_amount).to eq(budget_object[:projected_quarter3_amount])
          expect(budget.projected_postquarter_amount).to eq(budget_object[:projected_postquarter_amount])
        end
      end
    end
  end
end
