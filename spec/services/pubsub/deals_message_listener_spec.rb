# frozen_string_literal: true

require 'spec_helper'

describe Pubsub::DealsMessageListener do
  context 'Sending notification emails' do
    it 'logs an error and returns if null addresses are provided' do
      expect(Rails.logger).to receive(:warn).once
      subject.send(:send_notification_email, :notify_agency, nil)
      expect(AgencyGatewayNotificationMailer).to receive(:send).exactly(0).times
    end

    it 'logs an error and returns if empty array of addresses are provided' do
      expect(Rails.logger).to receive(:warn).once
      subject.send(:send_notification_email, :notify_agency, [])
      expect(AgencyGatewayNotificationMailer).to receive(:send).exactly(0).times
    end

    it 'rescues from a SocketError when sending an email' do
      to_addrs = ['<EMAIL>']
      expect(Rails.logger).to receive(:error).once
      expect(AgencyGatewayNotificationMailer).to receive(:send).and_raise(SocketError)
      subject.send(:send_notification_email, :notify_agency, to_addrs)
    end

    it 'rescues from an ArgumentError when sending an email' do
      to_addrs = ['<EMAIL>']
      expect(Rails.logger).to receive(:error).twice
      expect(AgencyGatewayNotificationMailer).to receive(:send).and_raise(ArgumentError)
      subject.send(:send_notification_email, :notify_agency, to_addrs)
    end
  end
end
