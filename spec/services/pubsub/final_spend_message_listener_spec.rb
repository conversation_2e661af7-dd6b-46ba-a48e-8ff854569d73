# frozen_string_literal: true

require 'spec_helper'

xdescribe Pubsub::FinalSpendMessageListener do
  subject { described_class.new('agency.final_spend.rspec') }
  let(:upfront) { Marketplace.upfront || create(:marketplace, :upfront) }
  let(:agency) { create(:parent_agency) }
  let(:budget_year) { create(:budget_year) }

  let(:payload) do
    <<-STR
    {
      "marketplace_id": #{upfront.id},
      "agency_id": #{agency.id},
      "budget_year": #{budget_year.fall_year},
      "final_spend": true
    }
    STR
  end
  let(:payload_invalid_ids) do
    <<-STR
    {
      "marketplace_id": 555555,
      "agency_id": 123456,
      "budget_year": 10000,
      "final_spend": true
    }
    STR
  end

  let(:payload_invalid_keys) do
    <<-STR
    {
      "frog": "Rio Grande Chirping Frog",
      "bread": "sourdough",
      "num_children": "sqrt(-1)"
    }
    STR
  end

  describe 'saving final spend' do
    context 'payload is good' do
      it 'saves the final spend status' do
        amy = subject.send(:save_final_spend_status, payload)
        expect(amy.valid?).to be_truthy
        expect(amy.agency).to eq(agency)
        expect(amy.marketplace).to eq(upfront)
        expect(amy.budget_year).to eq(budget_year)
        expect(amy.final_spend).to be_truthy
      end
    end
    context 'payload contains invalid budget year' do
      it 'does not save the final spend status' do
        expect { subject.send(:save_final_spend_status, payload_invalid_ids) }.to raise_error(NoMethodError)
      end
    end
    context 'payload contains invalid keys' do
      it 'does not save the final spend status' do
        expect { subject.send(:save_final_spend_status, payload_invalid_keys) }.to raise_error(RuntimeError)
      end
    end
  end

  it_behaves_like 'a direct message listener'
end
