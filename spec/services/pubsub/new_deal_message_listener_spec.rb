# frozen_string_literal: true

require 'spec_helper'
require 'support/shared_context/ag_new_deal_data'

describe Pubsub::NewDealMessageListener do
  let(:msg_listener) { described_class.new('agency.new_deals.to.pam.rspec', 'agency.new_deals.rspec') }
  let(:uuid) { SecureRandom.uuid }
  let(:budget_year) { create(:budget_year, fall_year: Time.now.year) }

  let(:payload) do
    <<-STR
    {
      "correlation_id": "#{uuid}",
      "source": "AG",
      "deals": [
        {
          "object_id": "5a8dce93126cee19b4593122",
          "agency_id": #{agency.id},
          "advertiser_id": #{advertiser.id},
          "demographic_id": #{demographic.id},
          "marketplace_id": #{marketplace.id},
          "rating_stream_id": #{rating_stream.id},
          "sales_type_id": #{sales_type.id},
          "budget_year": #{budget_year.fall_year},
          "properties": [
            {
              "property_id": #{property_1.id},
              "deal_name": "Test Deal 1"
            },
            {
              "property_id": #{property_1.id},
              "deal_name": "Test Deal 2"
            }
          ]
        },
        {
          "object_id": "5a8dce93126cee19b4593122",
          "agency_id": #{agency2.id},
          "advertiser_id": #{advertiser2.id},
          "demographic_id": #{demographic.id},
          "marketplace_id": #{marketplace.id},
          "rating_stream_id": #{rating_stream.id},
          "budget_year": #{budget_year.fall_year},
          "properties": [
            {
              "property_id": #{property_1.id},
              "deal_name": "Test Deal 4"
            },
            {
              "property_id": #{property_3.id},
              "deal_name": "Test Deal 5"
            }
          ]
        }
      ]
    }
    STR
  end

  let(:payload_invalid_keys) do
    <<-STR
    {
      "cheetah": "Fatest land animal",
      "fruits": "Apples"
    }
    STR
  end

  before(:each) do
    create_default_user
    create_sponsorship_type
    create_digital_sales_type
    allow_any_instance_of(Pubsub::Publisher)
      .to receive(:send)
  end

  describe 'create AG New Deals in PAM' do
    include_context 'ag new deal data'

    before(:each) do
      Deal.delete_all
      allow(PropertyType)
        .to receive(:digital_type)
        .and_return(PropertyType.find_by_name('Digital'))
      msg_listener.instance_variable_set(:@payload, payload)
      msg_listener.send(:valid_payload?, payload)
      msg_listener.send(:create_new_deals)
    end

    context 'payload is good' do
      it 'creates the new deals in PAM' do
        expect(Deal.all.count).to eq(4)
        expect(Deal.all.map(&:source).uniq).to eq(['AG'])
      end

      it 'creates the new deals and assigns the correct attributes' do
        deal1 = Deal.find_by(deal_name: 'Test Deal 1')
        deal2 = Deal.find_by(deal_name: 'Test Deal 5')

        expect(deal1.sales_type_id).to eq(sales_type.id)
        expect(deal2.sales_type_id).to eq(create_digital_sales_type.id)
      end
    end

    context 'payload contains invalid keys' do
      it 'returns false' do
        msg_listener.instance_variable_set(:@payload, payload_invalid_keys)
        expect(msg_listener.send(:valid_payload?, payload_invalid_keys)).to eq(false)
      end
    end
  end
end
