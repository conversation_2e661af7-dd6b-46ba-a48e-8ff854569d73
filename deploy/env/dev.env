# Feature flags
DISABLE_SPORTS=false
ENABLE_EXTERNAL_DEAL_IMPORT=true
ENABLE_EXTERNAL_DEAL_INSERT=true

#Rails
RAILS_ENV=dev
RAILS_LOG_TO_STDOUT=true
SECRET_KEY_BASE=97878b337d9c273ac2e571d1e726c80c1e62d4871aa6ee9a27689b27d5cb6f1d9a2d98bb1101d21be8ea4e245bde644b76ceef7475f5121e378f014cb1f4625e
LOAD_SECRETS=true

# Database config
RAILS_MAX_THREADS=20

# JVM Settings
JAVA_OPTS=-Duser.timezone="+05:30" -XX:MaxMetaspaceSize=356m -XX:MaxDirectMemorySize=256m -Xmx2g

# API V1
SMS_API_HOST=**************
SMS_API_PORT=9080

# Auditing
SMS_SQL_SET_UPDATED_BY=true

APPLICATION_CODE=CC

MSTR_ALEXA_BASE_URL=https://mstr11sradev.inbcu.com/MicroStrategyWebAlexa/servlet/taskProc
MSTR_ALEXA_SERVER=potapwd00069
MSTR_ALEXA_PROJECT_NAME=SMS

#Mail Config
ACTION_MAILER_DEFAULT_URL_HOST=pamapiqa.inbcu.com
PAM_API_SMTP_HOST=email-smtp.us-east-1.amazonaws.com
AGENCY_GATEWAY_ADDR=<EMAIL>
PAM_SUPPORT_EMAIL=<EMAIL>
DLQ_TO_ADDRESSES=<EMAIL>
DLQ_FROM_ADDRESS=<EMAIL>
EXTERNAL_DEAL_IMPORT_SUCCESS_EMAIL=<EMAIL>
EXTERNAL_DEAL_IMPORT_FAILURE_EMAIL=<EMAIL>

#S3 config
PAM_ASSETS_S3_BUCKET_NAME=adsales-pam-assets-private-nonprod
CDW_EXTERNAL_DEAL_S3_BUCKET_NAME=adsales-customapps-dev

SMS_URL='http://pamdev.inbcu.com'

#Mailer
DEAL_SHIFT_EXTERNAL_ADDRESS=https://pammanagementdev.adsalescloud.nbcuni.com/shift_request/deals/
DEAL_SHIFT_SUPPORT=<EMAIL>,<EMAIL>
AGENCY_GATEWAY_ADDR=<EMAIL>
PAM_SUPPORT_EMAIL=<EMAIL>
EXTERNAL_DEAL_IMPORT_SUCCESS_EMAIL=<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>
EXTERNAL_DEAL_IMPORT_FAILURE_EMAIL=<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>

#RMQ configurations
OUTGOING_DLQ_EXCHANGE=pam.to.agency.dlx
OUTGOING_FANOUT_NAME=pam.to.agency
INCOMING_FANOUT_NAME=agency.to.pam
INCOMING_DEALS_QUEUE_NAME=agency.deals
INCOMING_FINAL_SPEND_EX=agency.final_spend
INCOMING_NEW_DEAL_FANOUT_NAME=agency.new_deals.to.pam
INCOMING_NEW_DEALS_QUEUE_NAME=agency.new_deals

#Redis
REDIS_DEFAULT_QUEUE_NAME=dev-pam-api-default
REDIS_SALESFORCE_QUEUE_NAME=dev-pam-api-salesforce

#Salesforce
SF_TOKEN_URL=https://test.salesforce.com/services/oauth2/token

#UWS
UWS_DATABASE_NAME=uws_qa2
UWS_WORKGROUP=adsales-uwsinsights-readonly
UWS_OUTPUT_LOCATION=s3://adsales-tech-athena-output-nonprod/uwsinsights_readonly/
