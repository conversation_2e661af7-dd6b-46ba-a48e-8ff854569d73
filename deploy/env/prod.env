# Feature flags
DISABLE_SPORTS=false
ENABLE_EXTERNAL_DEAL_IMPORT=true
ENABLE_EXTERNAL_DEAL_INSERT=true

# Rails config
RAILS_ENV=production
RAILS_LOG_TO_STDOUT=true
LOAD_SECRETS=true

# Database config
RAILS_MAX_THREADS=20


# JVM Settings
JAVA_OPTS=-Duser.timezone="+05:30" -XX:MaxMetaspaceSize=356m -XX:MaxDirectMemorySize=256m -Xmx2g


# API V1
SMS_API_HOST=*************
SMS_API_PORT=8080

# Auditing
SMS_SQL_SET_UPDATED_BY=true

APPLICATION_CODE=CC

MSTR_ALEXA_BASE_URL=https://mstr11sraprod.inbcu.com/MicroStrategyWebAlexa/servlet/taskProc
MSTR_ALEXA_SERVER=USHAPWP01005
MSTR_ALEXA_PROJECT_NAME=SMS

ACTION_MAILER_DEFAULT_URL_HOST=pamapi.inbcu.com
PAM_API_SMTP_HOST=email-smtp.us-east-1.amazonaws.com
AGENCY_GATEWAY_ADDR=<EMAIL>
PAM_SUPPORT_EMAIL=<EMAIL>
DLQ_TO_ADDRESSES=<EMAIL>, <EMAIL>
DLQ_FROM_ADDRESS=<EMAIL>
EXTERNAL_DEAL_IMPORT_SUCCESS_EMAIL=<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>
EXTERNAL_DEAL_IMPORT_FAILURE_EMAIL=<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>

#S3
PAM_ASSETS_S3_BUCKET_NAME=adsales-pam-assets-private-nonprod
CDW_EXTERNAL_DEAL_S3_BUCKET_NAME=adsales-customapps-prod

# Rabbit queue config
OUTGOING_DLQ_EXCHANGE=pam.to.agency.dlx
OUTGOING_FANOUT_NAME=pam.to.agency
INCOMING_FANOUT_NAME=agency.to.pam
INCOMING_DEALS_QUEUE_NAME=agency.deals
INCOMING_FINAL_SPEND_EX=agency.final_spend
INCOMING_NEW_DEAL_FANOUT_NAME=agency.new_deals.to.pam
INCOMING_NEW_DEALS_QUEUE_NAME=agency.new_deals

SMS_URL='https://pam.inbcu.com'

DEAL_SHIFT_EXTERNAL_ADDRESS=https://pammanagement.nbcuni.com/shift_request/deals/
DEAL_SHIFT_SUPPORT=<EMAIL>

#Redis
REDIS_DEFAULT_QUEUE_NAME=production-pam-api-default
REDIS_SALESFORCE_QUEUE_NAME=production-pam-api-salesforce

#Salesforce
SF_TOKEN_URL=https://login.salesforce.com/services/oauth2/token

#UWS
UWS_DATABASE_NAME=uws_prod2
UWS_WORKGROUP=adsales-uwsinsights-readonly
UWS_OUTPUT_LOCATION=s3://adsales-tech-athena-output-prod/uwsinsights_readonly/
