processing 1 industries
.processing 1 industries
.processing 2 agencies
.processing 2 agencies
.processing 1 advertisers
.processing 1 advertisers
..✅ Successfully created Agency record with ID: 154777
.✅ Successfully created ParentAgency record with ID: 154778
.✅ Successfully created GrandParentAgency record with ID: 154779
....VALIDATION: Circular relationship detected - child 100 -> parent 101
.VALIDATION: Circular relationship detected - child 100 -> parent 102
.VALIDATION: Circular relationship detected - child 100 -> parent 100
.VALIDATION: Would create 4+ level hierarchy - blocked
.VALIDATION: Would create 4+ level hierarchy - blocked
..VALIDATION: Circular relationship detected - child 102 -> parent 100
.VALIDATION: Would create 4+ level hierarchy - blocked
.VALIDATION: Would create 4+ level hierarchy - blocked
...VALIDATION: Would create 4+ level hierarchy - blocked
.Updated agency 100: name, parent, type (within cutoff or regular agency)
.Promoted agency 100 from 'Agency' to 'ParentAgency'
Updated agency 101: name, parent, type (within cutoff or regular agency)
.Promoted agency 100 from 'ParentAgency' to 'GrandParentAgency'
Updated agency 102: name, parent, type (within cutoff or regular agency)
.Updated agency 100: name, parent, type (within cutoff or regular agency)
.PROTECTED: Agency 100 (ParentAgency) is outside cutoff period
PROTECTION: Blocked all changes blocked for Agency ID: 100, Type: ParentAgency, Created: 2025-07-04 14:06:07 UTC (outside cutoff period)
.PROTECTION: Blocked type promotion to GrandParentAgency for Agency ID: 100, Type: ParentAgency, Created: 2025-07-04 14:06:07 UTC (past cutoff period)
Updated agency 102: name, parent, type (within cutoff or regular agency)
.Promoted agency 101 from 'Agency' to 'ParentAgency'
Promoted agency 100 from 'ParentAgency' to 'GrandParentAgency'
Updated agency 100: name, parent, type (within cutoff or regular agency)
VALIDATION: Circular relationship detected - child 100 -> parent 101
BLOCKED: Invalid parent assignment for agency 100 -> parent 101
Reverting parent assignment to -1
.Promoted agency 102 from 'Agency' to 'ParentAgency'
Promoted agency 101 from 'ParentAgency' to 'GrandParentAgency'
Updated agency 103: name, parent, type (within cutoff or regular agency)
VALIDATION: Would create 4+ level hierarchy - blocked
BLOCKED: Invalid parent assignment for agency 103 -> parent 102
Reverting parent assignment to -1
.Updated agency 100: name, parent, type (within cutoff or regular agency)
.Updated agency 100: name, parent, type (within cutoff or regular agency)
.PROTECTION: Blocked type promotion to GrandParentAgency for Agency ID: 100, Type: ParentAgency, Created: 2025-07-04 14:06:07 UTC (past cutoff period)
Updated agency 102: name, parent, type (within cutoff or regular agency)
.......F.FFFFFFprocessing 3 industries
Fprocessing 0 industries
..processing 2 industries
Error processing industry IND001: StandardError - Industry 1 failed
/lib/salesforce/sync/main_logic.rb:33:in `block in process_industries'
/lib/salesforce/sync/main_logic.rb:30:in `process_industries'
/spec/lib/salesforce/sync/main_logic_spec.rb:1071:in `block in <main>'
/spec/lib/salesforce/sync/main_logic_spec.rb:1071:in `block in <main>'
/spec/spec_helper.rb:48:in `block in <main>'
/spec/spec_helper.rb:47:in `block in <main>'
Fprocessing 2 advertisers
Fprocessing 0 advertisers
..processing 2 advertisers
Error processing advertiser ADV001: StandardError - Advertiser 1 failed
/lib/salesforce/sync/main_logic.rb:67:in `block in process_advertisers'
/lib/salesforce/sync/main_logic.rb:64:in `process_advertisers'
/spec/lib/salesforce/sync/main_logic_spec.rb:1123:in `block in <main>'
/spec/lib/salesforce/sync/main_logic_spec.rb:1123:in `block in <main>'
/spec/spec_helper.rb:48:in `block in <main>'
/spec/spec_helper.rb:47:in `block in <main>'
FF

Failures:

  1) Salesforce::Sync::MainLogic#process_sf_advertiser with comprehensive scenarios with new advertiser creation creates category if it does not exist
     Failure/Error: expect { main_logic.send(:process_sf_advertiser, sf_advertiser_data) }.not_to raise_error

       expected no Exception, got #<RSpec::Mocks::MockExpectationError: #<Double "Advertiser"> received unexpected message :advertiser_name= with ("Test Advertiser Corp")> with backtrace:
         # ./lib/salesforce/sync/main_logic.rb:327:in `block in process_sf_advertiser'
         # ./lib/salesforce/sync/main_logic.rb:325:in `process_sf_advertiser'
         # ./spec/lib/salesforce/sync/main_logic_spec.rb:857:in `block in <main>'
         # ./spec/lib/salesforce/sync/main_logic_spec.rb:857:in `block in <main>'
         # ./spec/spec_helper.rb:48:in `block in <main>'
         # ./spec/spec_helper.rb:47:in `block in <main>'
     # ./spec/lib/salesforce/sync/main_logic_spec.rb:857:in `block in <main>'
     # ./spec/spec_helper.rb:48:in `block in <main>'
     # ./spec/spec_helper.rb:47:in `block in <main>'

  2) Salesforce::Sync::MainLogic#process_sf_advertiser with comprehensive scenarios with boolean field handling handles mixed case boolean values
     Failure/Error: expect { main_logic.send(:process_sf_advertiser, sf_data) }.not_to raise_error

       expected no Exception, got #<RSpec::Mocks::MockExpectationError: #<Double "Advertiser"> received unexpected message :new_record? with (no args)> with backtrace:
         # ./lib/salesforce/sync/main_logic.rb:334:in `block in process_sf_advertiser'
         # ./lib/salesforce/sync/main_logic.rb:325:in `process_sf_advertiser'
         # ./spec/lib/salesforce/sync/main_logic_spec.rb:903:in `block in <main>'
         # ./spec/lib/salesforce/sync/main_logic_spec.rb:903:in `block in <main>'
         # ./spec/spec_helper.rb:48:in `block in <main>'
         # ./spec/spec_helper.rb:47:in `block in <main>'
     # ./spec/lib/salesforce/sync/main_logic_spec.rb:903:in `block in <main>'
     # ./spec/spec_helper.rb:48:in `block in <main>'
     # ./spec/spec_helper.rb:47:in `block in <main>'

  3) Salesforce::Sync::MainLogic#process_sf_advertiser with comprehensive scenarios with boolean field handling handles nil boolean values
     Failure/Error: expect { main_logic.send(:process_sf_advertiser, sf_data) }.not_to raise_error

       expected no Exception, got #<RSpec::Mocks::MockExpectationError: #<Double "Advertiser"> received unexpected message :new_record? with (no args)> with backtrace:
         # ./lib/salesforce/sync/main_logic.rb:334:in `block in process_sf_advertiser'
         # ./lib/salesforce/sync/main_logic.rb:325:in `process_sf_advertiser'
         # ./spec/lib/salesforce/sync/main_logic_spec.rb:930:in `block in <main>'
         # ./spec/lib/salesforce/sync/main_logic_spec.rb:930:in `block in <main>'
         # ./spec/spec_helper.rb:48:in `block in <main>'
         # ./spec/spec_helper.rb:47:in `block in <main>'
     # ./spec/lib/salesforce/sync/main_logic_spec.rb:930:in `block in <main>'
     # ./spec/spec_helper.rb:48:in `block in <main>'
     # ./spec/spec_helper.rb:47:in `block in <main>'

  4) Salesforce::Sync::MainLogic#process_sf_advertiser with comprehensive scenarios with edge cases and error handling handles missing industry information
     Failure/Error: expect { main_logic.send(:process_sf_advertiser, sf_data) }.not_to raise_error

       expected no Exception, got #<RSpec::Mocks::MockExpectationError: #<Double "Advertiser"> received unexpected message :new_record? with (no args)> with backtrace:
         # ./lib/salesforce/sync/main_logic.rb:334:in `block in process_sf_advertiser'
         # ./lib/salesforce/sync/main_logic.rb:325:in `process_sf_advertiser'
         # ./spec/lib/salesforce/sync/main_logic_spec.rb:955:in `block in <main>'
         # ./spec/lib/salesforce/sync/main_logic_spec.rb:955:in `block in <main>'
         # ./spec/spec_helper.rb:48:in `block in <main>'
         # ./spec/spec_helper.rb:47:in `block in <main>'
     # ./spec/lib/salesforce/sync/main_logic_spec.rb:955:in `block in <main>'
     # ./spec/spec_helper.rb:48:in `block in <main>'
     # ./spec/spec_helper.rb:47:in `block in <main>'

  5) Salesforce::Sync::MainLogic#process_sf_advertiser with comprehensive scenarios with edge cases and error handling handles missing billing state
     Failure/Error: expect { main_logic.send(:process_sf_advertiser, sf_data) }.not_to raise_error

       expected no Exception, got #<RSpec::Mocks::MockExpectationError: #<Double "Advertiser"> received :main_state_id= with unexpected...got: (5)
        Please stub a default value first if message might be received with other args as well. 
       > with backtrace:
         # ./lib/salesforce/sync/main_logic.rb:329:in `block in process_sf_advertiser'
         # ./lib/salesforce/sync/main_logic.rb:325:in `process_sf_advertiser'
         # ./spec/lib/salesforce/sync/main_logic_spec.rb:976:in `block in <main>'
         # ./spec/lib/salesforce/sync/main_logic_spec.rb:976:in `block in <main>'
         # ./spec/spec_helper.rb:48:in `block in <main>'
         # ./spec/spec_helper.rb:47:in `block in <main>'
     # ./spec/lib/salesforce/sync/main_logic_spec.rb:976:in `block in <main>'
     # ./spec/spec_helper.rb:48:in `block in <main>'
     # ./spec/spec_helper.rb:47:in `block in <main>'

  6) Salesforce::Sync::MainLogic#process_sf_advertiser with comprehensive scenarios with edge cases and error handling handles empty advertiser name
     Failure/Error: expect { main_logic.send(:process_sf_advertiser, sf_data) }.not_to raise_error

       expected no Exception, got #<RSpec::Mocks::MockExpectationError: #<Double "Advertiser"> received unexpected message :new_record? with (no args)> with backtrace:
         # ./lib/salesforce/sync/main_logic.rb:334:in `block in process_sf_advertiser'
         # ./lib/salesforce/sync/main_logic.rb:325:in `process_sf_advertiser'
         # ./spec/lib/salesforce/sync/main_logic_spec.rb:997:in `block in <main>'
         # ./spec/lib/salesforce/sync/main_logic_spec.rb:997:in `block in <main>'
         # ./spec/spec_helper.rb:48:in `block in <main>'
         # ./spec/spec_helper.rb:47:in `block in <main>'
     # ./spec/lib/salesforce/sync/main_logic_spec.rb:997:in `block in <main>'
     # ./spec/spec_helper.rb:48:in `block in <main>'
     # ./spec/spec_helper.rb:47:in `block in <main>'

  7) Salesforce::Sync::MainLogic#process_sf_advertiser with comprehensive scenarios with edge cases and error handling handles special characters in advertiser name
     Failure/Error: expect { main_logic.send(:process_sf_advertiser, sf_data) }.not_to raise_error

       expected no Exception, got #<RSpec::Mocks::MockExpectationError: #<Double "Advertiser"> received unexpected message :new_record? with (no args)> with backtrace:
         # ./lib/salesforce/sync/main_logic.rb:334:in `block in process_sf_advertiser'
         # ./lib/salesforce/sync/main_logic.rb:325:in `process_sf_advertiser'
         # ./spec/lib/salesforce/sync/main_logic_spec.rb:1018:in `block in <main>'
         # ./spec/lib/salesforce/sync/main_logic_spec.rb:1018:in `block in <main>'
         # ./spec/spec_helper.rb:48:in `block in <main>'
         # ./spec/spec_helper.rb:47:in `block in <main>'
     # ./spec/lib/salesforce/sync/main_logic_spec.rb:1018:in `block in <main>'
     # ./spec/spec_helper.rb:48:in `block in <main>'
     # ./spec/spec_helper.rb:47:in `block in <main>'

  8) Salesforce::Sync::MainLogic#process_industries with error handling with successful processing processes multiple industries successfully
     Failure/Error: expect { main_logic.send(:process_industries) }.not_to raise_error

       expected no Exception, got #<RSpec::Mocks::MockExpectationError: #<InstanceDouble(Salesforce::Sync::SfLogic) (anonymous)> receiv...ed unexpected message :sf_industry_status with ({"Id"=>"IND001", "Name"=>"Technology"}, "pickedup")> with backtrace:
         # ./lib/salesforce/sync/main_logic.rb:31:in `block in process_industries'
         # ./lib/salesforce/sync/main_logic.rb:30:in `process_industries'
         # ./spec/lib/salesforce/sync/main_logic_spec.rb:1042:in `block in <main>'
         # ./spec/lib/salesforce/sync/main_logic_spec.rb:1042:in `block in <main>'
         # ./spec/spec_helper.rb:48:in `block in <main>'
         # ./spec/spec_helper.rb:47:in `block in <main>'
     # ./spec/lib/salesforce/sync/main_logic_spec.rb:1042:in `block in <main>'
     # ./spec/spec_helper.rb:48:in `block in <main>'
     # ./spec/spec_helper.rb:47:in `block in <main>'

  9) Salesforce::Sync::MainLogic#process_industries with error handling with error scenarios continues processing after individual industry error
     Failure/Error: expect { main_logic.send(:process_industries) }.to raise_error(StandardError, 'Industry 1 failed')

       expected StandardError with "Industry 1 failed", got #<RSpec::Mocks::MockExpectationError: #<InstanceDouble(Salesforce::Sync::SfLogic) (anonymous)> receiv...IND001", "Name"=>"Technology"}, "pickedup"]
       +[{"Id"=>"IND002", "Name"=>"Healthcare"}, "processed"]
       > with backtrace:
         # ./lib/salesforce/sync/main_logic.rb:34:in `block in process_industries'
         # ./lib/salesforce/sync/main_logic.rb:30:in `process_industries'
         # ./spec/lib/salesforce/sync/main_logic_spec.rb:1071:in `block in <main>'
         # ./spec/lib/salesforce/sync/main_logic_spec.rb:1071:in `block in <main>'
         # ./spec/spec_helper.rb:48:in `block in <main>'
         # ./spec/spec_helper.rb:47:in `block in <main>'
     # ./spec/lib/salesforce/sync/main_logic_spec.rb:1071:in `block in <main>'
     # ./spec/spec_helper.rb:48:in `block in <main>'
     # ./spec/spec_helper.rb:47:in `block in <main>'

  10) Salesforce::Sync::MainLogic#process_advertisers with error handling with successful processing processes multiple advertisers successfully
      Failure/Error: expect { main_logic.send(:process_advertisers) }.not_to raise_error

        expected no Exception, got #<RSpec::Mocks::MockExpectationError: #<InstanceDouble(Salesforce::Sync::SfLogic) (anonymous)> receiv...ertiser_status with ({"Id"=>"ADV001", "Industry__c"=>"IND001", "Name"=>"Advertiser 1"}, "pickedup")> with backtrace:
          # ./lib/salesforce/sync/main_logic.rb:65:in `block in process_advertisers'
          # ./lib/salesforce/sync/main_logic.rb:64:in `process_advertisers'
          # ./spec/lib/salesforce/sync/main_logic_spec.rb:1094:in `block in <main>'
          # ./spec/lib/salesforce/sync/main_logic_spec.rb:1094:in `block in <main>'
          # ./spec/spec_helper.rb:48:in `block in <main>'
          # ./spec/spec_helper.rb:47:in `block in <main>'
      # ./spec/lib/salesforce/sync/main_logic_spec.rb:1094:in `block in <main>'
      # ./spec/spec_helper.rb:48:in `block in <main>'
      # ./spec/spec_helper.rb:47:in `block in <main>'

  11) Salesforce::Sync::MainLogic#process_advertisers with error handling with error scenarios continues processing after individual advertiser error
      Failure/Error: expect { main_logic.send(:process_advertisers) }.to raise_error(StandardError, 'Advertiser 1 failed')

        expected StandardError with "Advertiser 1 failed", got #<RSpec::Mocks::MockExpectationError: #<InstanceDouble(Salesforce::Sync::SfLogic) (anonymous)> receiv...01", "Name"=>"Advertiser 1"}, "pickedup"]
        +[{"Id"=>"ADV002", "Name"=>"Advertiser 2"}, "processed"]
        > with backtrace:
          # ./lib/salesforce/sync/main_logic.rb:68:in `block in process_advertisers'
          # ./lib/salesforce/sync/main_logic.rb:64:in `process_advertisers'
          # ./spec/lib/salesforce/sync/main_logic_spec.rb:1123:in `block in <main>'
          # ./spec/lib/salesforce/sync/main_logic_spec.rb:1123:in `block in <main>'
          # ./spec/spec_helper.rb:48:in `block in <main>'
          # ./spec/spec_helper.rb:47:in `block in <main>'
      # ./spec/lib/salesforce/sync/main_logic_spec.rb:1123:in `block in <main>'
      # ./spec/spec_helper.rb:48:in `block in <main>'
      # ./spec/spec_helper.rb:47:in `block in <main>'

  12) Salesforce::Sync::MainLogic Integration scenarios with realistic Salesforce data processes complete advertiser with industry chain
      Failure/Error: expect { main_logic.send(:process_sf_industry, sf_industry) }.not_to raise_error

        expected no Exception, got #<RSpec::Mocks::MockExpectationError: #<InstanceDouble(Salesforce::Sync::PamLogic) (anonymous)> recei...,
        - {:create_pamobj=>true, :update_pamobj=>false}]
        + {:create_pamobj=>true, :update_pamobj=>true}]
        > with backtrace:
          # ./lib/salesforce/sync/main_logic.rb:246:in `process_sf_industry'
          # ./spec/lib/salesforce/sync/main_logic_spec.rb:1171:in `block in <main>'
          # ./spec/lib/salesforce/sync/main_logic_spec.rb:1171:in `block in <main>'
          # ./spec/spec_helper.rb:48:in `block in <main>'
          # ./spec/spec_helper.rb:47:in `block in <main>'
      # ./spec/lib/salesforce/sync/main_logic_spec.rb:1171:in `block in <main>'
      # ./spec/spec_helper.rb:48:in `block in <main>'
      # ./spec/spec_helper.rb:47:in `block in <main>'

Finished in 6.26 seconds (files took 19.08 seconds to load)
59 examples, 12 failures

Failed examples:

rspec ./spec/lib/salesforce/sync/main_logic_spec.rb:827 # Salesforce::Sync::MainLogic#process_sf_advertiser with comprehensive scenarios with new advertiser creation creates category if it does not exist
rspec ./spec/lib/salesforce/sync/main_logic_spec.rb:879 # Salesforce::Sync::MainLogic#process_sf_advertiser with comprehensive scenarios with boolean field handling handles mixed case boolean values
rspec ./spec/lib/salesforce/sync/main_logic_spec.rb:906 # Salesforce::Sync::MainLogic#process_sf_advertiser with comprehensive scenarios with boolean field handling handles nil boolean values
rspec ./spec/lib/salesforce/sync/main_logic_spec.rb:935 # Salesforce::Sync::MainLogic#process_sf_advertiser with comprehensive scenarios with edge cases and error handling handles missing industry information
rspec ./spec/lib/salesforce/sync/main_logic_spec.rb:958 # Salesforce::Sync::MainLogic#process_sf_advertiser with comprehensive scenarios with edge cases and error handling handles missing billing state
rspec ./spec/lib/salesforce/sync/main_logic_spec.rb:979 # Salesforce::Sync::MainLogic#process_sf_advertiser with comprehensive scenarios with edge cases and error handling handles empty advertiser name
rspec ./spec/lib/salesforce/sync/main_logic_spec.rb:1000 # Salesforce::Sync::MainLogic#process_sf_advertiser with comprehensive scenarios with edge cases and error handling handles special characters in advertiser name
rspec ./spec/lib/salesforce/sync/main_logic_spec.rb:1029 # Salesforce::Sync::MainLogic#process_industries with error handling with successful processing processes multiple industries successfully
rspec ./spec/lib/salesforce/sync/main_logic_spec.rb:1058 # Salesforce::Sync::MainLogic#process_industries with error handling with error scenarios continues processing after individual industry error
rspec ./spec/lib/salesforce/sync/main_logic_spec.rb:1082 # Salesforce::Sync::MainLogic#process_advertisers with error handling with successful processing processes multiple advertisers successfully
rspec ./spec/lib/salesforce/sync/main_logic_spec.rb:1110 # Salesforce::Sync::MainLogic#process_advertisers with error handling with error scenarios continues processing after individual advertiser error
rspec ./spec/lib/salesforce/sync/main_logic_spec.rb:1130 # Salesforce::Sync::MainLogic Integration scenarios with realistic Salesforce data processes complete advertiser with industry chain

