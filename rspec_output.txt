processing 1 industries
.processing 1 industries
.processing 2 agencies
.processing 2 agencies
.processing 1 advertisers
.processing 1 advertisers
..✅ Successfully created Agency record with ID: 155118
.✅ Successfully created ParentAgency record with ID: 155119
.✅ Successfully created GrandParentAgency record with ID: 155120
....VALIDATION: Circular relationship detected - child 100 -> parent 101
.VALIDATION: Circular relationship detected - child 100 -> parent 102
.VALIDATION: Circular relationship detected - child 100 -> parent 100
.VALIDATION: Would create 4+ level hierarchy - blocked
.VALIDATION: Would create 4+ level hierarchy - blocked
..VALIDATION: Circular relationship detected - child 102 -> parent 100
.VALIDATION: Would create 4+ level hierarchy - blocked
.VALIDATION: Would create 4+ level hierarchy - blocked
...VALIDATION: Would create 4+ level hierarchy - blocked
.Updated agency 100: name, parent, type (within cutoff or regular agency)
.Promoted agency 100 from 'Agency' to 'ParentAgency'
Updated agency 101: name, parent, type (within cutoff or regular agency)
.Promoted agency 100 from 'ParentAgency' to 'GrandParentAgency'
Updated agency 102: name, parent, type (within cutoff or regular agency)
.Updated agency 100: name, parent, type (within cutoff or regular agency)
.PROTECTED: Agency 100 (ParentAgency) is outside cutoff period
PROTECTION: Blocked all changes blocked for Agency ID: 100, Type: ParentAgency, Created: 2025-07-04 15:15:57 UTC (outside cutoff period)
.PROTECTION: Blocked type promotion to GrandParentAgency for Agency ID: 100, Type: ParentAgency, Created: 2025-07-04 15:15:57 UTC (past cutoff period)
Updated agency 102: name, parent, type (within cutoff or regular agency)
.Promoted agency 101 from 'Agency' to 'ParentAgency'
Promoted agency 100 from 'ParentAgency' to 'GrandParentAgency'
Updated agency 100: name, parent, type (within cutoff or regular agency)
VALIDATION: Circular relationship detected - child 100 -> parent 101
BLOCKED: Invalid parent assignment for agency 100 -> parent 101
Reverting parent assignment to -1
.Promoted agency 102 from 'Agency' to 'ParentAgency'
Promoted agency 101 from 'ParentAgency' to 'GrandParentAgency'
Updated agency 103: name, parent, type (within cutoff or regular agency)
VALIDATION: Would create 4+ level hierarchy - blocked
BLOCKED: Invalid parent assignment for agency 103 -> parent 102
Reverting parent assignment to -1
.Updated agency 100: name, parent, type (within cutoff or regular agency)
.Updated agency 100: name, parent, type (within cutoff or regular agency)
.PROTECTION: Blocked type promotion to GrandParentAgency for Agency ID: 100, Type: ParentAgency, Created: 2025-07-04 15:15:57 UTC (past cutoff period)
Updated agency 102: name, parent, type (within cutoff or regular agency)
...............processing 3 industries
.processing 0 industries
..processing 2 industries
Error processing industry IND001: StandardError - Industry 1 failed
/lib/salesforce/sync/main_logic.rb:33:in `block in process_industries'
/lib/salesforce/sync/main_logic.rb:30:in `process_industries'
/spec/lib/salesforce/sync/main_logic_spec.rb:1029:in `block in <main>'
/spec/lib/salesforce/sync/main_logic_spec.rb:1029:in `block in <main>'
/spec/spec_helper.rb:48:in `block in <main>'
/spec/spec_helper.rb:47:in `block in <main>'
.processing 2 advertisers
.processing 0 advertisers
..processing 2 advertisers
Error processing advertiser ADV001: StandardError - Advertiser 1 failed
/lib/salesforce/sync/main_logic.rb:67:in `block in process_advertisers'
/lib/salesforce/sync/main_logic.rb:64:in `process_advertisers'
/spec/lib/salesforce/sync/main_logic_spec.rb:1084:in `block in <main>'
/spec/lib/salesforce/sync/main_logic_spec.rb:1084:in `block in <main>'
/spec/spec_helper.rb:48:in `block in <main>'
/spec/spec_helper.rb:47:in `block in <main>'
..

Finished in 6.39 seconds (files took 19.41 seconds to load)
59 examples, 0 failures

