GIT
  remote: https://github.com/NBCUniversal/nbc_conventions.git
  revision: 2803e362eeb904e75c0c3a57fb79b136f53a653a
  specs:
    nbc_conventions (0.2.0)
      activerecord-oracle_enhanced-adapter (>= 1.4.0)
      rake
      rspec

GIT
  remote: https://github.com/NBCUniversal/pam_client.git
  revision: a52ddddc20ff0372ce2c3e8d10cebd8cc79973f2
  branch: develop
  specs:
    pam_client (2.37.0)
      activerecord (>= 7.0.7)
      composite_primary_keys
      data_migrate (~> 9.2)
      rails (>= 7.0.8)
      scenic
      scenic-oracle_adapter
      splitter
      validates_email_format_of

GIT
  remote: https://github.com/NBCUniversal/pam_secret_manager.git
  revision: defccaf6678b8c4fca727b75ddaf44105130370c
  specs:
    pam_secret_manager (1.1.0)
      aws-sdk-secretsmanager

GIT
  remote: https://github.com/NBCUniversal/splitter.git
  revision: cf1d3682bca2e60cfd87520c7d46f44e7a029d49
  specs:
    splitter (1.0.2)
      dry-struct
      dry-validation

GIT
  remote: https://github.com/mimemagicrb/mimemagic.git
  revision: 01f92d86d15d85cfd0f20dabd025dcbd36a8a60f
  ref: 01f92d86d15d85cfd0f20dabd025dcbd36a8a60f
  specs:
    mimemagic (0.3.5)

GIT
  remote: https://github.com/rsim/oracle-enhanced.git
  revision: 1ad893df4f3c083c8c6044e57f5b06c80cad2b28
  tag: v7.0.3
  specs:
    activerecord-oracle_enhanced-adapter (7.0.3-java)
      activerecord (~> 7.0.0)
      ruby-plsql (>= 0.6.0)

GEM
  remote: https://rubygems.org/
  specs:
    actioncable (*******)
      actionpack (= *******)
      activesupport (= *******)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
    actionmailbox (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      mail (>= 2.7.1)
      net-imap
      net-pop
      net-smtp
    actionmailer (*******)
      actionpack (= *******)
      actionview (= *******)
      activejob (= *******)
      activesupport (= *******)
      mail (~> 2.5, >= 2.5.4)
      net-imap
      net-pop
      net-smtp
      rails-dom-testing (~> 2.0)
    actionpack (*******)
      actionview (= *******)
      activesupport (= *******)
      rack (~> 2.0, >= 2.2.4)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.0, >= 1.2.0)
    actiontext (*******)
      actionpack (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (*******)
      activesupport (= *******)
      builder (~> 3.1)
      erubi (~> 1.4)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.1, >= 1.2.0)
    activejob (*******)
      activesupport (= *******)
      globalid (>= 0.3.6)
    activemodel (*******)
      activesupport (= *******)
    activerecord (*******)
      activemodel (= *******)
      activesupport (= *******)
    activestorage (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activesupport (= *******)
      marcel (~> 1.0)
      mini_mime (>= 1.1.0)
    activesupport (*******)
      concurrent-ruby (~> 1.0, >= 1.0.2)
      i18n (>= 1.6, < 2)
      minitest (>= 5.1)
      tzinfo (~> 2.0)
    addressable (2.8.7)
      public_suffix (>= 2.0.2, < 7.0)
    amq-protocol (2.3.2)
    ast (2.4.2)
    athens (0.4.2)
      aws-sdk-athena (~> 1)
      multi_json (~> 1.0)
    aws-eventstream (1.3.0)
    aws-partitions (1.962.0)
    aws-sdk-athena (1.89.0)
      aws-sdk-core (~> 3, >= 3.201.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-core (3.201.3)
      aws-eventstream (~> 1, >= 1.3.0)
      aws-partitions (~> 1, >= 1.651.0)
      aws-sigv4 (~> 1.8)
      jmespath (~> 1, >= 1.6.1)
    aws-sdk-kms (1.88.0)
      aws-sdk-core (~> 3, >= 3.201.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-s3 (1.157.0)
      aws-sdk-core (~> 3, >= 3.201.0)
      aws-sdk-kms (~> 1)
      aws-sigv4 (~> 1.5)
    aws-sdk-secretsmanager (1.102.0)
      aws-sdk-core (~> 3, >= 3.201.0)
      aws-sigv4 (~> 1.5)
    aws-sigv4 (1.9.1)
      aws-eventstream (~> 1, >= 1.0.2)
    base64 (0.2.0)
    bigdecimal (3.1.8-java)
    builder (3.3.0)
    bunny (2.23.0)
      amq-protocol (~> 2.3, >= 2.3.1)
      sorted_set (~> 1, >= 1.0.2)
    bunny-mock (1.7.0)
      bunny (>= 1.7)
    caxlsx (4.1.0)
      htmlentities (~> 4.3, >= 4.3.4)
      marcel (~> 1.0)
      nokogiri (~> 1.10, >= 1.10.4)
      rubyzip (>= 1.3.0, < 3)
    caxlsx_rails (0.6.4)
      actionpack (>= 3.1)
      caxlsx (>= 3.0)
    childprocess (5.1.0)
      logger (~> 1.5)
    coderay (1.1.3)
    composite_primary_keys (14.0.10)
      activerecord (~> 7.0.2)
    concurrent-ruby (1.3.3)
    connection_pool (2.4.1)
    crack (1.0.0)
      bigdecimal
      rexml
    crass (1.0.6)
    data_migrate (9.4.2)
      activerecord (>= 6.1)
      railties (>= 6.1)
    database_cleaner (2.0.2)
      database_cleaner-active_record (>= 2, < 3)
    database_cleaner-active_record (2.2.0)
      activerecord (>= 5.a)
      database_cleaner-core (~> 2.0.0)
    database_cleaner-core (2.0.1)
    date (3.3.4-java)
    diff-lcs (1.5.1)
    dotenv (3.1.2)
    dotenv-rails (3.1.2)
      dotenv (= 3.1.2)
      railties (>= 6.1)
    dry-configurable (1.2.0)
      dry-core (~> 1.0, < 2)
      zeitwerk (~> 2.6)
    dry-core (1.0.1)
      concurrent-ruby (~> 1.0)
      zeitwerk (~> 2.6)
    dry-inflector (1.1.0)
    dry-initializer (3.1.1)
    dry-logic (1.5.0)
      concurrent-ruby (~> 1.0)
      dry-core (~> 1.0, < 2)
      zeitwerk (~> 2.6)
    dry-schema (1.13.4)
      concurrent-ruby (~> 1.0)
      dry-configurable (~> 1.0, >= 1.0.1)
      dry-core (~> 1.0, < 2)
      dry-initializer (~> 3.0)
      dry-logic (>= 1.4, < 2)
      dry-types (>= 1.7, < 2)
      zeitwerk (~> 2.6)
    dry-struct (1.6.0)
      dry-core (~> 1.0, < 2)
      dry-types (>= 1.7, < 2)
      ice_nine (~> 0.11)
      zeitwerk (~> 2.6)
    dry-types (1.7.2)
      bigdecimal (~> 3.0)
      concurrent-ruby (~> 1.0)
      dry-core (~> 1.0)
      dry-inflector (~> 1.0)
      dry-logic (~> 1.4)
      zeitwerk (~> 2.6)
    dry-validation (1.10.0)
      concurrent-ruby (~> 1.0)
      dry-core (~> 1.0, < 2)
      dry-initializer (~> 3.0)
      dry-schema (>= 1.12, < 2)
      zeitwerk (~> 2.6)
    erubi (1.13.0)
    et-orbi (1.2.11)
      tzinfo
    factory_bot (6.4.6)
      activesupport (>= 5.0.0)
    factory_bot_rails (6.4.3)
      factory_bot (~> 6.4)
      railties (>= 5.0.0)
    faraday (1.10.4)
      faraday-em_http (~> 1.0)
      faraday-em_synchrony (~> 1.0)
      faraday-excon (~> 1.1)
      faraday-httpclient (~> 1.0)
      faraday-multipart (~> 1.0)
      faraday-net_http (~> 1.0)
      faraday-net_http_persistent (~> 1.0)
      faraday-patron (~> 1.0)
      faraday-rack (~> 1.0)
      faraday-retry (~> 1.0)
      ruby2_keywords (>= 0.0.4)
    faraday-em_http (1.0.0)
    faraday-em_synchrony (1.0.0)
    faraday-excon (1.1.0)
    faraday-follow_redirects (0.3.0)
      faraday (>= 1, < 3)
    faraday-httpclient (1.0.1)
    faraday-multipart (1.0.4)
      multipart-post (~> 2)
    faraday-net_http (1.0.2)
    faraday-net_http_persistent (1.2.0)
    faraday-patron (1.0.0)
    faraday-rack (1.0.0)
    faraday-retry (1.0.3)
    ffaker (2.23.0)
    ffi (1.17.0-java)
    fugit (1.11.0)
      et-orbi (~> 1, >= 1.2.11)
      raabro (~> 1.4)
    globalid (1.2.1)
      activesupport (>= 6.1)
    hashdiff (1.1.1)
    hashie (5.0.0)
    health_check (3.1.0)
      railties (>= 5.0)
    htmlentities (4.3.4)
    httpclient (2.8.3)
    i18n (1.14.5)
      concurrent-ruby (~> 1.0)
    ice_nine (0.11.2)
    jbuilder (2.12.0)
      actionview (>= 5.0.0)
      activesupport (>= 5.0.0)
    jmespath (1.6.2)
    json (2.7.2-java)
    jwt (2.9.3)
      base64
    kaminari (1.2.2)
      activesupport (>= 4.1.0)
      kaminari-actionview (= 1.2.2)
      kaminari-activerecord (= 1.2.2)
      kaminari-core (= 1.2.2)
    kaminari-actionview (1.2.2)
      actionview
      kaminari-core (= 1.2.2)
    kaminari-activerecord (1.2.2)
      activerecord
      kaminari-core (= 1.2.2)
    kaminari-core (1.2.2)
    language_server-protocol (********)
    launchy (3.0.1)
      addressable (~> 2.8)
      childprocess (~> 5.0)
    letter_opener (1.10.0)
      launchy (>= 2.2, < 4)
    listen (3.9.0)
      rb-fsevent (~> 0.10, >= 0.10.3)
      rb-inotify (~> 0.9, >= 0.9.10)
    little-plugger (1.1.4)
    logger (1.6.0)
    logging (2.4.0)
      little-plugger (~> 1.1)
      multi_json (~> 1.14)
    loofah (2.22.0)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.0.4)
    march_hare (4.6.0-java)
    method_source (1.1.0)
    mini_mime (1.1.5)
    minitest (5.24.1)
    multi_json (1.15.0)
    multipart-post (2.4.1)
    net-imap (0.4.14)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-smtp (0.5.0)
      net-protocol
    nio4r (2.7.3-java)
    nokogiri (1.16.7-java)
      racc (~> 1.4)
    parallel (1.26.1)
    parser (*******)
      ast (~> 2.4.1)
      racc
    pry (0.14.2-java)
      coderay (~> 1.1)
      method_source (~> 1.0)
      spoon (~> 0.0)
    public_suffix (6.0.1)
    puma (3.12.6-java)
    raabro (1.4.0)
    racc (1.8.1-java)
    rack (2.2.9)
    rack-cors (2.0.2)
      rack (>= 2.0.0)
    rack-session (1.0.2)
      rack (< 3)
    rack-test (2.1.0)
      rack (>= 1.3)
    rack-utf8_sanitizer (1.9.1)
      rack (>= 1.0, < 4.0)
    rails (*******)
      actioncable (= *******)
      actionmailbox (= *******)
      actionmailer (= *******)
      actionpack (= *******)
      actiontext (= *******)
      actionview (= *******)
      activejob (= *******)
      activemodel (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      bundler (>= 1.15.0)
      railties (= *******)
    rails-dom-testing (2.2.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.0)
      loofah (~> 2.21)
      nokogiri (~> 1.14)
    railties (*******)
      actionpack (= *******)
      activesupport (= *******)
      method_source
      rake (>= 12.2)
      thor (~> 1.0)
      zeitwerk (~> 2.5)
    rainbow (3.1.1)
    rake (13.2.1)
    rb-fsevent (0.11.2)
    rb-inotify (0.11.1)
      ffi (~> 1.0)
    redis (5.2.0)
      redis-client (>= 0.22.0)
    redis-actionpack (5.4.0)
      actionpack (>= 5, < 8)
      redis-rack (>= 2.1.0, < 4)
      redis-store (>= 1.1.0, < 2)
    redis-activesupport (5.3.0)
      activesupport (>= 3, < 8)
      redis-store (>= 1.3, < 2)
    redis-client (0.22.2)
      connection_pool
    redis-namespace (1.11.0)
      redis (>= 4)
    redis-rack (3.0.0)
      rack-session (>= 0.2.0)
      redis-store (>= 1.2, < 2)
    redis-rails (5.0.2)
      redis-actionpack (>= 5.0, < 6)
      redis-activesupport (>= 5.0, < 6)
      redis-store (>= 1.2, < 2)
    redis-store (1.10.0)
      redis (>= 4, < 6)
    regexp_parser (2.9.2)
    restforce (7.4.0)
      faraday (>= 1.1.0, < 2.11.0)
      faraday-follow_redirects (<= 0.3.0, < 1.0.0)
      faraday-multipart (>= 1.0.0, < 2.0.0)
      faraday-net_http (< 4.0.0)
      hashie (>= 1.2.0, < 6.0)
      jwt (>= 1.5.6)
    rexml (3.3.4)
      strscan
    roo (2.10.1)
      nokogiri (~> 1)
      rubyzip (>= 1.3.0, < 3.0.0)
    rspec (3.13.0)
      rspec-core (~> 3.13.0)
      rspec-expectations (~> 3.13.0)
      rspec-mocks (~> 3.13.0)
    rspec-core (3.13.0)
      rspec-support (~> 3.13.0)
    rspec-expectations (3.13.1)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-mocks (3.13.1)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-rails (4.1.2)
      actionpack (>= 4.2)
      activesupport (>= 4.2)
      railties (>= 4.2)
      rspec-core (~> 3.10)
      rspec-expectations (~> 3.10)
      rspec-mocks (~> 3.10)
      rspec-support (~> 3.10)
    rspec-support (3.13.1)
    rubocop (1.57.2)
      json (~> 2.3)
      language_server-protocol (>= 3.17.0)
      parallel (~> 1.10)
      parser (>= *******)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 1.8, < 3.0)
      rexml (>= 3.2.5, < 4.0)
      rubocop-ast (>= 1.28.1, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 2.4.0, < 3.0)
    rubocop-ast (1.32.0)
      parser (>= *******)
    rubocop-performance (1.21.1)
      rubocop (>= 1.48.1, < 2.0)
      rubocop-ast (>= 1.31.1, < 2.0)
    ruby-plsql (0.8.0)
    ruby-progressbar (1.13.0)
    ruby2_keywords (0.0.5)
    rubyzip (2.3.2)
    rufus-scheduler (3.9.1)
      fugit (~> 1.1, >= 1.1.6)
    scenic (1.8.0)
      activerecord (>= 4.0.0)
      railties (>= 4.0.0)
    scenic-oracle_adapter (1.3.5)
      activerecord-oracle_enhanced-adapter (>= 1.5.0)
      scenic (= 1.8.0)
    sidekiq (6.5.5)
      connection_pool (>= 2.2.2)
      rack (~> 2.0)
      redis (>= 4.5.0)
    simpleidn (0.2.3)
    sorted_set (1.0.3-java)
    spoon (0.0.6)
      ffi
    strscan (3.1.0-java)
    thor (1.3.1)
    timeout (0.4.1)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    tzinfo-data (1.2024.1)
      tzinfo (>= 1.0.0)
    unicode-display_width (2.5.0)
    validates_email_format_of (1.8.2)
      i18n (>= 0.8.0)
      simpleidn
    webmock (3.23.1)
      addressable (>= 2.8.0)
      crack (>= 0.3.2)
      hashdiff (>= 0.4.0, < 2.0.0)
    websocket-driver (0.7.6-java)
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    zeitwerk (2.6.17)

PLATFORMS
  universal-java-1.8
  universal-java-17

DEPENDENCIES
  activerecord-oracle_enhanced-adapter!
  athens
  aws-sdk-s3
  bunny-mock
  byebug
  caxlsx
  caxlsx_rails
  database_cleaner
  dotenv-rails
  factory_bot_rails
  faraday (~> 1.10.0)
  faraday-net_http
  ffaker
  health_check
  httpclient
  jbuilder
  kaminari
  letter_opener
  listen
  logging
  march_hare
  mimemagic!
  nbc_conventions!
  pam_client!
  pam_secret_manager!
  pry
  puma (~> 3.7)
  rack-cors
  rack-utf8_sanitizer
  rails (~> 7.0.8)
  redis-namespace
  redis-rails
  restforce (~> 7.4.0)
  roo
  rspec-rails (~> 4.0)
  rubocop (= 1.57.2)
  rubocop-performance
  rubyzip (>= 1.2.1)
  rufus-scheduler
  sidekiq (~> 6.5)
  splitter!
  tzinfo-data
  webmock

RUBY VERSION
   ruby 3.1.4p0 (jruby *******)

BUNDLED WITH
   2.5.16
