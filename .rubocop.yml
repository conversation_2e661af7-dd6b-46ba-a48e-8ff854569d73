inherit_from: .rubocop_todo.yml

require: rubocop-performance

AllCops:
  Exclude:
    - 'db/**/*'
    - 'bin/**/*'
    - 'vendor/**/*'
    - 'lib/tasks/**/*'
    - 'config/initializers/active_record.rb'
    - 'config/initializers/health_check.rb'
  DisplayCopNames: true
  NewCops: enable
  TargetRubyVersion: 3.1.4
Bundler:
  Enabled: true
Gemspec:
  Enabled: true
Layout:
  Enabled: true
Layout/LineLength:
  AutoCorrect: true
  Exclude:
    - Gemfile
Layout/EndAlignment:
  AutoCorrect: true
Lint:
  Enabled: true

Lint/ParenthesesAsGroupedExpression:
  Exclude:
    - '**/*.jbuilder'

Metrics:
  Enabled: true

Metrics/BlockLength:
  Exclude:
    - 'spec/**/*'

Migration:
  Enabled: true
Naming:
  Enabled: true
Performance/MapMethodChain:
  Enabled: false
Security:
  Enabled: true
Style:
  Enabled: true
Style/FetchEnvVar:
  Enabled: false
Style/ObjectThen:
  Enabled: false
