class CreateBudgetManagementScenarioView < ActiveRecord::Migration[5.1]
  # this migration is for CI purposes only in pam_api and pam_api_test schemas

  def up
    unless ActiveRecord::Base.connection.view_exists?('budget_management_scenario')
      execute(<<~SQL)

    CREATE OR REPLACE FORCE VIEW BUDGET_MANAGEMENT_SCENARIO AS 
    SELECT
            PED.PACING_BUDGET_ID,
            PED.PROPERTY_ID,
            P.PROPERTY_NAME,
            PROPERTY_TYPE.PROPERTY_TYPE_NAME,
            PED.ADVERTISER_ID,
            AD.ADVERTISER_NAME,
            PED.AGENCY_ID,
            AG.AGENCY_NAME,
            PED.BAE_APP_USER_ID,
            BAE.FULL_NAME AS BUYING_AE_NAME,
            PED.CAE_APP_USER_ID,
            CAE.FULL_NAME AS CLIENT_AE_NAME,
            PED.PAE_APP_USER_ID,
            PAE.FULL_NAME AS PLANNING_AE_NAME,
            V.VERTICAL_NAME,
            PED.VERTICAL_ID,
            PED.QUARTER_ID,
            PED.QUARTER,
            PED.QTR_LOCK_STATUS,
            CY.CALENDAR_YEAR_ID,
            PS.PACING_BUDGET_STATUS_ID,
            PS.PACING_BUDGET_STATUS_NAME,
            PED.BUDGET_DOLLARS,
          PED.PROJECTION_DOLLARS,
          PED.CY_CC_SALES_DOLLARS,
          PED.PY_CC_SALES_DOLLARS AS PY_CC_SALES_DOLLARS,
          PED.CY_WK_SALES_DOLLARS AS CY_WORKING_DOLLARS,
          PED.PY_WK_SALES_DOLLARS AS PY_WORKING_DOLLARS,
          BAE.LOCATION_ID AS BUYING_AE_LOCATION_ID,
          BAE.LOCATION_NAME AS BUYING_AE_LOCATION_NAME,
          CAE.LOCATION_ID AS CLIENT_AE_LOCATION_ID,
          CAE.LOCATION_NAME AS CLIENT_AE_LOCATION_NAME,
          PAE.LOCATION_ID AS PLANNING_AE_LOCATION_ID,
          PAE.LOCATION_ID AS PLANNING_AE_LOCATION_NAME,
          PED.BUYING_TL_ID,
          BTL.FIRST_NAME AS BUYING_TL_FIRSTNAME,
          BTL.LAST_NAME AS BUYING_TL_LASTNAME,
          PED.CLIENT_TL_ID,
          CTL.FIRST_NAME AS CLIENT_TL_FIRSTNAME,
          CTL.LAST_NAME AS CLIENT_TL_LASTNAME,
          PED.PLANNING_TL_ID,
          PTL.FIRST_NAME AS PLANNING_TL_FIRSTNAME,
          PTL.LAST_NAME AS PLANNING_TL_LASTNAME,
          CY.CALENDAR_YEAR AS CALENDAR_YEAR,
          PED.PACING_BUDGET_DETAIL_ID AS PACING_BUDGET_DETAIL_ID,
          PED.SCENARIO_AGENCY_ID,
          SAG.AGENCY_NAME AS SCENARIO_AGENCY_NAME,
          PED.SCENARIO_BAE_APP_USER_ID,
          SBAE.FULL_NAME AS SCENARIO_BUYING_AE_NAME,
          PED.SCENARIO_CAE_APP_USER_ID,
          SCAE.FULL_NAME AS SCENARIO_CLIENT_AE_NAME,
          PED.SCENARIO_PAE_APP_USER_ID,
          SPAE.FULL_NAME AS SCENARIO_PLANNING_AE_NAME,
          PED.SCENARIO_BUDGET_DOLLARS,
          PED.SCENARIO_PROJECTION_DOLLARS,
          AE_SPLIT.BAE_SPLIT,
          AE_SPLIT.CAE_SPLIT,
          AE_SPLIT.PAE_SPLIT,
          NVL(PY_PBD.PROJECTION_DOLLARS, 0) AS PY_PROJECTION_DOLLARS,
          NVL(CY_CL.PERCENTAGE, 0) AS CY_CL_PERCENTAGE,
          NVL(PY_CL.PERCENTAGE, 0) AS PY_CL_PERCENTAGE,
          NVL(PED.SCENARIO_BAE_APP_USER_ID, PED.BAE_APP_USER_ID) FILTERABLE_BAE_APP_USER_ID,
          NVL(PED.SCENARIO_CAE_APP_USER_ID, PED.CAE_APP_USER_ID) FILTERABLE_CAE_APP_USER_ID,
          NVL(PED.SCENARIO_PAE_APP_USER_ID, PED.PAE_APP_USER_ID) FILTERABLE_PAE_APP_USER_ID,
          NVL(PED.SCENARIO_AGENCY_ID, PED.AGENCY_ID) FILTERABLE_AGENCY_ID,
          NVL(SBAE.LOCATION_ID, BAE.LOCATION_ID) FILTERABLE_BAE_LOCATION_ID,
          NVL(SCAE.LOCATION_ID, CAE.LOCATION_ID) FILTERABLE_CAE_LOCATION_ID,
          NVL(SPAE.LOCATION_ID, PAE.LOCATION_ID) FILTERABLE_PAE_LOCATION_ID,
          NVL(PAC_OPP.OPPORTUNITY_DOLLARS, 0) OPPORTUNITY_DOLLARS
  FROM PED_KEYS_AND_DOLLARS_VIEW PED

  JOIN CALENDAR_YEAR CY ON CY.CALENDAR_YEAR_ID = PED.CALENDAR_YEAR_ID
  JOIN AE_SPLIT ON AE_SPLIT.PROPERTY_ID = PED.PROPERTY_ID
  JOIN PACING_BUDGET_STATUS PS ON PED.PACING_BUDGET_STATUS_ID = PS.PACING_BUDGET_STATUS_ID
  LEFT OUTER JOIN QUARTER PY_QTR ON PY_QTR.YEAR = (CY.CALENDAR_YEAR - 1) AND PY_QTR.QUARTER = PED.QUARTER
  LEFT OUTER JOIN PACING_BUDGET_DETAIL PY_PBD ON PY_PBD.QUARTER_ID = PY_QTR.QUARTER_ID AND PY_PBD.PACING_BUDGET_ID = PED.PACING_BUDGET_ID
  JOIN CONFIDENCE_LEVEL CY_CL ON PED.CONFIDENCE_LEVEL_ID = CY_CL.CONFIDENCE_LEVEL_ID
  LEFT OUTER JOIN CONFIDENCE_LEVEL PY_CL ON PY_PBD.CONFIDENCE_LEVEL_ID = PY_CL.CONFIDENCE_LEVEL_ID
  JOIN USER_DATA_VIEW BAE ON PED.BAE_APP_USER_ID = BAE.APP_USER_ID
  JOIN USER_DATA_VIEW CAE ON PED.CAE_APP_USER_ID = CAE.APP_USER_ID
  JOIN USER_DATA_VIEW PAE ON PED.PAE_APP_USER_ID = PAE.APP_USER_ID
  LEFT OUTER JOIN USER_DATA_VIEW SBAE ON PED.SCENARIO_BAE_APP_USER_ID = SBAE.APP_USER_ID
  LEFT OUTER JOIN USER_DATA_VIEW SCAE ON PED.SCENARIO_CAE_APP_USER_ID = SCAE.APP_USER_ID
  LEFT OUTER JOIN USER_DATA_VIEW SPAE ON PED.SCENARIO_PAE_APP_USER_ID = SPAE.APP_USER_ID
  LEFT OUTER JOIN AGENCY SAG ON PED.SCENARIO_AGENCY_ID = SAG.AGENCY_ID
  JOIN USER_DATA_VIEW BTL ON PED.BUYING_TL_ID = BTL.APP_USER_ID
  JOIN USER_DATA_VIEW CTL ON PED.CLIENT_TL_ID = CTL.APP_USER_ID
  JOIN USER_DATA_VIEW PTL ON PED.PLANNING_TL_ID = PTL.APP_USER_ID
  JOIN VERTICAL V ON PED.VERTICAL_ID = V.VERTICAL_ID
  JOIN AGENCY AG ON PED.AGENCY_ID = AG.AGENCY_ID
  JOIN ADVERTISER AD ON PED.ADVERTISER_ID = AD.ADVERTISER_ID
  JOIN PROPERTY P ON PED.PROPERTY_ID = P.PROPERTY_ID
  JOIN PROPERTY_TYPE ON P.PROPERTY_TYPE_ID = PROPERTY_TYPE.PROPERTY_TYPE_ID AND P.PROPERTY_TYPE_ID != -1
  LEFT OUTER JOIN PACING_OPPORTUNITY_DOLLARS PAC_OPP ON PED.PACING_BUDGET_DETAIL_ID = PAC_OPP.PACING_BUDGET_DETAIL_ID

      SQL
    end
  end

  def down
    # irreversible
  end
end
