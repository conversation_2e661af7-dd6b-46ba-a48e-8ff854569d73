class CreateUserDataView < ActiveRecord::Migration[5.1]
  # this migration is for CI purposes only in pam_api and pam_api_test schemas
  
  def up
    unless ActiveRecord::Base.connection.view_exists?('user_data_view')
      execute(<<~SQL)
      CREATE OR REPLACE FORCE VIEW USER_DATA_VIEW AS 
        SELECT U.APP_USER_ID, U.FIRST_NAME, U.LAST_NAME, U.FIRST_NAME || ' ' || U.LAST_NAME FULL_NAME, U.LOCATION_ID, LOC.LOCATION_NAME
          FROM APP_USER U
        JOIN LOCATION LOC ON U.LOCATION_ID = LOC.LOCATION_ID
      SQL
    end
  end

  def down
    # irreversible
  end
end
