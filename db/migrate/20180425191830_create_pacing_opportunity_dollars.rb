class CreatePacingOpportunityDollars < ActiveRecord::Migration[5.1]
  # this migration is for CI purposes only in pam_api and pam_api_test schemas
  
  def up
    unless ActiveRecord::Base.connection.view_exists?('pacing_opportunity_dollars')
      execute(<<~SQL)
        CREATE OR REPLACE FORCE VIEW PACING_OPPORTUNITY_DOLLARS AS 
          SELECT
          OPPO.PACING_BUDGET_DETAIL_ID,
          SUM(OPPO.OPPORTUNITY_DOLLARS) AS OPPORTUNITY_DOLLARS
        FROM OPPORTUNITY OPPO
        JOIN OPPORTUNITY_STATUS OPPO_ST ON OPPO.OPPORTUNITY_STATUS_ID = OPPO_ST.OPPORTUNITY_STATUS_ID
        AND OPPO_ST.COUNT_AS_PROJECTED_FINISH = 1
        GROUP BY OPPO.PACING_BUDGET_DETAIL_ID
      SQL
    end
  end

  def down
    # irreversible
  end
end
