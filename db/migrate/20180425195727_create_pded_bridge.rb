class CreatePdedBridge < ActiveRecord::Migration[5.1]
  # this migration is for CI purposes only in pam_api and pam_api_test schemas
  
  def up
    unless ActiveRecord::Base.connection.view_exists?('pd_ed_bridge')
      execute(<<~SQL)
        CREATE OR REPLACE FORCE VIEW PD_ED_BRIDGE AS 
        select PBD.PACING_BUDGET_ID, PBD.PACING_BUDGET_DETAIL_ID, PBD.QUARTER_ID, TO_NUMBER(SKA.EXTERNAL_SYSTEM_KEY) SALES_SYSTEM_ID,
        q.quarter, q.calendar_year_id CALENDAR_YEAR, PBD.PROPERTY_ID, PBD.VERTICAL_ID, PBD.SALES_TYPE_ID, SKA.SYSTEM_KEY_ASC_ID
        from system_key_asc ska
        join pacing_budget_detail pbd on ska.pam_key = pbd.pacing_budget_detail_id
        join quarter q on pbd.quarter_id = q.quarter_id
        where external_system_id = 2 and external_key_type_id = 51
      SQL
    end
  end

  def down
    # irreversible
  end
end
