class CreatePdKeysAndDollarsView < ActiveRecord::Migration[5.1]
  # this migration is for CI purposes only in pam_api and pam_api_test schemas
  
  def up
    unless ActiveRecord::Base.connection.view_exists?('pd_keys_and_dollars_view')
      execute(<<~SQL)
        CREATE OR REPLACE FORCE VIEW PD_KEYS_AND_DOLLARS_VIEW AS 
          SELECT
          PACING_BUDGET_DETAIL.PACING_BUDGET_ID,
          PACING_BUDGET_DETAIL.PACING_BUDGET_DETAIL_ID,
          PACING_BUDGET_DETAIL.PROPERTY_ID,
          PACING_BUDGET_DETAIL.ADVERTISER_ID,
          PACING_BUDGET_DETAIL.AGENCY_ID,
          PACING_BUDGET_DETAIL.BAE_APP_USER_ID,
          PACING_BUDGET_DETAIL.CAE_APP_USER_ID,
          PACING_BUDGET_DETAIL.PAE_APP_USER_ID,
          PACING_BUDGET_DETAIL.SALES_TYPE_ID,
          PACING_BUDGET_DETAIL.VERTICAL_ID,
          PACING_BUDGET.ACTIVE,
          PACING_BUDGET.INCLUDE_AGENCY,
          PACING_BUDGET_DETAIL.QUARTER_ID,
          QTR.QUARTER,
          QTR.LOCK_STATUS AS QTR_LOCK_STATUS,
          PACING_BUDGET_DETAIL.BUYING_TL_ID,
          PACING_BUDGET_DETAIL.CLIENT_TL_ID,
          PACING_BUDGET_DETAIL.PLANNING_TL_ID,
          PACING_BUDGET_DETAIL.BUDGET_DOLLARS,
          PACING_BUDGET_DETAIL.PROJECTION_DOLLARS,
          PACING_BUDGET_YEAR_STATUS.CALENDAR_YEAR_ID,
          PACING_BUDGET_YEAR_STATUS.PACING_BUDGET_STATUS_ID,
          PACING_BUDGET_DETAIL.CONFIDENCE_LEVEL_ID,
          PACING_BUDGET_DETAIL.SCENARIO_AGENCY_ID,
          PACING_BUDGET_DETAIL.SCENARIO_BAE_APP_USER_ID,
          PACING_BUDGET_DETAIL.SCENARIO_CAE_APP_USER_ID,
          PACING_BUDGET_DETAIL.SCENARIO_PAE_APP_USER_ID,
          PACING_BUDGET_DETAIL.SCENARIO_BUDGET_DOLLARS,
          PACING_BUDGET_DETAIL.SCENARIO_PROJECTION_DOLLARS
          FROM PACING_BUDGET_DETAIL
          INNER JOIN PACING_BUDGET ON PACING_BUDGET_DETAIL.PACING_BUDGET_ID = PACING_BUDGET.PACING_BUDGET_ID AND PACING_BUDGET.ACTIVE = 1 AND PACING_BUDGET_DETAIL.ACTIVE = 1
          INNER JOIN QUARTER QTR ON QTR.QUARTER_ID = PACING_BUDGET_DETAIL.QUARTER_ID
          INNER JOIN PACING_BUDGET_YEAR_STATUS ON PACING_BUDGET_YEAR_STATUS.PACING_BUDGET_ID = PACING_BUDGET.PACING_BUDGET_ID AND PACING_BUDGET_YEAR_STATUS.CALENDAR_YEAR_ID = PACING_BUDGET_DETAIL.CALENDAR_YEAR_ID
      SQL
    end
  end

  def down
    # irreversible
  end
end
