class CreatePedKeysAndDollarsView < ActiveRecord::Migration[5.1]
  # this migration is for CI purposes only in pam_api and pam_api_test schemas

  def up
    unless ActiveRecord::Base.connection.view_exists?('ped_keys_and_dollars_view')
      execute(<<~SQL)
      CREATE OR REPLACE FORCE VIEW PED_KEYS_AND_DOLLARS_VIEW AS
      SELECT
      CP.PACING_BUDGET_ID,
      CP.PACING_BUDGET_DETAIL_ID,
      CP.PROPERTY_ID,
      CP.ADVERTISER_ID,
      CP.AGENCY_ID,
      CP.BAE_APP_USER_ID,
      CP.PAE_APP_USER_ID,
      CP.CAE_APP_USER_ID,
      CP.SALES_TYPE_ID,
      CP.VERTICAL_ID,
      CP.ACTIVE,
      CP.INCLUDE_AGENCY,
      CP.BUYING_TL_ID,
      CP.CLIENT_TL_ID,
      CP.PLANNING_TL_ID,
      CP.QUARTER_ID,
      CP.QUARTER,
      CP.QTR_LOCK_STATUS,
      CP.CALENDAR_YEAR_ID,
      CP.PACING_BUDGET_STATUS_ID,
      CP.BUDGET_DOLLARS,
      CP.PROJECTION_DOLLARS,
      CP.CONFIDENCE_LEVEL_ID,
      CP.SCENARIO_AGENCY_ID,
      CP.SCENARIO_BAE_APP_USER_ID,
      CP.SCENARIO_CAE_APP_USER_ID,
      CP.SCENARIO_PAE_APP_USER_ID,
      CP.SCENARIO_BUDGET_DOLLARS,
      CP.SCENARIO_PROJECTION_DOLLARS,
      NVL(ED.CC_SALES_DOLLARS, 0) as CY_CC_SALES_DOLLARS,
      NVL(ED.WK_SALES_DOLLARS, 0) as CY_WK_SALES_DOLLARS,
      NVL(PYED.CC_SALES_DOLLARS, 0) as PY_CC_SALES_DOLLARS,
      NVL(PYED.WK_SALES_DOLLARS, 0) as PY_WK_SALES_DOLLARS
      FROM PD_KEYS_AND_DOLLARS_VIEW CP
      LEFT OUTER JOIN EXTERNAL_DEAL_DATA_VIEW ED ON CP.PACING_BUDGET_ID = ED.PACING_BUDGET_ID and CP.QUARTER_ID = ED.QUARTER_ID
      LEFT OUTER JOIN EXTERNAL_DEAL_DATA_VIEW PYED ON  CP.PACING_BUDGET_ID = PYED.PACING_BUDGET_ID and CP.QUARTER = PYED.QUARTER AND (CP.CALENDAR_YEAR_ID - 1) = PYED.CALENDAR_YEAR_ID

      SQL
    end
  end

  def down
    # irreversible
  end
end
