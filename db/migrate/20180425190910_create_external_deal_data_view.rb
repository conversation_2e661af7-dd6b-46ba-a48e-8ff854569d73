class CreateExternalDealDataView < ActiveRecord::Migration[5.1]
  # this migration is for CI purposes only in pam_api and pam_api_test schemas
  
  def up
    unless ActiveRecord::Base.connection.view_exists?('external_deal_data_view')
      execute(<<~SQL)
        CREATE OR REPLACE FORCE VIEW EXTERNAL_DEAL_DATA_VIEW AS 
        SELECT pacing_budget_id, pacing_budget_detail_id, edd.quarter_id, br.quarter, br.calendar_year CALENDAR_YEAR_ID,
          sum(case when edd.business_type_id = 1 then nvl(edd.sales_dollars,0) else 0 end)  CC_SALES_DOLLARS,
          sum(case when edd.business_type_id = 2 then nvl(edd.sales_dollars,0) else 0 end) WK_SALES_DOLLARS
          from EXTERNAL_DEAL ED
          INNER JOIN EXTERNAL_DEAL_DETAIL EDD on EDD.EXTERNAL_DEAL_ID = ED.EXTERNAL_DEAL_ID AND ED.ACTIVE = 1 AND EDD.ACTIVE = 1 AND EDD.BUSINESS_TYPE_ID IN (1,2)
          INNER JOIN PD_ED_BRIDGE br on ED.SALES_SYSTEM_ID = BR.SALES_SYSTEM_ID
          AND EDD.QUARTER_ID = BR.QUARTER_ID AND ED.PROPERTY_ID = BR.PROPERTY_ID
          AND ED.VERTICAL_ID = BR.VERTICAL_ID AND ED.SALES_TYPE_ID = BR.SALES_TYPE_ID
          group by pacing_budget_id, pacing_budget_detail_id, br.quarter_id, edd.quarter_id, br.quarter, br.calendar_year
      SQL
    end
  end

  def down
    # irreversible
  end
end
