# frozen_string_literal: true

# Helper module for filtering and printing exception backtraces
module BacktraceHelper
  class << self
    # Filters the backtrace to show only app-specific lines
    #
    # @param backtrace [Array<String>] The full backtrace from an exception
    # @return [Array<String>] Filtered backtrace with only app-specific lines
    def filter_backtrace(backtrace)
      app_root = File.expand_path('..', __dir__)
      filtered_backtrace = backtrace.select do |line|
        line.include?(app_root) && !line.include?('/lib/ruby/gems/')
      end
      filtered_backtrace.map do |line|
        line.gsub(app_root, '')
      end
    end

    # Prints a formatted exception message with a filtered backtrace
    #
    # @param exception [Exception] The exception to print
    # @param context [String, nil] Optional context to prepend to the exception message
    def print_exception(exception, context = nil)
      puts "#{"#{context}: " if context}#{exception.class.name} - #{exception.message}"
      filtered_backtrace = filter_backtrace(exception.backtrace)
      puts filtered_backtrace.join("\n")
    end
  end
end
