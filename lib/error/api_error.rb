# frozen_string_literal: true

module Error
  class ApiError < StandardError
    attr_reader :status, :message, :data

    def initialize(message, data = nil, status = :internal_server_error)
      @message = message
      @status = status
      @data = data
    end

    class UnauthorizedError < ApiError
      def initialize(message, data = nil)
        super
        @status = :unauthorized
      end
    end

    class ForbiddenError < ApiError
      def initialize(message, data = nil)
        super
        @status = :forbidden
      end
    end

    class BadRequestError < ApiError
      def initialize(message, data = nil)
        super
        @status = :bad_request
      end
    end

    class UnprocessableEntityError < ApiError
      def initialize(message, data = nil)
        super
        @status = :unprocessable_entity
      end
    end

    class FailedDependencyError < ApiError
      def initialize(message, data = nil)
        super
        @status = :failed_dependency
      end
    end

    class ConflictError < ApiError
      def initialize(message, data = nil)
        super
        @status = :conflict
      end
    end
  end
end
