# frozen_string_literal: true

require_relative 'api_error'

module Error
  module <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
    def self.included(clazz)
      clazz.class_eval do
        rescue_from ActionController::ParameterMissing do |error|
          log_error(error)
          render json: { error: error.class.to_s, message: error.message }, status: :bad_request
        end

        rescue_from ActiveRecord::ActiveRecordError do |error|
          log_error(error)
          render json: { error: error.class.to_s, message: error.message, data: (begin
                                                                                   error.record
                                                                                 rescue StandardError
                                                                                   nil
                                                                                 end) }, status: :unprocessable_entity
        end

        rescue_from Error::ApiError do |error|
          log_error(error)
          render json: { error: error.class.to_s, message: error.message, data: error.data }, status: error.status
        end
      end
    end

    def log_error(error)
      Rails.logger.info(error)
      Rails.logger.debug(caller.first(50).join("\n"))
    end
  end
end
