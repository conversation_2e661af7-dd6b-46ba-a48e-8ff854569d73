# frozen_string_literal: true

module Salesforce
  class Client
    attr_reader :instance_url, :access_token

    def initialize
      fetch_token
    end

    # Sample Response
    # {
    #   "attributes": {
    #       "type": "Opportunity",
    #       "url": "/services/data/v58.0/sobjects/Opportunity/0060400000ASNJXAA5"
    #   },
    #   "OprerativeOne_Proposal_ID__c": null,
    #   "Id": "0060400000ASNJXAA5"
    # }
    def get_operative_one_id(opportunity_id)
      response = get("/services/data/v58.0/sobjects/Opportunity/#{opportunity_id}?fields=OprerativeOne_Proposal_ID__c")
      body = JSON.parse(response.body)
      body['OprerativeOne_Proposal_ID__c']
    end

    # Sample Response
    # {
    #   "totalSize": 299,
    #   "done": true,
    #   "records": [
    #       {
    #           "attributes": {
    #               "type": "Opportunity",
    #               "url": "/services/data/v58.0/sobjects/Opportunity/0060400000A0bw7AAB"
    #           },
    #           "Id": "0060400000A0bw7AAB",
    #           "OprerativeOne_Proposal_ID__c": null
    #       }
    #   ]
    # }
    def get_operative_one_ids(opportunity_ids)
      ary = []
      opportunity_ids.each_slice(40) do |oids_slice|
        opportunity_ids_query_string = oids_slice.map { |oid| "'#{oid}'" }.join(',')
        # rubocop:disable Layout/LineLength
        response = get("/services/data/v58.0/query/?q=select+Id,+OprerativeOne_Proposal_ID__c+from+opportunity+where+Id+in+(#{opportunity_ids_query_string})+and+OprerativeOne_Proposal_ID__c+!=null")
        # rubocop:enable Layout/LineLength
        body = JSON.parse(response.body)
        body['records'].each do |r|
          ary << { opportunity_id: r['Id'], operative_one_id: r['OprerativeOne_Proposal_ID__c'] }
        end
      end
      ary
    end

    def restforce
      return @restforce if @restforce

      @restforce = Restforce.new(
        oauth_token: @access_token,
        instance_url: @instance_url,
        adapter: :net_http
      )
      @restforce
    end

    private

    def fetch_token
      payload = {
        grant_type: 'password',
        client_id: ENV['SF_CLIENT_ID'],
        client_secret: ENV['SF_CLIENT_SECRET'],
        username: ENV['SF_USERNAME'],
        password: ENV['SF_PASSWORD']
      }
      response = post_form(ENV['SF_TOKEN_URL'], payload)
      body = JSON.parse(response.body)
      @instance_url = body['instance_url']
      @access_token = body['access_token']
    end

    def post_form(url, payload)
      uri = URI(url)
      http = Net::HTTP.new(uri.host, uri.port)
      http.use_ssl = true
      http.verify_mode = OpenSSL::SSL::VERIFY_NONE
      Net::HTTP.post_form(uri, payload)
    end

    def get(route)
      uri = URI("#{instance_url}#{route}")
      Rails.logger.info(uri)
      http = Net::HTTP.new(uri.host, uri.port)
      http.use_ssl = true
      http.verify_mode = OpenSSL::SSL::VERIFY_NONE
      request = Net::HTTP::Get.new(uri)
      request['Authorization'] = "Bearer #{access_token}"
      http.request(request)
    end
  end
end
