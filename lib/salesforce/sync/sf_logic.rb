# frozen_string_literal: true

module Salesforce
  module Sync
    class SfLogic
      # Establishes a connection to Salesforce using the Salesforce::Client
      def connect_sf
        lib_sf_client = Salesforce::Client.new
        @rst = lib_sf_client.restforce
      end

      # Returns the Restforce client instance
      attr_reader :rst

      # Executes a SOQL count query and returns the result
      # @param soql [String] The SOQL query to execute
      # @return [Integer, nil] The count result or nil if an error occurs
      def count_by_soql(soql)
        result = @rst.query(soql)
        return result.first.expr0 if result.any?

        puts 'No records found or count is zero.'
        0
      rescue Restforce::Errors::AuthenticationError => e
        puts "Authentication failed: #{e.message}"
        nil
      rescue Restforce::Errors::ApiError => e
        puts "API error occurred: #{e.message}"
        nil
      rescue StandardError => e
        puts "An error occurred: #{e.message}"
        nil
      end

      # Executes a SOQL query and returns the results as an array of hashes
      # @param soql [String] The SOQL query to execute
      # @return [Array<Hash>] An array of result records as hashes
      def query_by_soql(soql)
        result = @rst.query(soql)
        result.map(&:to_hash)
      end

      # Retrieves Salesforce Industry__c records
      # @param sf_id [String, nil] Optional Salesforce ID to fetch a specific record
      # @param limit [Integer] Maximum number of records to retrieve
      # @return [Array<Hash>] An array of Industry__c records
      def get_sf_industries(sf_id = nil, limit = 10)
        query = <<-SOQL
          SELECT
              Id,
              Name,
              IsDeleted,
              PAM_Processing_Flag__c
          FROM
              Industry__c
          WHERE
        SOQL
        if sf_id
          query += " ID = '#{sf_id}' " if sf_id
        else
          query += " PAM_Processing_Flag__c = 'unprocessed' "
        end
        query += " LIMIT #{limit} " if limit
        query_by_soql(query)
      end

      # Retrieves Salesforce Account records of type 'client' (Advertisers)
      # @param sf_id [String, nil] Optional Salesforce ID to fetch a specific record
      # @param limit [Integer] Maximum number of records to retrieve
      # @return [Array<Hash>] An array of Account records
      def get_sf_advertisers(sf_id = nil, limit = 10)
        query = <<-SOQL
          SELECT
              Id,
              Name,
              Industry__c,
              Industry_Name__c,
              isActive__c,
              BillingState,
              Target_Account__c,
              D2C_Target_Account__c,
              Global_Account__c,
              Peacock_Target_Account__c,
              Target_Classification__c,
              NBCU_Client_Lead__c,
              PAM_Processing_Flag__c
          FROM
              Account
          WHERE
              RecordType.Name IN ('client')
              AND Industry__c != NULL
        SOQL
        if sf_id
          query += " AND ID = '#{sf_id}' " if sf_id
        else
          query += " AND PAM_Processing_Flag__c = 'unprocessed' "
        end
        query += ' ORDER By CreatedDate DESC '
        query += " LIMIT #{limit} " if limit
        query_by_soql(query)
      end

      # Retrieves Salesforce Account records of type 'agency'
      # @param sf_id [String, nil] Optional Salesforce ID to fetch a specific record
      # @param limit [Integer] Maximum number of records to retrieve
      # @return [Array<Hash>] An array of Account records
      def get_sf_agencies(sf_id = nil, limit = 10)
        query = <<-SOQL
          SELECT
              Id,
              Name,
              isActive__c,
              ParentAgency__c,
              IsParent__c,
              PAM_Processing_Flag__c
          FROM
              Account
          WHERE
              RecordType.Name IN ('agency')
        SOQL
        if sf_id
          query += " AND ID = '#{sf_id}' " if sf_id
        else
          query += " AND PAM_Processing_Flag__c = 'unprocessed' "
        end
        query += ' ORDER By CreatedDate DESC '
        query += " LIMIT #{limit} "
        query_by_soql(query)
      end

      # Updates the PAM_Processing_Flag__c for a Salesforce Industry__c record
      # @param sf_industry [Hash] The Salesforce Industry__c record
      # @param status [String] The new status to set
      def sf_industry_status(sf_industry, status)
        sf_id = sf_industry['Id']
        @rst.update!('Industry__c', Id: sf_id, PAM_Processing_Flag__c: status)
      end

      # Updates the PAM_Processing_Flag__c for a Salesforce Account record (Advertiser)
      # @param sf_advertiser [Hash] The Salesforce Account record
      # @param status [String] The new status to set
      def sf_advertiser_status(sf_advertiser, status)
        sf_id = sf_advertiser['Id']
        @rst.update!('Account', Id: sf_id, PAM_Processing_Flag__c: status)
      end

      # Updates the PAM_Processing_Flag__c for a Salesforce Account record (Agency)
      # @param sf_agency [Hash] The Salesforce Account record
      # @param status [String] The new status to set
      def sf_agency_status(sf_agency, status)
        sf_id = sf_agency['Id']
        @rst.update!('Account', Id: sf_id, PAM_Processing_Flag__c: status)
      end

      # Updates a Salesforce Industry__c record with the given attributes
      # @param industry_id [String] The Salesforce ID of the Industry__c record
      # @param attributes [Hash] The attributes to update
      # @return [Boolean] True if the update was successful, false otherwise
      def sf_update_industry(industry_id, attributes)
        result = @rst.update!('Industry__c', attributes.merge(Id: industry_id))
        if result
          puts "Successfully updated Industry__c record: #{industry_id}"
          true
        else
          puts "Failed to update Industry__c record: #{industry_id}"
          false
        end
      rescue Restforce::Error => e
        puts "Error updating Industry__c record #{industry_id}: #{e.message}"
        false
      end
    end
  end
end
