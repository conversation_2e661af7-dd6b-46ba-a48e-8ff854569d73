# frozen_string_literal: true

require_relative 'sync_config'

module Salesforce
  module Sync
    class MainLogic

      # Initializes the MainLogic class
      # Sets up PamLogic and SfLogic instances and connects to Salesforce
      def initialize
        @pam = PamLogic.new
        @sf = SfLogic.new
        @sf.connect_sf
      end

      # Main method to run the synchronization process
      # Processes industries, agencies, and advertisers
      def run
        puts 'Starting Salesforce sync process'
        process_industries
        process_agencies
        process_advertisers
      end

      # Processes Salesforce industries and syncs them to PAM categories
      def process_industries
        sf_industries = @sf.get_sf_industries(nil, MAX_RECORDS)
        puts "processing #{sf_industries.count} industries"
        sf_industries.each do |sf_industry|
          @sf.sf_industry_status(sf_industry, 'pickedup')
          begin
            process_sf_industry(sf_industry)
            @sf.sf_industry_status(sf_industry, 'processed')
          rescue StandardError => e
            BacktraceHelper.print_exception(e, "Error processing industry #{sf_industry['Id']}")
          end
        end
      end

      # Processes Salesforce agencies and syncs them to PAM agencies
      def process_agencies
        sf_agencies = @sf.get_sf_agencies(nil, SyncConfig.max_parent_records)
        puts "processing #{sf_agencies.count} agencies"
        sorted_agencies = order_agencies(sf_agencies)

        # Take only the first MAX_RECORDS after sorting
        agencies_to_process = sorted_agencies.first(SyncConfig.max_records)
        agencies_to_process.each do |sf_agency|
          @sf.sf_agency_status(sf_agency, 'pickedup')
          begin
            process_sf_agency(sf_agency)
            @sf.sf_agency_status(sf_agency, 'processed')
          rescue StandardError => e
            BacktraceHelper.print_exception(e, "Error processing agencies #{sf_agency['Id']}")
          end
        end
      end

      # Processes Salesforce advertisers and syncs them to PAM advertisers
      def process_advertisers
        sf_advertisers = @sf.get_sf_advertisers(nil, SyncConfig.max_records)
        puts "processing #{sf_advertisers.count} advertisers"
        sf_advertisers.each do |sf_advertiser|
          @sf.sf_advertiser_status(sf_advertiser, 'pickedup')
          begin
            process_sf_advertiser(sf_advertiser)
            @sf.sf_advertiser_status(sf_advertiser, 'processed')
          rescue StandardError => e
            BacktraceHelper.print_exception(e, "Error processing advertiser #{sf_advertiser['Id']}")
          end
        end
      end

      # Sorts agencies by their dependecies
      # @param agencies [Array<Hash>] List of Salesforce agencies
      # @return [Array<Hash>] Sorted list of agencies
      def order_agencies(agencies)
        # Create a map of agency IDs for quick lookup
        agency_ids_in_batch = agencies.map { |agency| agency['Id'] }.to_set

        # Sort agencies in dependency order (parents before children)
        sorted_agencies = []
        remaining_agencies = agencies.dup

        # Keep processing until all agencies are sorted
        while remaining_agencies.any?
          # Find agencies whose parents are either not in the batch or already processed
          ready_agencies = remaining_agencies.select do |agency|
            parent_id = agency['ParentAgency__c']
            parent_id.nil? || parent_id.empty? ||
              !agency_ids_in_batch.include?(parent_id) ||
              sorted_agencies.any? { |sorted| sorted['Id'] == parent_id }
          end

          # If no agencies are ready, we have a circular dependency - just take the first one
          if ready_agencies.empty?
            ready_agencies = [remaining_agencies.first]
          end

          # Add ready agencies to sorted list and remove from remaining
          sorted_agencies.concat(ready_agencies)
          remaining_agencies -= ready_agencies
        end

        # Assign agency types after sorting
        sorted_agencies.each do |agency|
          if agency['IsParent__c']
            agency['agency_type'] = 'ParentAgency'
          else
            agency['agency_type'] = 'Agency'
          end
        end

        sorted_agencies
      end

      private

      # Validates parent assignment to prevent circular relationships and 3+ level hierarchies
      # @param child_pam_id [Integer] The child agency PAM ID
      # @param parent_pam_id [Integer] The parent agency PAM ID (-1 for no parent)
      # @return [Boolean] True if assignment is valid
      def validate_parent_assignment(child_pam_id, parent_pam_id)
        return true if parent_pam_id == -1 # No parent is always valid

        # Find the parent agency
        parent_agency = BaseAgency.find_by(agency_id: parent_pam_id)
        return false unless parent_agency

        # Check for circular relationship
        if would_create_circular_relationship?(child_pam_id, parent_pam_id)
          puts "VALIDATION: Circular relationship detected - child #{child_pam_id} -> parent #{parent_pam_id}"
          return false
        end

        # Check for 4+ level hierarchy (PAM allows max 3 levels)
        if would_create_deep_hierarchy?(child_pam_id, parent_pam_id)
          puts "VALIDATION: Would create 4+ level hierarchy - blocked"
          return false
        end

        true
      end

      # Checks if assigning parent would create circular relationship (using PAM IDs)
      def would_create_circular_relationship?(child_pam_id, parent_pam_id)
        current_agency_id = parent_pam_id
        visited = Set.new

        while current_agency_id && current_agency_id != -1
          return true if visited.include?(current_agency_id)

          # Check if current agency ID matches child ID
          return true if current_agency_id == child_pam_id

          visited.add(current_agency_id)
          parent_agency = BaseAgency.find_by(agency_id: current_agency_id)
          current_agency_id = parent_agency&.parent_agency_id
        end

        false
      end

      # Checks if assigning parent would create hierarchy deeper than 3 levels (using PAM IDs)
      def would_create_deep_hierarchy?(child_pam_id, parent_pam_id)
        # Calculate depth going UP from parent (how many ancestors)
        depth_up = calculate_depth_up(parent_pam_id)

        # Calculate depth going DOWN from child (how many descendants)
        depth_down = calculate_depth_down(child_pam_id)

        # Total depth: ancestors + parent + child + descendants
        # depth_up + 1 (parent) + 1 (child) + depth_down
        total_depth = depth_up + 1 + 1 + depth_down

        total_depth > 3  # Block if would create more than 3 levels
      end

      # Calculate how many levels UP from an agency (ancestors)
      def calculate_depth_up(agency_id)
        depth = 0
        current_id = agency_id
        visited = Set.new

        while current_id && current_id != -1
          return 999 if visited.include?(current_id)  # Circular detected
          visited.add(current_id)

          agency = BaseAgency.find_by(agency_id: current_id)
          break unless agency&.parent_agency_id && agency.parent_agency_id != -1

          depth += 1
          current_id = agency.parent_agency_id
        end

        depth
      end

      # Calculate how many levels DOWN from an agency (descendants)
      def calculate_depth_down(agency_id, visited = Set.new)
        return 999 if visited.include?(agency_id)  # Circular detected
        visited.add(agency_id)

        max_depth = 0
        children = BaseAgency.where(parent_agency_id: agency_id)

        children.each do |child|
          child_depth = 1 + calculate_depth_down(child.agency_id, visited.dup)
          max_depth = [max_depth, child_depth].max
        end

        max_depth
      end

      # Handles type promotions when assigning parent relationships
      def handle_type_promotions(parent_agency)
        return unless parent_agency

        # Promote direct parent: Agency -> ParentAgency (always allowed)
        if parent_agency.agency_type == 'Agency'
          parent_agency.agency_type = 'ParentAgency'
          parent_agency.save!
          puts "Promoted agency #{parent_agency.agency_id} from 'Agency' to 'ParentAgency'"
        end

        # Promote grandparent: ParentAgency -> GrandParentAgency
        if parent_agency.parent_agency_id && parent_agency.parent_agency_id != -1
          grandparent_agency = BaseAgency.find_by(agency_id: parent_agency.parent_agency_id)
          if grandparent_agency && grandparent_agency.agency_type == 'ParentAgency'
            if !SyncConfig.cutoff?(grandparent_agency)
              grandparent_agency.agency_type = 'GrandParentAgency'
              grandparent_agency.save!
              puts "Promoted agency #{grandparent_agency.agency_id} from 'ParentAgency' to 'GrandParentAgency'"
            else
              SyncConfig.log_protection_block(grandparent_agency, 'type promotion to GrandParentAgency', 'past cutoff period')
            end
          end
        end
      end

      # Processes a single Salesforce industry
      # @param sf_industry [Hash] Salesforce industry data
      def process_sf_industry(sf_industry)
        cls = Category
        @pam.fetch_or_create_pam_obj(cls, sf_industry['Id'], sf_industry['Name'],
                                     create_pamobj: true, update_pamobj: true) do |record|
          record.product_category_name = sf_industry['Name']
        end
      end

      # Processes a single Salesforce agency
      # @param sf_agency [Hash] Salesforce agency data
      def process_sf_agency(sf_agency)
        parent_agency = @pam.fetch_or_create_pam_obj(BaseAgency, sf_agency['ParentAgency__c'], nil,
                                                     create_pamobj: true, update_pamobj: false) do |record|
          # This is to handle pre-existing differences between Salesforce and PAM, so that the sync is more robust.
          # theoretically we shoudn't need to create this parent agency, because it should already exist.
          # if we ever need to create it here, we don't worry about its parent.
          # We can periodically review if there are agencies with parent -1, and manually modify them if needed.
          puts "Warning, shouldn't need. " \
               "Creating parent agency #{sf_agency['ParentAgency__c']} for agency #{sf_agency['Id']}"
          sf_parent_agency = @sf.get_sf_agencies(sf_agency['ParentAgency__c']).first
          record.agency_name = sf_parent_agency['Name']
          record.parent_agency_id = -1
          record.agency_type = 'ParentAgency'
          record.active = true if record.new_record?
        end

        # Handle type promotions when assigning parent relationship
        handle_type_promotions(parent_agency)

        # Get or create the child agency first
        child_agency = @pam.fetch_or_create_pam_obj(BaseAgency, sf_agency['Id'], sf_agency['Name'],
                                                    create_pamobj: true, update_pamobj: true) do |record|
          # Get existing record to check protections
          existing_agency = record.persisted? ? record : nil
          new_parent_id = parent_agency&.agency_id || -1

          # Apply protection rules for existing agencies
          if existing_agency && existing_agency.agency_type != 'Agency' && SyncConfig.cutoff?(existing_agency)
            # Protected agency - only allow certain changes
            puts "PROTECTED: Agency #{existing_agency.agency_id} (#{existing_agency.agency_type}) is outside cutoff period"

            # Keep existing name, parent, and type
            record.agency_name = existing_agency.agency_name if record.agency_name != sf_agency['Name']
            record.parent_agency_id = existing_agency.parent_agency_id if record.parent_agency_id != new_parent_id
            record.agency_type = existing_agency.agency_type if record.agency_type != sf_agency['agency_type']

            SyncConfig.log_protection_block(existing_agency, 'all changes blocked', 'outside cutoff period')
          else
            # New agency or within cutoff - allow changes
            record.agency_name = sf_agency['Name']
            record.parent_agency_id = new_parent_id
            record.agency_type = sf_agency['agency_type']

            if existing_agency
              puts "Updated agency #{existing_agency.agency_id}: name, parent, type (within cutoff or regular agency)"
            end
          end

          record.active = true if record.new_record?
        end

        # Validate parent assignment after both agencies exist (using PAM IDs)
        new_parent_id = parent_agency&.agency_id || -1
        if new_parent_id != -1 && !validate_parent_assignment(child_agency.agency_id, new_parent_id)
          puts "BLOCKED: Invalid parent assignment for agency #{child_agency.agency_id} -> parent #{new_parent_id}"
          puts "Reverting parent assignment to -1"
          child_agency.parent_agency_id = -1
          child_agency.save!
        end
      end

      # Processes a single Salesforce advertiser
      # @param sf_advertiser [Hash] Salesforce advertiser data
      def process_sf_advertiser(sf_advertiser)
        category = @pam.fetch_or_create_pam_obj(Category, sf_advertiser['Industry__c'],
                                                sf_advertiser['Industry_Name__c'],
                                                create_pamobj: true, update_pamobj: false) do |record|
          record.product_category_name = sf_advertiser['Industry_Name__c']
        end
        geo_state = @pam.fetch_geo_state(sf_advertiser['BillingState'])

        @pam.fetch_or_create_pam_obj(Advertiser, sf_advertiser['Id'], sf_advertiser['Name'],
                                     create_pamobj: true, update_pamobj: true) do |record|
          record.advertiser_name = sf_advertiser['Name']
          record.default_category = category
          record.main_state_id = geo_state.geo_state_id
          record.target_account = convert_boolean(sf_advertiser['Target_Account__c'])
          record.d2c_target_account = convert_boolean(sf_advertiser['D2C_Target_Account__c'])
          record.global_account = convert_boolean(sf_advertiser['Global_Account__c'])
          record.peacock_target_account = convert_boolean(sf_advertiser['Peacock_Target_Account__c'])
          record.active = true if record.new_record?
        end
      end

      # Converts a Salesforce boolean value to a Ruby boolean
      # @param value [String, Boolean] The value to convert
      # @return [Boolean] The converted boolean value
      def convert_boolean(value)
        return value if value.is_a?(TrueClass) || value.is_a?(FalseClass)

        %w[true 1 yes].include?(value.to_s.downcase.strip)
      end
    end
  end
end
