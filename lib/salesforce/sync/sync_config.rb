# frozen_string_literal: true

module Salesforce
  module Sync
    class SyncConfig
      # Sync processing limits
      def self.max_records
        ENV.fetch('SF_SYNC_MAX_RECORDS', '40').to_i
      end

      def self.max_parent_records
        ENV.fetch('SF_SYNC_MAX_PARENT_RECORDS', '500').to_i
      end

      # Agency protection cutoff period (in days)
      def self.protection_cutoff_days
        ENV.fetch('AGENCY_PROTECTION_CUTOFF_DAYS', '10').to_i
      end

      # Cutoff check - returns true if agency is past cutoff (can't be modified)
      def self.cutoff?(agency)
        return false if agency.nil?
        agency.created_at <= protection_cutoff_days.days.ago
      end

      # Logging helper
      def self.log_protection_block(agency, change_type, reason = nil)
        agency_info = "Agency ID: #{agency&.agency_id}, Type: #{agency&.agency_type}, Created: #{agency&.created_at}"
        reason_text = reason ? " (#{reason})" : ""
        puts "PROTECTION: Blocked #{change_type} for #{agency_info}#{reason_text}"
      end
    end
  end
end
