# frozen_string_literal: true

module Salesforce
  module Sync
    class PamLogic
      # Constants for external system and key type identifiers
      EXTERNAL_SYSTEM_ID = 1 # sfdc (Salesforce)
      SYSTEM_KEY_ASC_TYPE = 2

      # Mapping of PAM objects to their external key types and name fields
      EXTERNAL_KEY_TYPE_MAP = {
        Category => %w[Category product_category_name],
        Advertiser => %w[Advertiser advertiser_name],
        BaseAgency => %w[Agency agency_name],
        Agency => %w[Agency agency_name],
        ParentAgency => %w[Agency agency_name],
        GrandParentAgency => %w[Agency agency_name],
        TargetClassification => ['Target Account Classification', 'target_classification_name'],
        GeoState => ['Geo State', 'geo_state_name']
      }.freeze

      # Fetches the external key type for a given PAM object
      # @param pam_obj [ActiveRecord::Base] The PAM object
      # @return [ExternalKeyType] The corresponding external key type
      def fetch_external_key_type(pam_obj)
        external_key_type_name, = EXTERNAL_KEY_TYPE_MAP[pam_obj.class]
        ExternalKeyType.find_by_external_key_type_name(external_key_type_name)
      end

      # Creates a new external key association for a PAM object
      # @param pam_obj [ActiveRecord::Base] The PAM object
      # @param sf_id [String] The Salesforce ID
      def create_ext_key(pam_obj, sf_id)
        puts("create_ext_key(#{pam_obj.class}, #{sf_id})")
        external_key_type = fetch_external_key_type(pam_obj)

        sys_key = SystemKeyAssociation.new do |r|
          r.external_key_type = external_key_type
          r.external_system_id = EXTERNAL_SYSTEM_ID
          r.system_key_asc_type_id = SYSTEM_KEY_ASC_TYPE
          r.external_system_key = sf_id
          r.pam_key = pam_obj.id.to_s
        end
        sys_key.save!
      end

      # Fetches the PAM key for a given Salesforce ID and key type
      # @param sf_id [String] The Salesforce ID
      # @param key_type [Class, String] The key type (either a class or a string)
      # @return [String, nil] The PAM key if found, nil otherwise
      def fetch_pam_key_for_sf_id(sf_id, key_type)
        key_type, = EXTERNAL_KEY_TYPE_MAP[key_type] if key_type.is_a?(Class)
        query = SystemKeyAssociation.joins(:external_key_type)
                                    .where(external_key_type: { external_key_type_name: key_type })
                                    .where(external_system_id: EXTERNAL_SYSTEM_ID)
                                    .where(external_system_key: sf_id)
                                    .select(:pam_key)
                                    .limit(1)
        query.pluck(:pam_key).first
      end

      # Fetches the GeoState object for a given Salesforce billing state
      # @param sf_billing_state [String] The billing state from Salesforce
      # @return [GeoState] The corresponding GeoState object
      def fetch_geo_state(sf_billing_state)
        return GeoState.find(-1) if sf_billing_state.nil? || sf_billing_state.strip.empty?

        normalized_state = sf_billing_state.strip.downcase
        GeoState.where('lower(geo_state_code) = ?', normalized_state).first ||
          GeoState.where('lower(geo_state_name) = ?', normalized_state).first ||
          GeoState.find(-1)
      end

      # Fetches or creates a PAM object based on Salesforce data
      # @param cls [Class] The PAM object class
      # @param sf_id [String] The Salesforce ID
      # @param sf_value [String, nil] The Salesforce value (usually a name)
      # @param create_pamobj [Boolean] Whether to create a new PAM object if not found
      # @param update_pamobj [Boolean] Whether to update an existing PAM object
      # @return [ActiveRecord::Base] The fetched or created PAM object
      def fetch_or_create_pam_obj(cls, sf_id, _sf_value = nil, create_pamobj: false, update_pamobj: false)
        pam_obj_type, = EXTERNAL_KEY_TYPE_MAP[cls]

        return cls.find(-1) if sf_id.nil? || sf_id.strip.empty?

        norm_sf_id = sf_id.strip
        log_prefix = "#{cls} sf_id(#{norm_sf_id})"

        pam_key = fetch_pam_key_for_sf_id(norm_sf_id, cls)

        if pam_key
          puts "#{log_prefix}: pam key #{pam_key} found in system_key_asc"
          pk_name = "#{cls.table_name}_id"
          pam_obj = cls.find_by(pk_name => pam_key)

          raise "#{log_prefix}: No pam obj found for pam_key #{pam_key}. Data inconsistency issue." unless pam_obj

          if update_pamobj && should_update?(pam_obj)
            puts "Updating #{log_prefix}"
            yield pam_obj if block_given?

            if pam_obj.changed?
              save_pamobj(pam_obj)
            else
              puts "#{log_prefix}: no change detected"
            end
          end

        elsif create_pamobj
          puts "#{log_prefix}: system_key_asc record not found"
          pam_obj = cls.new

          yield pam_obj if block_given?

          save_pamobj(pam_obj)

          create_ext_key(pam_obj, norm_sf_id)
          puts "#{log_prefix}: No syskey found, syskey(#{pam_obj_type}) created"
        else
          raise "#{log_prefix}: No matching obj found and create_pamobj flag is off"
        end

        pam_obj
      end

      # Determines if a PAM object should be updated
      # @param pam_obj [ActiveRecord::Base] The PAM object
      # @return [Boolean] True if the object should be updated, false otherwise
      def should_update?(pam_obj)
        !pam_obj.is_a?(BaseAgency) || !%w[ParentAgency GrandParentAgency].include?(pam_obj.agency_type)
      end

      # Finds or creates a PAM object based on Salesforce data
      # @param cls [Class] The PAM object class
      # @param norm_sf_id [String] The normalized Salesforce ID
      # @param sf_value [String] The Salesforce value (usually a name)
      # @param name_field [String] The name field of the PAM object
      # @return [ActiveRecord::Base] The found or created PAM object
      def find_or_create_pam_obj(cls, norm_sf_id, sf_value, name_field)
        log_prefix = "#{cls} sf_id(#{norm_sf_id})"

        if sf_value
          norm_sf_value = sf_value.strip
          pam_obj = cls_find_by_name(cls, name_field, norm_sf_value)

          if pam_obj
            puts "#{log_prefix}: pam_obj found based on sf_name #{norm_sf_value}"
          else
            puts "#{log_prefix}: pam_obj not found based on sf_name #{norm_sf_value}"
            pam_obj = create_new_pam_obj(cls, log_prefix)
          end
        else
          pam_obj = create_new_pam_obj(cls, log_prefix)
        end

        pam_obj
      end

      # Creates a new PAM object
      # @param cls [Class] The PAM object class
      # @param log_prefix [String] The log prefix for debugging
      # @return [ActiveRecord::Base] The newly created PAM object
      def create_new_pam_obj(cls, log_prefix)
        puts "#{log_prefix}: Creating new pam_obj"
        cls.new
      end

      # Returns the name field for a given PAM object class
      # @param cls [Class] The PAM object class
      # @return [String] The name field
      def cls_name_field(cls)
        "#{cls.name.demodulize.underscore}_name"
      end

      # Finds a PAM object by name
      # @param cls [Class] The PAM object class
      # @param name_fields [String, Array<String>] The name field(s) to search
      # @param name [String] The name to search for
      # @return [ActiveRecord::Base, nil] The found PAM object or nil
      def cls_find_by_name(cls, name_fields, name)
        if name_fields.is_a?(Array)
          conditions = name_fields.map { |field| "#{field} = :name" }.join(' OR ')
          cls.where(conditions, name:).first
        else
          cls.where("#{name_fields} = ?", name).first
        end
      end

      # Sets a field value on a PAM object
      # @param obj [ActiveRecord::Base] The PAM object
      # @param field [String] The field name
      # @param value [Object] The value to set
      def cls_set_field(obj, field, value)
        obj.send(:"#{field}=", value)
      end

      # Saves a PAM (Pamadmin) object and prints the result
      # @param pam_obj [ActiveRecord::Base] The PAM object to save
      def save_pamobj(pam_obj)
        pk_name = pam_obj.class.primary_key
        original_pk_value = pam_obj.send(pk_name)
        is_new_record = pam_obj.new_record?

        # For new records, we want all attributes except created_at and updated_at
        # For existing records, we only want the changes
        changes = is_new_record ? pam_obj.attributes.except('created_at', 'updated_at') : pam_obj.changes
        save_succeeded = false
        error_message = nil

        begin
          pam_obj.save!
          save_succeeded = true
        rescue ActiveRecord::RecordInvalid => e
          error_message = e.message
          raise # Re-raise the exception after capturing the error message
        ensure
          # Always print the record, whether the save succeeded or not
          output = print_record(pam_obj, changes, is_new_record, original_pk_value, save_succeeded, error_message)
          puts output
        end
      end

      # Generates a formatted string representation of a PAM object's save operation
      # @param pam_obj [ActiveRecord::Base] The PAM object
      # @param changes [Hash] The changes made to the object
      # @param is_new_record [Boolean] Whether this is a new record
      # @param original_pk_value [String] The original primary key value
      # @param save_succeeded [Boolean] Whether the save operation succeeded
      # @param error_message [String, nil] Any error message from a failed save
      # @return [String] Formatted string representation of the save operation
      # rubocop:disable Metrics/PerceivedComplexity, Metrics/ParameterLists
      def print_record(pam_obj, changes, is_new_record, original_pk_value, save_succeeded, error_message)
        output = []
        output << '--------------'
        action_str = is_new_record ? 'Creating' : 'Updating'
        output << "#{action_str} record of type: #{pam_obj.class}"
        output << '--------------'

        pk_name = pam_obj.class.primary_key
        current_pk_value = pam_obj.send(pk_name)

        # Print primary key information
        output << if save_succeeded
                    if is_new_record
                      "#{pk_name}: #{current_pk_value} (new record)"
                    elsif original_pk_value != current_pk_value
                      "#{pk_name}: #{original_pk_value} -> #{current_pk_value}"
                    else
                      "#{pk_name}: #{current_pk_value}"
                    end
                  else
                    "#{pk_name}: #{original_pk_value || 'nil'} (save failed)"
                  end

        # Print changes to fields
        changes.each do |field, value|
          # rubocop:disable Performance/CollectionLiteralInLoop
          next if %w[created_at updated_at].include?(field)
          # rubocop:enable Performance/CollectionLiteralInLoop

          if is_new_record
            output << "#{field}: #{value.inspect}"
          else
            old_value, new_value = value
            output << "#{field}: #{old_value.inspect} -> #{new_value.inspect}"
          end
        end

        # Print error message if save failed
        output << "Error: #{error_message}" if error_message

        output << '--------------'
        output << (save_succeeded ? 'Save succeeded' : 'Save failed')

        output.join("\n")
      end
      # rubocop:enable Metrics/PerceivedComplexity, Metrics/ParameterLists
    end
  end
end
