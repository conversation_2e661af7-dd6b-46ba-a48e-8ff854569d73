# frozen_string_literal: true

namespace :data do
  desc 'Refresh all finance actual records with current historical quarterly data'
  task refresh_finance_actuals: :environment do
    raise 'Need to pass calendar_year_id' unless (calendar_year_id = ARGV[1])

    [1, 2, 3, 4].each do |quarter|
      unless PamClient::FinanceMonth.find_by(historical_finance_quarter: quarter)
        raise "Missing finance_historical_quarter #{quarter}"
      end
    end

    [
      PamClient::FinanceRevenue,
      PamClient::FinanceCpm,
      PamClient::FinanceMakeGood,
      PamClient::FinanceRatingImpression,
      PamClient::FinanceRiskOpportunity,
      PamClient::FinanceUnit
    ].each do |klass|
      klass.refresh_actual_data(calendar_year_id: calendar_year_id)
    end
  end
end
