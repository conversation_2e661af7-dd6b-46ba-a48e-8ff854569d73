# frozen_string_literal: true

namespace :deploy do
  desc 'deploy app to specified PCF environment/host. usage: `deploy:pcf[dev,devsysush]`'
  task :pcf, %i[env host app_name] do |_t, args|
    Rake::Task['deploy:tag_version'].invoke
    sh "cf login -a https://api.#{args.host}.inbcu.com -u #{ENV['PCF_USER']} -p #{ENV['PCF_PASSWORD']} -o Ad-Sales -s Software-Eng-pam-api-#{args.env}"

    push_options = if args.app_name
      "#{args.app_name} -f manifest.feature.yml --hostname #{args.app_name}"
    else
      "-f manifest.#{args.env}.yml"
    end

    sh "cf push #{push_options}"
  end

  desc 'Tag current SHA to pam_api_version'
  task :tag_version do
    require 'version_tagger'
    VersionTagger.tag
  end
end
