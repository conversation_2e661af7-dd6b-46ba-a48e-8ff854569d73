# frozen_string_literal: true

namespace :db do
  namespace :seed do

    desc 'seed PAM data'
    task :pam, [:type] => :environment do |_, args|
      args.type == 'live' ? PamSeed.seed_live : PamSeed.seed_local
    end

    module PamSeed
      module_function

      PAM_SEED_TABLES = %w[
        advertiser
        agency
        budget
        budget_year
        calendar_year
        deal
        marketplace
        not_returning_reason
        opportunity
        opportunity_status
        pacing_budget
        pacing_budget_detail
        property
        quarter
        sales_type
        user
        vertical
      ].freeze

      def seed_local
        PAM_SEED_TABLES.each do |table|
          begin
            p "seeding #{table} from LOCAL"
            Rake::Task["db:seed:pam:#{table}"].invoke
          rescue RuntimeError => e
            fail e unless e.message =~ /Don't know how to build task/
            p "no local data for #{table}"
          end
        end
      end

      def seed_live
        start = Time.now
        environment_connection = ActiveRecord::Base.connection_config

        pam_connection = {
          adapter:  'oracle_enhanced',
          host:     '***********',
          username: 'SMSDBO',
          password: 'dbosms_11g_d426',
          port:     16_617,
          database: '/d533.tfayd.com'
        }

        toggle_constraints('disable', PAM_SEED_TABLES)

        PAM_SEED_TABLES.each do |pam_table|
          p "seeding #{pam_table} from DEV"

          model = "PamClient::#{pam_table.downcase.camelize}".constantize
          columns = model.column_names

          inserts = execute_with_pam(pam_connection, environment_connection) { model.select(columns).to_a }

          fail if ActiveRecord::Base.connection_config[:host] != 'localhost'
          p "#{inserts.size} total records"

          model.transaction do
            model.clear_validators!
            model.delete_all
            inserts.each do |insert|
              begin
                model.create(insert.attributes)
              rescue => e
                p e
              end
            end
          end

          p "Finished importing #{pam_table}"
        end

        p "Completed in #{(Time.now - start) / 60} minutes"
      end

      def execute_with_pam(connection, original_connection)
        ActiveRecord::Base.establish_connection(connection)
        yield
      ensure
        ActiveRecord::Base.establish_connection(original_connection)
      end

      def toggle_constraints(toggle, tables)
        ActiveRecord::Base.connection.execute(<<-SQL)
        BEGIN
          FOR r IN (SELECT table_name, constraint_name
                    FROM user_constraints
                    WHERE constraint_type = 'R') --AND TABLE_NAME IN (#{tables.map { |t| "'#{t}'" }.join(',')}))
            LOOP
              EXECUTE IMMEDIATE 'alter table '|| r.table_name ||' #{toggle} constraint '|| r.constraint_name;
            END LOOP;
          END LOOP;
        SQL
      end
    end
  end
end
