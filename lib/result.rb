# frozen_string_literal: true

# Class based (strongly) on the `Github::Result` class from the `github-ds`
# gem.  Didn't want to import entire `github-ds` gem because it has a lot of
# things in there we don't want.
#
# @see https://johnnunemaker.com/resilience-in-ruby/
# @see https://github.com/github/github-ds
# @see https://github.com/github/github-ds/blob/master/examples/result.rb
class Result
  attr_reader :error

  def initialize
    @error = false
    begin
      @value = yield
      @error = nil
    rescue StandardError => e
      @error = e
    end
  end

  def ok?
    !@error
  end

  def then
    return self unless ok?

    Result.new do
      yield(@value)
    end
  end

  def rescue
    return self if ok?

    Result.new do
      yield(@error)
    end
  end
end
