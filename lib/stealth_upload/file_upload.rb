# frozen_string_literal: true

module StealthUpload
  # rubocop:disable Metrics/ClassLength
  class FileUpload
    include ActiveModel::Model
    include ActiveModel::Validations

    attr_reader :input
    attr_reader :current_user
    attr_reader :ruleset

    validates_presence_of :ruleset, message: 'can\'t be blank'
    validate :validate_rows
    validate :validate_data_presence

    BUDGET_CY_QUARTERLY_FIELDS = %i[
      actual_prequarter_amount
      actual_quarter1_amount actual_quarter2_amount
      actual_quarter3_amount actual_quarter4_amount
      actual_postquarter_amount
    ].freeze

    EXCEPTIONS = [
      ActiveRecord::RecordNotFound,
      NoMethodError
    ].tap do |ary|
      StealthUpload::Error.constants.map { |cls| ary << StealthUpload::Error.const_get(cls) }
    end.freeze

    def initialize(input:, parent_agency_id:, marketplace_id:, budget_year_id:, current_user:)
      @input = input
      @parent_agency_id = parent_agency_id
      @marketplace = Marketplace.find(marketplace_id)
      @budget_year = BudgetYear.find(budget_year_id)
      @current_user = current_user
      agency = BaseAgency.find(parent_agency_id)
      @ruleset = Pubsub::MappingRulesRpcClient.new(agency).call
      @updated_deal_ids = []
      @data = []

      @success_count = 0
      @row_messages = []
    end

    # Create a MappedRow for each row given in the input
    #
    # @return [Array]
    def mapped_rows
      @mapped_rows ||=
        @input.each_with_object([]) do |row, ary|
          ary << StealthUpload::Upload::MappedRow.new(row.keys, row.values, ruleset)
        rescue ArgumentError => e
          add_message(e, row)
        end.each(&:map)
    end

    def perform
      mapped_rows.each do |row|
        if row.valid?
          persist_individual(row)
          @success_count += 1
        end
      rescue StealthUpload::Error::InvalidNewSmsDealError => e
        add_message(e, row)
      rescue *EXCEPTIONS => e
        add_message(e, row)
      end
      result
    end

    def result
      rows = mapped_rows.each_with_object([]) do |row, ary|
        next if row.valid?

        ary << HashWithIndifferentAccess.new.tap do |hash|
          hash[:reason] = row.errors.full_messages
          hash.merge!(row.to_h)
        end
      end

      {
        success_count: @success_count,
        errors: rows + @row_messages
      }
    end

    private

    def add_message(err, record = {})
      Rails.logger.error(err.backtrace[0..15].join("\n\t"))
      Rails.logger.error(err)
      msg_hash = { reason: err.to_s }
      msg_hash[:class] = err.class.name if Rails.env.development?
      msg_hash.merge!(record) if record
      @row_messages << msg_hash
    end

    def persist_individual(row)
      budgets = budgets_for(row)
      allocate_registrations(row, budgets)
    end

    def budgets_for(row)
      sms_deals = sms_deals_for(row)
      unless sms_deals
        Rails.logger.error "Could not find SmsDeals for record: #{row.to_h}"
        raise StealthUpload::Error::NoSmsDealError, 'Could not find SmsDeals for record'
      end

      [].tap do |ary|
        sms_deals.each do |sms_deal|
          budget = sms_deal.budgets.where(budget_year: @budget_year).first
          raise StealthUpload::Error::NoBudgetError unless budget.present?

          # Find or create stealth budget if stealth enabled
          budget = budget.stealth_mode_budget || budget.create_stealth_mode_budget if stealth_enabled?
          ary << budget
        end
      end
    end

    def sms_deals_for(row)
      sms_deals = find_sms_deals(row)
      if sms_deals.present?
        @updated_deal_ids.concat(sms_deals.map(&:deal_id))
        return sms_deals
      end

      Rails.logger.debug "should_create_new_deal? #{ruleset.create_new_deals}"

      return if !ruleset.create_new_deals || row.direct_mapping_cells.any?

      new_sms_deal = create_new_sms_deal(row)
      @updated_deal_ids << new_sms_deal.id

      [new_sms_deal]
    end

    def find_sms_deals(row)
      int_cells = row.integration_cells
      direct_mapping_cells = row.direct_mapping_cells
      criteria = if int_cells.any?
                   { deal_id: int_cells.flat_map(&:mapped_values) }
                 elsif direct_mapping_cells.any?
                   { deal_id: direct_mapping_cells.flat_map(&:value) }
                 else
                   row.mapped_h
                 end
      criteria[:marketplace] = @marketplace

      Deal
        .joins(:budgets)
        .where.not(deal_id: @updated_deal_ids)
        .where(budget: { budget_year_id: @budget_year.id })
        .where(criteria)
    end

    def create_new_sms_deal(row)
      Rails.logger.debug "create_new_sms_deal. row: #{row.mapped_h}"
      row_hash = row.mapped_h
      @new_deal = Deal.new(row_hash)
      @new_deal.app_user_id = current_user.id
      @new_deal.rating_stream_id = row_hash[:rating_stream_id] || RatingStream.find_by(name: 'C3').id
      @new_deal.product_category_id = row_hash[:product_category_id] || -1
      @new_deal.demographic_id =
        row_hash[:demographic_id] ||
        Property.find_by(property_id: row_hash[:property_id])&.demographic_id ||
        Demographic.find_by(name: 'P18-49').id
      @new_deal.budgets.build(budget_year: @budget_year, marketplace: @marketplace, agency: @new_deal.agency)
      @new_deal.budgets.first.build_stealth_mode_budget if stealth_enabled?
      raise StealthUpload::Error::InvalidNewSmsDealError unless @new_deal.save!

      budget = stealth_enabled? ? [@new_deal.budgets.first.stealth_mode_budget] : @new_deal.budgets
      allocate_registrations(row, budget)

      @new_deal
    end

    # @description
    #   Assigns mapped registration quarter values across
    #   all budget quarters.
    #
    # @return [Hash]
    def budgets_by_quarter(record)
      budget_hash =
        ruleset
        .registration_column
        .quarter_fields
        .transform_values { |val| record[val] if val }
        .compact
        .with_indifferent_access
      budget_hash.each { |k, v| budget_hash[k] = v.to_i }
    end

    def headers
      @headers ||= mapped_rows && !mapped_rows.empty? ? mapped_rows[0].headers : []
    end

    def allocate_registrations(row, budgets)
      if row.registrations_are_by_total?
        allocate_registrations_by_total(row.registrations, budgets)
      else
        allocate_registrations_by_quarter(row, budgets)
      end
    end

    # @param total [FixNum]
    # @param budgets [Array<Budget|StealthModeBudget>]
    # @return [Array<Budget|StealthModeBudget>]
    def allocate_registrations_by_total(total, budgets)
      return [] unless budgets&.any?

      regs = py_spend_for(budgets)
      pcts = regs.map(&:to_f).map { |reg| reg.positive? ? reg * 100 / regs.sum : 0.0 }
      lr_pcts = largest_remainder(pcts)
      new_totals = lr_pcts.each_with_object([]) do |pct, ary|
        ary << (total * (pct.to_f / 100)).floor
      end
      diff = total - new_totals.sum

      if new_totals.size == 1
        new_totals[0] = total
      else
        while diff.positive?
          new_totals.each_with_index do |ttl, idx|
            new_totals[idx] = diff.positive? ? ttl + 1 : ttl
            diff -= 1
          end
        end
      end

      bgt_ttl_hash = budgets.zip(new_totals).to_h
      bgt_ttl_hash.each do |bgt, ttl|
        bgt.update!(total_to_quarters(ttl, bgt))
      end
      budgets
    end

    # @param row [MappedRow]
    # @param budgets [Array<Budget|StealthModeBudget>]
    # @return [Array<Budget|StealthModeBudget>]
    def allocate_registrations_by_quarter(row, budgets)
      registration_dollars = budgets_by_quarter(row.to_h)
      amounts =
        if budgets.size == 1
          [registration_dollars]
        else
          py_spend_sum = py_spend_for(budgets).sum
          total_remainder = 0.0

          allocated_dollars = [].tap do |ary|
            budgets.each do |budget|
              row_remainder = 0.0
              dollars =
                registration_dollars.transform_values.each do |val|
                  # If no PY Spend for any deals, split registration dollar
                  # quarter values evenly per the count of deals
                  # REG $           two       divide           Q1|Q2|Q3|Q4
                  # Q1|Q2|Q3|Q4 -> agency -> all qtrs -> Deal1  5|10|15|20
                  # 10|20|30|40    deals      by two     Deal2  5|10|15|20
                  #
                  # If PY Spend for any deals, split registration dollar
                  # quarter values based on PY Spend percentage
                  # REG $               AGENCY DEALS       PCT          Q1|Q2|Q3|Q4
                  # Q1|Q2|Q3|Q4 ->  Deal1 PY Spend $60 ->  60% -> Deal1  6|12|18|24
                  # 10|20|30|40     Deal2 PY Spend $40     40%    Deal2  4| 8|12|16
                  qtr =
                    if py_spend_sum.zero?
                      val.to_f / budgets.size
                    else
                      val.to_f * (py_spend_for([budget]).first.to_f / py_spend_sum)
                    end
                  # Truncated quarter value
                  val = qtr.truncate
                  # Keep track of remainder
                  row_remainder += qtr - val
                  return val
                end
              # Add truncated portion of row_remainder to overall total_remainder
              # and split the integer across the quarters until remainder is zero
              # i.e.: 2.4 -> .4 added to total_remainder -> 2 is split
              # Q1|Q2|Q3|Q4  ->  Q1|Q2|Q3|Q4
              #  6|12|18|24       7|13|18|24
              total_remainder += (row_remainder - row_remainder.truncate)
              row_remainder = row_remainder.truncate

              dollars.transform_values!.each do |val|
                val += 1 if row_remainder.positive?
                row_remainder -= 1
                return val
              end

              ary << dollars
            end
          end

          # Apply overall total remainder to each row/qtr until remainder is zero
          allocated_dollars.each do |amt|
            amt.transform_values!.each do |val|
              val += 1 if total_remainder.positive?
              total_remainder -= 1
              return val
            end
          end

          allocated_dollars
        end

      budgets.each_with_index do |bgt, idx|
        bgt.update!(amounts[idx])
      end

      budgets
    end

    # Determines prior year spend and injects zero spend dollars if no prior year budget present
    #
    # @param budgets [Array<Budget|StealthModeBudget>]
    # @return [Array<Integer>]
    def py_spend_for(budgets)
      budgets
        .map(&:prior_year_budget)
        .map { |b| b.nil? ? BUDGET_CY_QUARTERLY_FIELDS.to_h { |x| [x, 0] } : b }
        .map { |b| b.slice(*BUDGET_CY_QUARTERLY_FIELDS) }
        .flat_map { |b| b.values.reduce(:+) } # [1000, 0, 5000, ...]
    end

    # Uses splitter logic to split total amount to quarterly breakouts for a budget
    #
    # @param budgets [Budget]
    # @return [Hash]
    def total_to_quarters(total, budget)
      quarterly = PamClient::SplitterInterface.new(budget, budget.prior_year_budget).split_total_into_quarters(total,
                                                                                                               :actual)
      BUDGET_CY_QUARTERLY_FIELDS.each_with_object({}) do |qtr, hash|
        amt_key = qtr.to_s.gsub(/^actual_(.*)_amount/, '\1').to_sym
        hash[qtr] = quarterly[amt_key]
      end
    end

    # @see https://github.com/CINBCUniversal/sms/wiki/Distributing-Percents-and-Dollars-Across-Quarters
    #
    # Example Input: [13.626332, 47.989636, 9.596008, 28.788024]
    # Example Output: [48, 29, 14, 9]
    def largest_remainder(pcts)
      sum = pcts.reduce(:+)
      rounded_sum = pcts.map(&:floor).reduce(:+)
      return pcts if sum == rounded_sum

      diff = sum - rounded_sum
      sorted_decimals = pcts.sort do |pct1, pct2|
        (pct2 % 1) - (pct1 % 1)
      end
      sorted_decimals
        .map!(&:floor)
        .map! do |pct|
          new_val = diff.positive? ? pct + 1 : pct
          diff -= 1
          new_val
        end
    end

    def validate_data_presence
      return unless ruleset.present?

      raise StealthUpload::Error::EmptyUploadError unless mapped_rows.any?
    end

    def validate_rows
      err_rows = mapped_rows.reject(&:valid?)
      return if err_rows.empty?

      err_rows.flat_map(&:errors).flat_map(&:full_messages).each do |msg|
        errors.add :base, msg
      end
    end

    def stealth_enabled?
      @stealth_enabled ||=
        AgencyMarketplaceYear.find_by(
          agency_id: @parent_agency_id,
          marketplace_id: @marketplace.id,
          budget_year_id: @budget_year.id
        )&.stealth_enabled?
    end
  end
  # rubocop:enable Metrics/ClassLength
end
