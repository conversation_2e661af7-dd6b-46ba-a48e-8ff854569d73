# frozen_string_literal: true

module StealthUpload
  module Mapping
    class RegistrationColumn
      include ActiveModel::Model

      attr_accessor :total_dollars, :actual_prequarter_amount, :actual_quarter1_amount, :actual_quarter2_amount
      attr_accessor :actual_quarter3_amount, :actual_quarter4_amount, :actual_postquarter_amount

      def by_total?
        total_dollars.present?
      end

      def by_quarter?
        total_dollars.blank?
      end

      def blank_quarters?
        quarter_fields.values.all?(&:blank?)
      end

      def quarter_fields
        {
          actual_prequarter_amount:,
          actual_quarter1_amount:,
          actual_quarter2_amount:,
          actual_quarter3_amount:,
          actual_quarter4_amount:,
          actual_postquarter_amount:
        }
      end

      def registration_fields
        { total_dollar: total_dollars }.merge(quarter_fields)
      end

      def [](attr)
        send(attr)
      end

      private

      def by_total_or_quarter
        msg = 'Specify either total or quarter registration columns, not both'
        errors.add(:base, msg) unless by_quarter? ^ blank_quarters?
      end
    end
  end
end
