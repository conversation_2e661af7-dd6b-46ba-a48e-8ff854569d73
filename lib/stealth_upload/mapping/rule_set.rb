# frozen_string_literal: true

module StealthUpload
  module Mapping
    class RuleSet
      include ActiveModel::Model
      REQD_MAPPINGS = %i[advertiser_id agency_id property_id].freeze

      attr_accessor :create_new_deals
      attr_reader :source_columns, :registration_column, :rules

      def source_columns=(source_columns)
        @source_columns ||= [].tap do |ary|
          source_columns.each do |source_column|
            ary << StealthUpload::Mapping::SourceColumn.new(source_column_name: source_column[:source_column_name],
                                                            integration: source_column[:integration],
                                                            direct_mapping: source_column[:direct_mapping])
          end
        end
      end

      def registration_column=(registration_column)
        return unless registration_column

        @registration_column =
          StealthUpload::Mapping::RegistrationColumn
          .new(total_dollars: registration_column[:total_dollars],
               actual_prequarter_amount: registration_column[:actual_prequarter_amount],
               actual_quarter1_amount: registration_column[:actual_quarter1_amount],
               actual_quarter2_amount: registration_column[:actual_quarter2_amount],
               actual_quarter3_amount: registration_column[:actual_quarter3_amount],
               actual_quarter4_amount: registration_column[:actual_quarter4_amount],
               actual_postquarter_amount: registration_column[:actual_postquarter_amount])
      end

      def rules=(rules)
        @rules ||= [].tap do |ary|
          rules.each do |rule|
            ary <<
              StealthUpload::Mapping::Rule.new(conditions: rule[:conditions_attributes],
                                               actions: rule[:actions_attributes])
          end
        end
      end

      def source_column_names
        source_columns.map(&:source_column_name)
      end

      def apply_rules(record)
        raise StealthUpload::Error::RuleSetMissingRuleError if rules.count < 1

        {}.tap do |hsh|
          rules.each do |rule|
            tmp_record = hsh.empty? ? record : hsh
            hsh.merge!(rule.apply(tmp_record))
          end
        end
      end

      def matching_rules(hash)
        rules.each_with_object([]) do |rule, ary|
          ary << rule if rule.meets_conditions?(hash)
        end.compact
      end

      def headers
        @headers ||= [].tap do |ary|
          ary << source_columns.map(&:source_column_name)
          ary << registration_column.registration_fields.values.reject(&:blank?) if has_registration_column?
        end.flatten.uniq
      end

      def mapping_tos
        rules.flat_map(&:actions).map(&:target_column_name).uniq.map(&:to_sym)
      end

      def has_direct_mapping_column?
        @has_direct_mapping_column ||= source_columns.any?(&:is_direct_mapping)
      end

      def has_integration_column?
        @has_integration_column ||= source_columns.any?(&:is_integration)
      end

      def has_registration_column?
        !registration_column.nil?
      end

      def direct_mapping_columns
        @direct_mapping_columns ||= source_columns.select(&:is_direct_mapping)
      end

      def direct_mapping_column_names
        @direct_mapping_column_names ||= direct_mapping_columns.flat_map(&:source_column_name)
      end

      def integration_columns
        @integration_columns ||= source_columns.select(&:is_integration)
      end

      def integration_column_names
        @integration_column_names ||= integration_columns.flat_map(&:source_column_name)
      end

      def non_integration_columns
        @non_integration_columns ||= source_columns.reject(&:is_integration)
      end

      def actions_for_required_mappings
        rules.flat_map(&:actions).find_all { |action| REQD_MAPPINGS.map(&:to_s).include?(action[:target_column_name]) }
      end

      def conditions_for_required_mappings
        actions_for_required_mappings.flat_map(&:rule).flat_map(&:conditions)
      end
    end
  end
end
