# frozen_string_literal: true

module StealthUpload
  module Mapping
    class Rule
      include ActiveModel::Model

      attr_reader :conditions, :actions

      def initialize(attributes)
        super
        after_initialize
      end

      def conditions=(conditions)
        @conditions ||= [].tap do |ary|
          conditions.each do |condition|
            ary <<
              StealthUpload::Mapping::Condition.new(condition_index: condition[:condition_index],
                                                    condition_name: condition[:condition_name],
                                                    source_column_id: condition[:source_column_id],
                                                    source_column_name: condition[:source_column_name],
                                                    source_column_value: condition[:source_column_value],
                                                    rule_id: condition[:rule_id])
          end
        end
      end

      alias conditions_attributes conditions
      alias conditions_attributes= conditions=
      def actions=(actions)
        @actions ||= [].tap do |ary|
          actions.each do |action|
            ary <<
              StealthUpload::Mapping::Action.new(target_column_name: action[:target_column_name],
                                                 sms_target_column_value: action[:sms_target_column_value])
          end
        end
      end

      alias actions_attributes actions
      alias actions_attributes= actions=

      def [](attr)
        send(attr).as_json.map(&:symbolize_keys)
      end

      def composite?
        @composite ||= conditions.count > 1
      end

      def integration?
        @integration ||= conditions.flat_map(&:source_column).any?(&:integration?)
      end

      def maps_from
        @maps_from ||= conditions.flat_map(&:condition_name).uniq
      end

      def maps_to
        @maps_to ||= actions.flat_map(&:target_column_name).uniq
      end

      def apply(record)
        raise StealthUpload::Error::RuleMissingConditionError if conditions.count < 1
        raise StealthUpload::Error::RuleMissingActionError if actions.count < 1

        meets_conditions?(record) ? apply_actions(record) : record
      end

      # @description
      #   Checks if the conditions associated to the rule are all met
      #   Fills and array of booleans with the result of
      #   `condition.met_for?` for each record, and reduces the result
      #   to a single boolean utilizing the `and` operator
      #
      # @param [Hash] record A hash of the record being processed
      # @return [Boolean]
      def meets_conditions?(record)
        conditions.sort_by(&:condition_index).map do |condition|
          condition.met_for?(record)
        end.reduce(&:&)
      end

      # @description
      #   Apply the actions associated to the rule
      #
      # @param [Hash] record The record to which we are applying the action
      # @return [Hash] The result of the applied action
      def apply_actions(record)
        tmp_record = record
        actions.each do |action|
          tmp_record = action.apply(tmp_record)
          Rails
            .logger
            .debug("Value #{action.sms_target_column_value} mapped to #{action.target_column_name}")
        end
        tmp_record
      end

      # Sort by number of conditions for that composite rules show up first
      def <=>(other)
        other.conditions.size <=> conditions.size
      end

      private

      def after_initialize
        condition_names = conditions&.map(&:condition_name)
        actions.each { |action| action.condition_names = condition_names || [] }
      end
    end
  end
end
