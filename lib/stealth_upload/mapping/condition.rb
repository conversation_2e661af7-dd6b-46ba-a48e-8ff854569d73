# frozen_string_literal: true

module StealthUpload
  module Mapping
    class Condition
      include ActiveModel::Model

      attr_accessor :condition_index,
                    :condition_name,
                    :source_column_id,
                    :source_column_name,
                    :source_column_value,
                    :rule_id

      # @description
      #   Checks if the condition is met for the record
      #
      # @param [Hash] record
      # @return [<PERSON><PERSON><PERSON>]
      def met_for?(record)
        rec = record[source_column_name]
        val = source_column_value
        rec.blank? ? compare_blank(rec, val) : compare_strings(rec, val)
      end

      def compare_strings(val_a, val_b)
        val_a&.downcase&.strip == val_b&.downcase&.strip
      end

      def compare_blank(val_a, val_b)
        val_a.blank? == val_b.blank?
      end
    end
  end
end
