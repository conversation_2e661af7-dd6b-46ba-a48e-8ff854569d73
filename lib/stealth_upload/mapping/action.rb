# frozen_string_literal: true

module StealthUpload
  module Mapping
    class Action
      include ActiveModel::Model

      attr_reader :target_column_name
      attr_accessor :sms_target_column_value, :condition_names

      def target_column_name=(target_column_name)
        return unless target_column_name

        # Replace `sms_deal_id` action to `deal_id`
        @target_column_name = target_column_name.gsub(/\bsms_/, '')
      end

      def apply(record)
        change = {}
        change.store(target_column_name.to_sym, sms_target_column_value)
        record.merge(change)
      end

      # @return [Array<Array<String>, String>] 2-elem array indicating what this action maps from & to,
      # e.g. `[["Business"], "agency_id"]`
      def from_to
        @from_to ||= [condition_names, target_column_name]
      end
    end
  end
end
