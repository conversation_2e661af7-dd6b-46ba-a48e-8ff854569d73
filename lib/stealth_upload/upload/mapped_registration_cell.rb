# frozen_string_literal: true

module StealthUpload
  module Upload
    # A cell containing registration values such as total registration, prequarter amount, etc.
    class MappedRegistrationCell < MappedCell
      attr_accessor :registration_column

      validate :registration_value_present
      validate :registration_value_gte_zero

      def map; end

      private

      def registration_value_present
        return if value.any?

        errors.add :base, "A value is required for #{header.join(', ')}"
      end

      def registration_value_gte_zero
        return if value&.all? { |x| x.to_i >= 0 }

        errors.add :base, "#{header.join(', ')} must be greater than or equal to zero"
      end
    end
  end
end
