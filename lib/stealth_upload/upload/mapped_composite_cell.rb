# frozen_string_literal: true

module StealthUpload
  module Upload
    # A cell used to map header/value pairs containing composite conditions to to an
    # internal model. Will be invalid if no rule is applicable to the `header` and
    # `value` values for this cell.
    class MappedCompositeCell < MappedCell
      attr_accessor :registration_column

      alias_attribute :headers, :header
      alias_attribute :values, :value

      validate :valid_conditions?

      private

      def valid_conditions?
        return true if rule.meets_conditions?(to_h)

        errors.add :base, "Mapping for #{header} '#{value}' does not exist"
      end
    end
  end
end
