# frozen_string_literal: true

module StealthUpload
  module Upload
    # A cell used to map header/value pairs to to an internal model. Will be
    # invalid if no rule is applicable to the `header` and `value` values for this
    # cell.
    class MappedModelCell < MappedCell
      REQD_VALUE_FIELDS = %w[advertiser_id agency_id property_id].freeze

      validate :meets_conditions

      private

      def missing_value_when_required?
        mapping_tos.any? &&
          (REQD_VALUE_FIELDS - mapping_tos).any? &&
          (value.nil? || value.try(:empty?))
      end

      def meets_conditions
        return if missing_value_when_required? || rule.meets_conditions?(to_h)

        errors.add :base, "A value is required for #{header.join(', ')}"
      end
    end
  end
end
