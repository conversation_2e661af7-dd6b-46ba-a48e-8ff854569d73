# frozen_string_literal: true

module StealthUpload
  module Upload
    # A cell containing a direct mapping to a PAM deal id
    class MappedDirectMappingCell
      include ActiveModel::Model

      attr_accessor :direct_mapping_columns, :header, :value

      validates_presence_of :direct_mapping_columns
      validates_presence_of :header

      def map
        @mapped_headers = [@header]
        @mapped_values = [@value]
        self
      end
    end
  end
end
