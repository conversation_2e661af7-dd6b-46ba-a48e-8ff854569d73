# frozen_string_literal: true

module StealthUpload
  module Upload
    # Represents a row from a dataset provided by the agency (CSV, JSON, etc.)
    # containing values and maps those values to based on a given ruleset.
    class MappedRow
      include ActiveModel::Model
      include ActiveModel::Validations

      attr_reader :headers
      attr_reader :values
      attr_reader :cells
      attr_reader :ruleset

      validates_presence_of :ruleset, message: 'can\'t be blank'
      validate :required_columns_present?
      validate :direct_mapping_column_present_if_required?
      validate :integration_column_present_if_required?
      validate :validate_cells
      validate :registration_cells_present?

      # > ruleset = StealthUpload::Mapping::RuleSet.find_by(...)
      # > row = MappedRow.new(["Agency", "Property"], ["Dentsu", "Bravo"], ruleset)
      # rubocop:disable Metrics/AbcSize
      def initialize(headers, values, ruleset)
        validate_init_args(headers, values, ruleset)
        @headers = headers
        @values = values
        @ruleset = ruleset
        hvals = headers.zip(values).to_h

        @cells = ruleset.matching_rules(hvals).each_with_object([]) do |rule, ary|
          ary << if rule.meets_conditions?(hvals)
                   if rule.composite?
                     MappedCompositeCell.new(headers: rule.maps_from,
                                             values: hvals.slice(*rule.maps_from).values,
                                             rule:,
                                             registration_column: ruleset.registration_column)
                   else
                     MappedModelCell.new(header: rule.maps_from,
                                         value: hvals.slice(*rule.maps_from).values,
                                         rule:)
                   end
                 end
        end
        if ruleset.has_integration_column? && hvals.key?(*ruleset.integration_column_names)
          int_cols = ruleset.integration_column_names
          @cells << MappedIntegrationCell.new(header: int_cols,
                                              value: hvals.slice(*int_cols).values,
                                              integration_columns: ruleset.integration_columns)
        end
        if ruleset.has_direct_mapping_column? && hvals.key?(*ruleset.direct_mapping_column_names)
          direct_mapping_columns = ruleset.direct_mapping_column_names
          @cells << MappedDirectMappingCell.new(header: direct_mapping_columns,
                                                value: hvals.slice(*direct_mapping_columns).values,
                                                direct_mapping_columns: ruleset.direct_mapping_columns)
        end

        return unless ruleset.has_registration_column?

        regi_headers = ruleset.registration_column.registration_fields.reject { |_k, v| v.blank? }.values
        regi_headers.each do |r_header|
          @cells << MappedRegistrationCell.new(header: [r_header],
                                               value: [hvals[r_header]],
                                               registration_column: ruleset.registration_column)
        end
      end
      # rubocop:enable Metrics/AbcSize

      # Map agency-provided values to internal (Agency Gateway) values based upon
      # pre-defined mappings.
      #
      # @return MappedRow
      def map
        cells.each(&:map)
      end

      def composite_cells
        @composite_cells ||= cells.select { |cell| cell.is_a?(MappedCompositeCell) }
      end

      def model_cells
        @model_cells ||= cells.select { |cell| cell.is_a?(MappedModelCell) }
      end

      def direct_mapping_cells
        @direct_mapping_cells ||= cells.select { |cell| cell.is_a?(MappedDirectMappingCell) }
      end

      def integration_cells
        @integration_cells ||= cells.select { |cell| cell.is_a?(MappedIntegrationCell) }
      end

      def registration_cells
        @registration_cells ||= cells.select { |cell| cell.is_a?(MappedRegistrationCell) }
      end

      # @return [FixNum] Total registration dollars for this row
      def registrations
        registration_cells.flat_map(&:value).map(&:to_i).sum
      end

      def registrations_are_by_total?
        ruleset.registration_column.by_total?
      end

      def mapped_h
        @mapped_h ||= HashWithIndifferentAccess[mapped_headers.compact.zip(mapped_values.compact)]
                      .transform_values!(&:to_i)
      end
      alias mapped_hash mapped_h

      def to_h
        headers.zip(values).to_h
      end
      alias to_hash to_h

      private

      def validate_init_args(headers, values, ruleset)
        raise ArgumentError, 'No headers given' if headers.blank?
        raise ArgumentError, 'No values given' if values.blank?
        raise ArgumentError, 'Ruleset can\'t be blank' if ruleset.blank? || !ruleset.valid?
      end

      def validate_cells
        err_cells = cells.reject(&:valid?)
        return if err_cells.empty?

        err_cells.flat_map(&:errors).flat_map(&:full_messages).each do |msg|
          errors.add :base, msg
        end
      end

      def mapped_headers
        map
        cells.flat_map(&:mapped_headers)
      end

      def mapped_values
        map
        cells.flat_map(&:mapped_values)
      end

      def registration_cells_present?
        return false if cells.any? do |cell|
          cell.respond_to?(:registration_column) && !cell.registration_column.blank?
        end

        errors.add :base, 'Registration column(s) not given'
      end

      def direct_mapping_column_present_if_required?
        return false unless ruleset.has_direct_mapping_column?

        cols = ruleset.direct_mapping_columns
        return false if direct_mapping_cells.size == cols.size

        missing_cols = cols.map(&:source_column_name).join(', ')
        errors.add :base, "Missing #{missing_cols}"
      end

      def integration_column_present_if_required?
        return false unless ruleset.has_integration_column?

        cols = ruleset.integration_columns
        return false if integration_cells.size == cols.size

        missing_cols = cols.map(&:source_column_name).join(', ')
        errors.add :base, "Missing #{missing_cols}"
      end

      def required_columns_present?
        return false if missing_required_columns.empty? || direct_mapping_cells.any?

        errors.add :base, "Required value(s) missing: #{headers_for_missing_columns.join(', ')}"
      end

      # @return [Array] array of symbols of missing search fields
      def missing_required_columns
        StealthUpload::Mapping::RuleSet::REQD_MAPPINGS - mapping_tos
      end

      def mapping_tos
        (model_cells.flat_map(&:mapping_tos) + composite_cells.flat_map(&:mapping_tos)).map(&:to_sym).uniq
      end

      # @return [Array<DataImport::Action>]
      def actions_for_missing_columns
        return [] unless missing_required_columns.any?

        ruleset.rules.flat_map(&:actions).find_all do |action|
          missing_required_columns.map(&:to_s).include?(action.target_column_name)
        end
      end

      # @return [Array<String>]
      def headers_for_missing_columns
        actions_for_missing_columns.map(&:from_to).map { |ft| ft[0] }.uniq
      end
    end
  end
end
