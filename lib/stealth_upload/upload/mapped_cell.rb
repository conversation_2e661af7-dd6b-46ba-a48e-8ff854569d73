# frozen_string_literal: true

module StealthUpload
  module Upload
    # Base class representing a cell from an upload (e.g.: an individual element
    # in a CSV, a spreadsheet cell, or a value in an array of JSON key/val hashes)
    # that can have its value mapped to an internal one.

    class MappedCell
      include ActiveModel::Model

      attr_accessor :header
      attr_accessor :rule
      attr_accessor :value
      attr_reader :mapped_headers
      attr_reader :mapped_values

      validates_presence_of :header

      define_model_callbacks :initialize

      def initialize(attrs)
        run_callbacks :initialize do
          super(attrs)
        end
      end

      # Map `value` (given by someone outside our system) to our internal value.
      # @return [MappedCell]
      def map
        return unless rule&.meets_conditions?(to_h)

        actions = rule.actions
        @mapped_headers = actions.map(&:target_column_name)
        @mapped_values = actions.map(&:sms_target_column_value)
        self
      end

      def mapped_h
        mapped_headers.zip(mapped_values).to_h
      end

      def to_h
        header.zip(value).to_h
      end
      alias to_hash to_h

      # @return [Array<String>] What we are mapping from, i.e. what the upload column headers are
      def mapping_froms
        from_tos { |ft| ft[0] }
      end

      # @return [Array<String>] What we are mapping to, i.e. what our internal model id fields are
      def mapping_tos
        from_tos { |ft| ft[1] }
      end

      # From and to mapping values for this cell's rule
      #
      # @return [Array<String>]
      def from_tos
        rule.actions.map(&:from_to).yield_self do |ary|
          if ary
            ary.uniq.map do |from_to|
              block_given? ? yield(from_to) : from_to
            end
          else
            []
          end
        end
      end
    end
  end
end
