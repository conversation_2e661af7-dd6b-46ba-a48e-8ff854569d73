{"data": {"id": "2", "type": "rule_sets", "links": {"self": "http://localhost:3000/api/v1/mapping/rule_sets/2"}, "attributes": {"create_new_deals": false, "sms_parent_agency_id": 11181, "source_columns_attributes": [{"id": 26, "source_column_name": "IntegrationId", "rule_set_id": 2, "created_at": null, "updated_at": "2019-05-09T17:32:12.000Z", "integration": false}, {"id": 33, "source_column_name": "Client", "rule_set_id": 2, "created_at": "2019-05-03T14:57:32.000Z", "updated_at": "2019-05-03T14:57:32.000Z", "integration": false}, {"id": 34, "source_column_name": "Network", "rule_set_id": 2, "created_at": "2019-05-03T14:57:39.000Z", "updated_at": "2019-05-03T14:57:39.000Z", "integration": false}, {"id": 35, "source_column_name": "Channel", "rule_set_id": 2, "created_at": "2019-05-03T14:57:44.000Z", "updated_at": "2019-05-03T14:57:44.000Z", "integration": false}, {"id": 36, "source_column_name": "Product", "rule_set_id": 2, "created_at": "2019-05-03T14:57:47.000Z", "updated_at": "2019-05-03T14:57:47.000Z", "integration": false}, {"id": 37, "source_column_name": "Demo", "rule_set_id": 2, "created_at": "2019-05-03T15:00:26.000Z", "updated_at": "2019-05-03T15:00:26.000Z", "integration": false}], "registration_column_attributes": {"id": 16, "total_dollars": "", "actual_prequarter_amount": "Q3N Ordered", "actual_quarter1_amount": "Q1 Ordered", "actual_quarter2_amount": "Q2 Ordered", "actual_quarter3_amount": "Q3 Ordered", "actual_quarter4_amount": "Q4 Ordered", "actual_postquarter_amount": "Q4P Ordered", "rule_set_id": 2, "created_at": "2019-04-10T20:04:49.000Z", "updated_at": "2019-05-03T14:58:30.000Z"}, "rules_attributes": [{"id": 196, "rule_name": "When IntegrationId is ABC123|Deal_ID is 73705|20190429220214", "rule_set_id": 2, "created_at": "2019-04-30T02:02:14.000Z", "updated_at": "2019-04-30T02:02:14.000Z"}, {"id": 210, "rule_name": "When Client is Capital One|Advertisers is Carmax|20190503105901", "rule_set_id": 2, "created_at": "2019-05-03T14:59:01.000Z", "updated_at": "2019-05-03T14:59:01.000Z"}, {"id": 211, "rule_name": "When Client is Burger King|Advertisers is Anheuser-Busch InBev|20190503105930", "rule_set_id": 2, "created_at": "2019-05-03T14:59:30.000Z", "updated_at": "2019-05-03T14:59:30.000Z"}, {"id": 212, "rule_name": "When Client is Boost|Advertisers is Walt Disney Parks & Resorts|20190503105952", "rule_set_id": 2, "created_at": "2019-05-03T14:59:52.000Z", "updated_at": "2019-05-03T14:59:52.000Z"}, {"id": 213, "rule_name": "When Network is BRAVO|Properties is Bravo|20190507113339", "rule_set_id": 2, "created_at": "2019-05-03T15:00:08.000Z", "updated_at": "2019-05-07T15:33:39.000Z"}, {"id": 214, "rule_name": "When Demo is A25-54|Demographics is F25-54|20190503110040", "rule_set_id": 2, "created_at": "2019-05-03T15:00:41.000Z", "updated_at": "2019-05-03T15:00:41.000Z"}, {"id": 215, "rule_name": "When Demo is A18-49|Demographics is F18-49|20190503110058", "rule_set_id": 2, "created_at": "2019-05-03T15:00:58.000Z", "updated_at": "2019-05-03T15:00:58.000Z"}, {"id": 216, "rule_name": "When Product is Capital One- Consumer Card|Agencies is Carat USA-AGENCY|20190503110123", "rule_set_id": 2, "created_at": "2019-05-03T15:01:23.000Z", "updated_at": "2019-05-03T15:01:23.000Z"}, {"id": 217, "rule_name": "When Product is BKC|Agencies is Empowering Media|20190503110149", "rule_set_id": 2, "created_at": "2019-05-03T15:01:49.000Z", "updated_at": "2019-05-03T15:01:49.000Z"}, {"id": 218, "rule_name": "When Product is Boost Mobile (General)|Agencies is Agyle Advantage - AGENCY|20190503110259", "rule_set_id": 2, "created_at": "2019-05-03T15:02:59.000Z", "updated_at": "2019-05-03T15:02:59.000Z"}, {"id": 219, "rule_name": "When IntegrationId is 3682577d-5935-e911-a961-ee14739f7968|Deal_ID is 85671|12345", "rule_set_id": 2, "created_at": "2019-05-03T17:15:03.000Z", "updated_at": "2019-05-03T17:15:03.000Z"}, {"id": 220, "rule_name": "When IntegrationId is 7584577d-5935-e911-a961-ee14739f7968|Deal_ID is 85672|12345", "rule_set_id": 2, "created_at": "2019-05-03T17:15:14.000Z", "updated_at": "2019-05-03T17:15:14.000Z"}, {"id": 221, "rule_name": "When IntegrationId is 4d82577d-5935-e911-a961-ee14739f7968|Deal_ID is 85673|12345", "rule_set_id": 2, "created_at": "2019-05-03T17:15:24.000Z", "updated_at": "2019-05-03T17:15:24.000Z"}, {"id": 222, "rule_name": "When IntegrationId is ce86577d-5935-e911-a961-ee14739f7968|Deal_ID is 85674|12345", "rule_set_id": 2, "created_at": "2019-05-03T17:15:34.000Z", "updated_at": "2019-05-03T17:15:34.000Z"}]}, "relationships": {"rules": {"links": {"self": "http://localhost:3000/api/v1/mapping/rule_sets/2/relationships/rules", "related": "http://localhost:3000/api/v1/mapping/rule_sets/2/rules"}, "data": [{"type": "rules", "id": "196"}, {"type": "rules", "id": "210"}, {"type": "rules", "id": "211"}, {"type": "rules", "id": "212"}, {"type": "rules", "id": "213"}, {"type": "rules", "id": "214"}, {"type": "rules", "id": "215"}, {"type": "rules", "id": "216"}, {"type": "rules", "id": "217"}, {"type": "rules", "id": "218"}, {"type": "rules", "id": "219"}, {"type": "rules", "id": "220"}, {"type": "rules", "id": "221"}, {"type": "rules", "id": "222"}]}, "source_columns": {"links": {"self": "http://localhost:3000/api/v1/mapping/rule_sets/2/relationships/source_columns", "related": "http://localhost:3000/api/v1/mapping/rule_sets/2/source_columns"}}, "registration_column": {"links": {"self": "http://localhost:3000/api/v1/mapping/rule_sets/2/relationships/registration_column", "related": "http://localhost:3000/api/v1/mapping/rule_sets/2/registration_column"}}}}, "included": [{"id": "196", "type": "rules", "links": {"self": "http://localhost:3000/api/v1/mapping/rules/196"}, "attributes": {"rule_name": "When IntegrationId is ABC123|Deal_ID is 73705|20190429220214", "conditions_attributes": [{"id": 360, "condition_index": 0, "condition_name": "IntegrationId", "source_column_value": "ABC123", "rule_id": 196, "created_at": "2019-04-30T02:02:14.000Z", "updated_at": "2019-04-30T02:02:14.000Z", "source_column_id": 26}], "actions_attributes": [{"id": 757, "target_column_name": "sms_deal_id", "target_column_value": "73705", "rule_id": 196, "created_at": "2019-04-30T02:02:14.000Z", "updated_at": "2019-04-30T02:02:14.000Z"}]}, "relationships": {"actions": {"links": {"self": "http://localhost:3000/api/v1/mapping/rules/196/relationships/actions", "related": "http://localhost:3000/api/v1/mapping/rules/196/actions"}, "data": [{"type": "actions", "id": "757"}]}, "conditions": {"links": {"self": "http://localhost:3000/api/v1/mapping/rules/196/relationships/conditions", "related": "http://localhost:3000/api/v1/mapping/rules/196/conditions"}, "data": [{"type": "conditions", "id": "360"}]}}}, {"id": "210", "type": "rules", "links": {"self": "http://localhost:3000/api/v1/mapping/rules/210"}, "attributes": {"rule_name": "When Client is Capital One|Advertisers is Carmax|20190503105901", "conditions_attributes": [{"id": 374, "condition_index": 0, "condition_name": "Client", "source_column_value": "Capital One", "rule_id": 210, "created_at": "2019-05-03T14:59:01.000Z", "updated_at": "2019-05-03T14:59:01.000Z", "source_column_id": 33}], "actions_attributes": [{"id": 771, "target_column_name": "advertiser_id", "target_column_value": "199", "rule_id": 210, "created_at": "2019-05-03T14:59:01.000Z", "updated_at": "2019-05-03T14:59:01.000Z"}]}, "relationships": {"actions": {"links": {"self": "http://localhost:3000/api/v1/mapping/rules/210/relationships/actions", "related": "http://localhost:3000/api/v1/mapping/rules/210/actions"}, "data": [{"type": "actions", "id": "771"}]}, "conditions": {"links": {"self": "http://localhost:3000/api/v1/mapping/rules/210/relationships/conditions", "related": "http://localhost:3000/api/v1/mapping/rules/210/conditions"}, "data": [{"type": "conditions", "id": "374"}]}}}, {"id": "211", "type": "rules", "links": {"self": "http://localhost:3000/api/v1/mapping/rules/211"}, "attributes": {"rule_name": "When Client is Burger King|Advertisers is Anheuser-Busch InBev|20190503105930", "conditions_attributes": [{"id": 375, "condition_index": 0, "condition_name": "Client", "source_column_value": "Burger King", "rule_id": 211, "created_at": "2019-05-03T14:59:30.000Z", "updated_at": "2019-05-03T14:59:30.000Z", "source_column_id": 33}], "actions_attributes": [{"id": 772, "target_column_name": "advertiser_id", "target_column_value": "186", "rule_id": 211, "created_at": "2019-05-03T14:59:30.000Z", "updated_at": "2019-05-03T14:59:30.000Z"}]}, "relationships": {"actions": {"links": {"self": "http://localhost:3000/api/v1/mapping/rules/211/relationships/actions", "related": "http://localhost:3000/api/v1/mapping/rules/211/actions"}, "data": [{"type": "actions", "id": "772"}]}, "conditions": {"links": {"self": "http://localhost:3000/api/v1/mapping/rules/211/relationships/conditions", "related": "http://localhost:3000/api/v1/mapping/rules/211/conditions"}, "data": [{"type": "conditions", "id": "375"}]}}}, {"id": "212", "type": "rules", "links": {"self": "http://localhost:3000/api/v1/mapping/rules/212"}, "attributes": {"rule_name": "When Client is Boost|Advertisers is Walt Disney Parks & Resorts|20190503105952", "conditions_attributes": [{"id": 376, "condition_index": 0, "condition_name": "Client", "source_column_value": "Boost", "rule_id": 212, "created_at": "2019-05-03T14:59:52.000Z", "updated_at": "2019-05-03T14:59:52.000Z", "source_column_id": 33}], "actions_attributes": [{"id": 773, "target_column_name": "advertiser_id", "target_column_value": "10", "rule_id": 212, "created_at": "2019-05-03T14:59:52.000Z", "updated_at": "2019-05-03T14:59:52.000Z"}]}, "relationships": {"actions": {"links": {"self": "http://localhost:3000/api/v1/mapping/rules/212/relationships/actions", "related": "http://localhost:3000/api/v1/mapping/rules/212/actions"}, "data": [{"type": "actions", "id": "773"}]}, "conditions": {"links": {"self": "http://localhost:3000/api/v1/mapping/rules/212/relationships/conditions", "related": "http://localhost:3000/api/v1/mapping/rules/212/conditions"}, "data": [{"type": "conditions", "id": "376"}]}}}, {"id": "213", "type": "rules", "links": {"self": "http://localhost:3000/api/v1/mapping/rules/213"}, "attributes": {"rule_name": "When Network is BRAVO|Properties is Bravo|20190507113339", "conditions_attributes": [{"id": 377, "condition_index": 0, "condition_name": "Network", "source_column_value": "BRAVO", "rule_id": 213, "created_at": "2019-05-03T15:00:08.000Z", "updated_at": "2019-05-03T15:00:08.000Z", "source_column_id": 34}], "actions_attributes": [{"id": 774, "target_column_name": "property_id", "target_column_value": "13", "rule_id": 213, "created_at": "2019-05-03T15:00:08.000Z", "updated_at": "2019-05-03T15:00:08.000Z"}]}, "relationships": {"actions": {"links": {"self": "http://localhost:3000/api/v1/mapping/rules/213/relationships/actions", "related": "http://localhost:3000/api/v1/mapping/rules/213/actions"}, "data": [{"type": "actions", "id": "774"}]}, "conditions": {"links": {"self": "http://localhost:3000/api/v1/mapping/rules/213/relationships/conditions", "related": "http://localhost:3000/api/v1/mapping/rules/213/conditions"}, "data": [{"type": "conditions", "id": "377"}]}}}, {"id": "214", "type": "rules", "links": {"self": "http://localhost:3000/api/v1/mapping/rules/214"}, "attributes": {"rule_name": "When Demo is A25-54|Demographics is F25-54|20190503110040", "conditions_attributes": [{"id": 378, "condition_index": 0, "condition_name": "Demo", "source_column_value": "A25-54", "rule_id": 214, "created_at": "2019-05-03T15:00:41.000Z", "updated_at": "2019-05-03T15:00:41.000Z", "source_column_id": 37}], "actions_attributes": [{"id": 775, "target_column_name": "demographic_id", "target_column_value": "7", "rule_id": 214, "created_at": "2019-05-03T15:00:41.000Z", "updated_at": "2019-05-03T15:00:41.000Z"}]}, "relationships": {"actions": {"links": {"self": "http://localhost:3000/api/v1/mapping/rules/214/relationships/actions", "related": "http://localhost:3000/api/v1/mapping/rules/214/actions"}, "data": [{"type": "actions", "id": "775"}]}, "conditions": {"links": {"self": "http://localhost:3000/api/v1/mapping/rules/214/relationships/conditions", "related": "http://localhost:3000/api/v1/mapping/rules/214/conditions"}, "data": [{"type": "conditions", "id": "378"}]}}}, {"id": "215", "type": "rules", "links": {"self": "http://localhost:3000/api/v1/mapping/rules/215"}, "attributes": {"rule_name": "When Demo is A18-49|Demographics is F18-49|20190503110058", "conditions_attributes": [{"id": 379, "condition_index": 0, "condition_name": "Demo", "source_column_value": "A18-49", "rule_id": 215, "created_at": "2019-05-03T15:00:58.000Z", "updated_at": "2019-05-03T15:00:58.000Z", "source_column_id": 37}], "actions_attributes": [{"id": 776, "target_column_name": "demographic_id", "target_column_value": "14", "rule_id": 215, "created_at": "2019-05-03T15:00:58.000Z", "updated_at": "2019-05-03T15:00:58.000Z"}]}, "relationships": {"actions": {"links": {"self": "http://localhost:3000/api/v1/mapping/rules/215/relationships/actions", "related": "http://localhost:3000/api/v1/mapping/rules/215/actions"}, "data": [{"type": "actions", "id": "776"}]}, "conditions": {"links": {"self": "http://localhost:3000/api/v1/mapping/rules/215/relationships/conditions", "related": "http://localhost:3000/api/v1/mapping/rules/215/conditions"}, "data": [{"type": "conditions", "id": "379"}]}}}, {"id": "216", "type": "rules", "links": {"self": "http://localhost:3000/api/v1/mapping/rules/216"}, "attributes": {"rule_name": "When Product is Capital One- Consumer Card|Agencies is Carat USA-AGENCY|20190503110123", "conditions_attributes": [{"id": 380, "condition_index": 0, "condition_name": "Product", "source_column_value": "Capital One- Consumer Card", "rule_id": 216, "created_at": "2019-05-03T15:01:23.000Z", "updated_at": "2019-05-03T15:01:23.000Z", "source_column_id": 36}], "actions_attributes": [{"id": 777, "target_column_name": "agency_id", "target_column_value": "2", "rule_id": 216, "created_at": "2019-05-03T15:01:23.000Z", "updated_at": "2019-05-03T15:01:23.000Z"}]}, "relationships": {"actions": {"links": {"self": "http://localhost:3000/api/v1/mapping/rules/216/relationships/actions", "related": "http://localhost:3000/api/v1/mapping/rules/216/actions"}, "data": [{"type": "actions", "id": "777"}]}, "conditions": {"links": {"self": "http://localhost:3000/api/v1/mapping/rules/216/relationships/conditions", "related": "http://localhost:3000/api/v1/mapping/rules/216/conditions"}, "data": [{"type": "conditions", "id": "380"}]}}}, {"id": "217", "type": "rules", "links": {"self": "http://localhost:3000/api/v1/mapping/rules/217"}, "attributes": {"rule_name": "When Product is BKC|Agencies is Empowering Media|20190503110149", "conditions_attributes": [{"id": 381, "condition_index": 0, "condition_name": "Product", "source_column_value": "BKC", "rule_id": 217, "created_at": "2019-05-03T15:01:49.000Z", "updated_at": "2019-05-03T15:01:49.000Z", "source_column_id": 36}], "actions_attributes": [{"id": 778, "target_column_name": "agency_id", "target_column_value": "8", "rule_id": 217, "created_at": "2019-05-03T15:01:49.000Z", "updated_at": "2019-05-03T15:01:49.000Z"}]}, "relationships": {"actions": {"links": {"self": "http://localhost:3000/api/v1/mapping/rules/217/relationships/actions", "related": "http://localhost:3000/api/v1/mapping/rules/217/actions"}, "data": [{"type": "actions", "id": "778"}]}, "conditions": {"links": {"self": "http://localhost:3000/api/v1/mapping/rules/217/relationships/conditions", "related": "http://localhost:3000/api/v1/mapping/rules/217/conditions"}, "data": [{"type": "conditions", "id": "381"}]}}}, {"id": "218", "type": "rules", "links": {"self": "http://localhost:3000/api/v1/mapping/rules/218"}, "attributes": {"rule_name": "When Product is Boost Mobile (General)|Agencies is Agyle Advantage - AGENCY|20190503110259", "conditions_attributes": [{"id": 382, "condition_index": 0, "condition_name": "Product", "source_column_value": "<PERSON><PERSON> (General)", "rule_id": 218, "created_at": "2019-05-03T15:02:59.000Z", "updated_at": "2019-05-03T15:02:59.000Z", "source_column_id": 36}], "actions_attributes": [{"id": 779, "target_column_name": "agency_id", "target_column_value": "72", "rule_id": 218, "created_at": "2019-05-03T15:02:59.000Z", "updated_at": "2019-05-03T15:02:59.000Z"}]}, "relationships": {"actions": {"links": {"self": "http://localhost:3000/api/v1/mapping/rules/218/relationships/actions", "related": "http://localhost:3000/api/v1/mapping/rules/218/actions"}, "data": [{"type": "actions", "id": "779"}]}, "conditions": {"links": {"self": "http://localhost:3000/api/v1/mapping/rules/218/relationships/conditions", "related": "http://localhost:3000/api/v1/mapping/rules/218/conditions"}, "data": [{"type": "conditions", "id": "382"}]}}}, {"id": "219", "type": "rules", "links": {"self": "http://localhost:3000/api/v1/mapping/rules/219"}, "attributes": {"rule_name": "When IntegrationId is 3682577d-5935-e911-a961-ee14739f7968|Deal_ID is 85671|12345", "conditions_attributes": [{"id": 383, "condition_index": 0, "condition_name": "IntegrationId", "source_column_value": "3682577d-5935-e911-a961-ee14739f7968", "rule_id": 219, "created_at": "2019-05-03T17:15:03.000Z", "updated_at": "2019-05-03T17:15:03.000Z", "source_column_id": 26}], "actions_attributes": [{"id": 780, "target_column_name": "sms_deal_id", "target_column_value": "85671", "rule_id": 219, "created_at": "2019-05-03T17:15:03.000Z", "updated_at": "2019-05-03T17:15:03.000Z"}]}, "relationships": {"actions": {"links": {"self": "http://localhost:3000/api/v1/mapping/rules/219/relationships/actions", "related": "http://localhost:3000/api/v1/mapping/rules/219/actions"}, "data": [{"type": "actions", "id": "780"}]}, "conditions": {"links": {"self": "http://localhost:3000/api/v1/mapping/rules/219/relationships/conditions", "related": "http://localhost:3000/api/v1/mapping/rules/219/conditions"}, "data": [{"type": "conditions", "id": "383"}]}}}, {"id": "220", "type": "rules", "links": {"self": "http://localhost:3000/api/v1/mapping/rules/220"}, "attributes": {"rule_name": "When IntegrationId is 7584577d-5935-e911-a961-ee14739f7968|Deal_ID is 85672|12345", "conditions_attributes": [{"id": 384, "condition_index": 0, "condition_name": "IntegrationId", "source_column_value": "7584577d-5935-e911-a961-ee14739f7968", "rule_id": 220, "created_at": "2019-05-03T17:15:14.000Z", "updated_at": "2019-05-03T17:15:14.000Z", "source_column_id": 26}], "actions_attributes": [{"id": 781, "target_column_name": "sms_deal_id", "target_column_value": "85672", "rule_id": 220, "created_at": "2019-05-03T17:15:14.000Z", "updated_at": "2019-05-03T17:15:14.000Z"}]}, "relationships": {"actions": {"links": {"self": "http://localhost:3000/api/v1/mapping/rules/220/relationships/actions", "related": "http://localhost:3000/api/v1/mapping/rules/220/actions"}, "data": [{"type": "actions", "id": "781"}]}, "conditions": {"links": {"self": "http://localhost:3000/api/v1/mapping/rules/220/relationships/conditions", "related": "http://localhost:3000/api/v1/mapping/rules/220/conditions"}, "data": [{"type": "conditions", "id": "384"}]}}}, {"id": "221", "type": "rules", "links": {"self": "http://localhost:3000/api/v1/mapping/rules/221"}, "attributes": {"rule_name": "When IntegrationId is 4d82577d-5935-e911-a961-ee14739f7968|Deal_ID is 85673|12345", "conditions_attributes": [{"id": 385, "condition_index": 0, "condition_name": "IntegrationId", "source_column_value": "4d82577d-5935-e911-a961-ee14739f7968", "rule_id": 221, "created_at": "2019-05-03T17:15:24.000Z", "updated_at": "2019-05-03T17:15:24.000Z", "source_column_id": 26}], "actions_attributes": [{"id": 782, "target_column_name": "sms_deal_id", "target_column_value": "85673", "rule_id": 221, "created_at": "2019-05-03T17:15:24.000Z", "updated_at": "2019-05-03T17:15:24.000Z"}]}, "relationships": {"actions": {"links": {"self": "http://localhost:3000/api/v1/mapping/rules/221/relationships/actions", "related": "http://localhost:3000/api/v1/mapping/rules/221/actions"}, "data": [{"type": "actions", "id": "782"}]}, "conditions": {"links": {"self": "http://localhost:3000/api/v1/mapping/rules/221/relationships/conditions", "related": "http://localhost:3000/api/v1/mapping/rules/221/conditions"}, "data": [{"type": "conditions", "id": "385"}]}}}, {"id": "222", "type": "rules", "links": {"self": "http://localhost:3000/api/v1/mapping/rules/222"}, "attributes": {"rule_name": "When IntegrationId is ce86577d-5935-e911-a961-ee14739f7968|Deal_ID is 85674|12345", "conditions_attributes": [{"id": 386, "condition_index": 0, "condition_name": "IntegrationId", "source_column_value": "ce86577d-5935-e911-a961-ee14739f7968", "rule_id": 222, "created_at": "2019-05-03T17:15:34.000Z", "updated_at": "2019-05-03T17:15:34.000Z", "source_column_id": 26}], "actions_attributes": [{"id": 783, "target_column_name": "sms_deal_id", "target_column_value": "85674", "rule_id": 222, "created_at": "2019-05-03T17:15:34.000Z", "updated_at": "2019-05-03T17:15:34.000Z"}]}, "relationships": {"actions": {"links": {"self": "http://localhost:3000/api/v1/mapping/rules/222/relationships/actions", "related": "http://localhost:3000/api/v1/mapping/rules/222/actions"}, "data": [{"type": "actions", "id": "783"}]}, "conditions": {"links": {"self": "http://localhost:3000/api/v1/mapping/rules/222/relationships/conditions", "related": "http://localhost:3000/api/v1/mapping/rules/222/conditions"}, "data": [{"type": "conditions", "id": "386"}]}}}, {"id": "757", "type": "actions", "links": {"self": "http://localhost:3000/api/v1/mapping/actions/757"}, "attributes": {"target_column_name": "sms_deal_id", "rule_id": 196, "sms_target_column_value": "73705"}}, {"id": "771", "type": "actions", "links": {"self": "http://localhost:3000/api/v1/mapping/actions/771"}, "attributes": {"target_column_name": "advertiser_id", "rule_id": 210, "sms_target_column_value": 55852}}, {"id": "772", "type": "actions", "links": {"self": "http://localhost:3000/api/v1/mapping/actions/772"}, "attributes": {"target_column_name": "advertiser_id", "rule_id": 211, "sms_target_column_value": 55803}}, {"id": "773", "type": "actions", "links": {"self": "http://localhost:3000/api/v1/mapping/actions/773"}, "attributes": {"target_column_name": "advertiser_id", "rule_id": 212, "sms_target_column_value": 56209}}, {"id": "774", "type": "actions", "links": {"self": "http://localhost:3000/api/v1/mapping/actions/774"}, "attributes": {"target_column_name": "property_id", "rule_id": 213, "sms_target_column_value": 10245}}, {"id": "775", "type": "actions", "links": {"self": "http://localhost:3000/api/v1/mapping/actions/775"}, "attributes": {"target_column_name": "demographic_id", "rule_id": 214, "sms_target_column_value": 10064}}, {"id": "776", "type": "actions", "links": {"self": "http://localhost:3000/api/v1/mapping/actions/776"}, "attributes": {"target_column_name": "demographic_id", "rule_id": 215, "sms_target_column_value": 10068}}, {"id": "777", "type": "actions", "links": {"self": "http://localhost:3000/api/v1/mapping/actions/777"}, "attributes": {"target_column_name": "agency_id", "rule_id": 216, "sms_target_column_value": 10365}}, {"id": "778", "type": "actions", "links": {"self": "http://localhost:3000/api/v1/mapping/actions/778"}, "attributes": {"target_column_name": "agency_id", "rule_id": 217, "sms_target_column_value": 11137}}, {"id": "779", "type": "actions", "links": {"self": "http://localhost:3000/api/v1/mapping/actions/779"}, "attributes": {"target_column_name": "agency_id", "rule_id": 218, "sms_target_column_value": 21802}}, {"id": "780", "type": "actions", "links": {"self": "http://localhost:3000/api/v1/mapping/actions/780"}, "attributes": {"target_column_name": "sms_deal_id", "rule_id": 219, "sms_target_column_value": "85671"}}, {"id": "781", "type": "actions", "links": {"self": "http://localhost:3000/api/v1/mapping/actions/781"}, "attributes": {"target_column_name": "sms_deal_id", "rule_id": 220, "sms_target_column_value": "85672"}}, {"id": "782", "type": "actions", "links": {"self": "http://localhost:3000/api/v1/mapping/actions/782"}, "attributes": {"target_column_name": "sms_deal_id", "rule_id": 221, "sms_target_column_value": "85673"}}, {"id": "783", "type": "actions", "links": {"self": "http://localhost:3000/api/v1/mapping/actions/783"}, "attributes": {"target_column_name": "sms_deal_id", "rule_id": 222, "sms_target_column_value": "85674"}}, {"id": "360", "type": "conditions", "links": {"self": "http://localhost:3000/api/v1/mapping/conditions/360"}, "attributes": {"condition_index": 0, "condition_name": "IntegrationId", "source_column_value": "ABC123", "rule_id": 196, "source_column_id": 26}}, {"id": "374", "type": "conditions", "links": {"self": "http://localhost:3000/api/v1/mapping/conditions/374"}, "attributes": {"condition_index": 0, "condition_name": "Client", "source_column_value": "Capital One", "rule_id": 210, "source_column_id": 33}}, {"id": "375", "type": "conditions", "links": {"self": "http://localhost:3000/api/v1/mapping/conditions/375"}, "attributes": {"condition_index": 0, "condition_name": "Client", "source_column_value": "Burger King", "rule_id": 211, "source_column_id": 33}}, {"id": "376", "type": "conditions", "links": {"self": "http://localhost:3000/api/v1/mapping/conditions/376"}, "attributes": {"condition_index": 0, "condition_name": "Client", "source_column_value": "Boost", "rule_id": 212, "source_column_id": 33}}, {"id": "377", "type": "conditions", "links": {"self": "http://localhost:3000/api/v1/mapping/conditions/377"}, "attributes": {"condition_index": 0, "condition_name": "Network", "source_column_value": "BRAVO", "rule_id": 213, "source_column_id": 34}}, {"id": "378", "type": "conditions", "links": {"self": "http://localhost:3000/api/v1/mapping/conditions/378"}, "attributes": {"condition_index": 0, "condition_name": "Demo", "source_column_value": "A25-54", "rule_id": 214, "source_column_id": 37}}, {"id": "379", "type": "conditions", "links": {"self": "http://localhost:3000/api/v1/mapping/conditions/379"}, "attributes": {"condition_index": 0, "condition_name": "Demo", "source_column_value": "A18-49", "rule_id": 215, "source_column_id": 37}}, {"id": "380", "type": "conditions", "links": {"self": "http://localhost:3000/api/v1/mapping/conditions/380"}, "attributes": {"condition_index": 0, "condition_name": "Product", "source_column_value": "Capital One- Consumer Card", "rule_id": 216, "source_column_id": 36}}, {"id": "381", "type": "conditions", "links": {"self": "http://localhost:3000/api/v1/mapping/conditions/381"}, "attributes": {"condition_index": 0, "condition_name": "Product", "source_column_value": "BKC", "rule_id": 217, "source_column_id": 36}}, {"id": "382", "type": "conditions", "links": {"self": "http://localhost:3000/api/v1/mapping/conditions/382"}, "attributes": {"condition_index": 0, "condition_name": "Product", "source_column_value": "<PERSON><PERSON> (General)", "rule_id": 218, "source_column_id": 36}}, {"id": "383", "type": "conditions", "links": {"self": "http://localhost:3000/api/v1/mapping/conditions/383"}, "attributes": {"condition_index": 0, "condition_name": "IntegrationId", "source_column_value": "3682577d-5935-e911-a961-ee14739f7968", "rule_id": 219, "source_column_id": 26}}, {"id": "384", "type": "conditions", "links": {"self": "http://localhost:3000/api/v1/mapping/conditions/384"}, "attributes": {"condition_index": 0, "condition_name": "IntegrationId", "source_column_value": "7584577d-5935-e911-a961-ee14739f7968", "rule_id": 220, "source_column_id": 26}}, {"id": "385", "type": "conditions", "links": {"self": "http://localhost:3000/api/v1/mapping/conditions/385"}, "attributes": {"condition_index": 0, "condition_name": "IntegrationId", "source_column_value": "4d82577d-5935-e911-a961-ee14739f7968", "rule_id": 221, "source_column_id": 26}}, {"id": "386", "type": "conditions", "links": {"self": "http://localhost:3000/api/v1/mapping/conditions/386"}, "attributes": {"condition_index": 0, "condition_name": "IntegrationId", "source_column_value": "ce86577d-5935-e911-a961-ee14739f7968", "rule_id": 222, "source_column_id": 26}}]}