# frozen_string_literal: true

require 'net/http'

class SmsApiClient
  PAM_ACCESS_TOKEN = 'abf76e0980e5109b9a3b53c073272593ee629eee'

  class << self
    def redirect(request_method, v1_endpoint, params = {}, body = {})
      uri = redirect_uri(v1_endpoint, params)

      Net::HTTP.start(uri.host, uri.port) do |http|
        request_type = Net::HTTP.const_get(request_method.capitalize)
        request = request_type.new(uri)
        unless request_type == Net::HTTP::Get
          request.body = body.to_json
          request.content_type = 'application/json'
        end
        http.request(request)
      end
    end

    private

    def redirect_uri(v1_endpoint, params)
      port = ENV['SMS_API_PORT'] || URI::HTTP::DEFAULT_PORT
      URI::HTTP.build(host: ENV['SMS_API_HOST'], port:, path: "/api#{v1_endpoint}",
                      query: params_with_auth(params))
    end

    def params_with_auth(params)
      params.merge(access_token: PAM_ACCESS_TOKEN).to_query
    end
  end
end
