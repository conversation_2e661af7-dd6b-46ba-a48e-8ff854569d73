# frozen_string_literal: true

require_relative 'production'

Rails.application.configure do
  # CORS
  config.allowed_cors_origins = ['pamdev.inbcu.com', 'pamdev.sales-enablement-dev.adsalescloud.inbcu.com',
                                 'mstrstg2.inbcu.com', 'rmxdev.inbcu.com',
                                 'pamdevec.inbcu.com']

  config.cache_store = :redis_store, {
    url: "rediss://#{ENV['REDIS_HOST']}:#{ENV['REDIS_PORT']}",
    password: ENV['REDIS_PASSWORD']
  }
end
