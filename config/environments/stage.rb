# frozen_string_literal: true

require_relative 'production'

Rails.application.configure do
  # CORS
  config.allowed_cors_origins = ['pamstage.inbcu.com', 'pamstage.devaoa.inbcu.com',
                                 'rmxstage.inbcu.com', 'pamstage1.inbcu.com',
                                 'pamstage2.inbcu.com']

  config.cache_store = :redis_store, {
    url: "rediss://#{ENV['REDIS_HOST']}:#{ENV['REDIS_PORT']}",
    password: ENV['REDIS_PASSWORD']
  }
end
