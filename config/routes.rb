# frozen_string_literal: true

require 'sidekiq/web'

# rubocop:disable Metrics/BlockLength
Rails.application.routes.draw do
  mount Sidekiq::Web => '/sidekiq'

  namespace 'api' do
    resources :pacing_budget_details, only: %i[index]
    resources :agency_marketplace_years, only: %i[create update index]
    resources :agency_property_locks, only: [:index]
    resources :advertiser_summaries, only: [:index] do
      collection do
        get 'info/:advertiser_id', to: 'advertiser_summaries#info'
        get 'marketplaces/:advertiser_id', to: 'advertiser_summaries#marketplaces'
        get 'pillars/:advertiser_id', to: 'advertiser_summaries#pillars'
        get 'property_types/:advertiser_id', to: 'advertiser_summaries#property_types'
        get 'quintiles/:advertiser_id', to: 'advertiser_summaries#quintiles'
        get 'selling_verticals/:advertiser_id', to: 'advertiser_summaries#selling_verticals'
        get :archives, to: 'advertiser_summary_archives#index'
        get :quintile_chart
        post :archives, to: 'advertiser_summary_archives#create'
        post :save
      end
    end
    resources :advertiser_brands, only: [] do
      collection do
        post :publish
      end
    end

    namespace 'cdw' do
      resources :external_deals, only: [] do
        collection do
          get :import
        end
      end
    end

    namespace 'sf_callback' do
      put :update_budgets
      put :update_special_event_details
      put :update_budget_sync_status
      put :update_budget_sync_status_by_deal_ids
      put :update_parent_deal
    end

    namespace 'salesforce' do
      get 'deals/:deal_id', to: 'deals#index'
    end

    namespace 'uws' do
      resources :budget_sales_systems, only: [] do
        collection do
          get :digital
          get :linear
          get :linear_health_check
        end
      end
    end

    resources :budgets, except: [:destroy] do
      collection do
        patch :batch_update
      end
    end
    resources :budget_years, only: [] do
      collection do
        post :publish
      end
    end
    resources :calendar_years, only: [:index]
    resources :comments, except: [:show] do
      collection do
        patch :batch_update
      end
    end

    resources :deals do
      collection do
        post :batch_create
        post :batch_new
        post :batch_update
        post :publish
        get :budget_totals
        get :status_counts
      end
      get :linked_deals
    end
    resources :deal_link_associations, only: %i[create destroy] do
      collection do
        post :batch_create
      end
    end
    resources :deal_links, only: %i[index create update] do
      collection do
        post :batch_create
        patch :batch_update
      end
    end
    resources :deal_placeholders, only: [:index]
    resources :deal_shifts, only: %i[create show], param: :guid do
      post :approve
      post :reject
    end
    resources :external_deals, only: [:index]
    resources :fields, only: %i[index]
    resources :finance_models, only: [:index]
    resources :finance_months, only: [:index]
    resources :finance_uploads, only: [:index]

    get 'images/:name',
        to: 'images#show',
        param: :name,
        defaults: { format: 'jpg' },
        constraints: { name: %r{[^/]+} }

    resources :opportunities, only: %i[index create update]
    resources :opportunity_statuses, only: [:index]
    resources :sales_systems, only: [:index]
    resources :parent_deals, only: %i[index create update] do
      collection do
        post :move
      end
    end
    resources :pat_sponsorships, only: %i[index create destroy]
    resources :pending_shift_requests, only: %i[index update]
    resources :placeholder2, only: [:index]
    resources :portfolio_monetizations, only: [] do
      collection do
        post :upload
      end
    end

    post '/splits/publish', to: 'splits#publish'
    resources :splits, only: [] do
      collection do
        post :locks_and_splits
        get :refresh_splits
      end
    end

    resources :sales_types, only: [] do
      collection do
        post :publish
      end
    end
    resources :saved_templates, only: [:index] do
      post :send_to_mstr
    end
    post '/verticals/publish', to: 'verticals#publish'
    resources :budget_management_scenarios, only: [:index]
    resources :marketplaces, only: [:index] do
      collection do
        post :publish
      end
    end
    resources :properties, only: [] do
      collection do
        post :publish
      end
      get 'image', on: :member
    end
    resources :demographics, only: [:index]
    resources :quarters, only: [:index]
    resources :portal_teams, only: %i[index update create]
    resources :user_preferences, only: [:index]
    resource :agency_of_records, only: [:update]
    resources :aor_ae_assignments, only: [:index]
    patch 'user_preferences', to: 'user_preferences#update'
    resources :advertisers, only: %i[index] do
      post :send_to_mstr
    end
    get 'dropdown_values/:type', to: 'dropdown_values#index'
    resources :user_entitlements, only: [:index]
    resources :registration_types, only: [] do
      collection do
        post :publish
      end
    end
    resources :users, only: %i[index show update] do
      resources :assignments, only: %i[index create update destroy], shallow: true
    end

    namespace 'finance' do
      resources :properties, only: [:index]
      resources :upload, only: [:create]
      resources :revenues, only: [:index]
      patch 'revenues', to: 'revenues#bulk_update'
      resources :revenue_variances, only: [:index]
      patch 'revenue_variances', to: 'revenue_variances#bulk_update'
      resources :cpm, only: [:index]
      patch 'cpm', to: 'cpm#bulk_update'
      resources :units, only: [:index]
      patch 'units', to: 'units#bulk_update'
      resources :ratings_impressions, only: [:index]
      patch 'ratings_impressions', to: 'ratings_impressions#bulk_update'
      resources :make_goods, only: [:index]
      patch 'make_goods', to: 'make_goods#bulk_update'
      resources :risks_opportunities, only: [:index]
      patch 'risks_opportunities', to: 'risks_opportunities#bulk_update'
    end

    namespace 'stealth_mode' do
      resources :agencies, only: [] do
        post :toggle_stealth_mode
        post :toggle_incognito_mode
        post :send_data_to_ag
        post :upload
        collection do
          post :publish
        end
      end
      resources :budgets, only: [] do
        collection do
          post :batch_update
          post :flag_send_to_salesforce
          post :flag_sync_to_salesforce
          post :flag_update_in_salesforce
        end
      end
    end

    fin_actions = %i[metric_types model_types months models headers quarters
                     rating_impressions units cpm make_good allocations
                     property_types revenue_types revenue]
    resource :finance_revenues, only: [:index, *fin_actions] do
      get :index
      fin_actions.each do |action|
        get action
      end
    end
  end
end
# rubocop:enable Metrics/BlockLength
