# frozen_string_literal: true

require_relative 'boot'

require 'rails'
# Pick the frameworks you want:
require 'active_model/railtie'
require 'active_job/railtie'
require 'active_record/railtie'
require 'active_record/connection_adapters/oracle_enhanced_adapter'
require 'action_controller/railtie'
require 'action_mailer/railtie'
require 'action_view/railtie'
require 'action_cable/engine'
# require "sprockets/railtie"
require 'rails/test_unit/railtie'
require 'logging'

# Require the gems listed in Gemfile, including any gems
# you've limited to :test, :development, or :production.
Bundler.require(*Rails.groups)

# Pull secrets from AWS
PamSecretManager::Railtie.load_from_secret_manager if ENV['LOAD_SECRETS']

module Api
  class Application < Rails::Application
    config.time_zone = 'UTC'

    # Initialize configuration defaults for Rails 7.0 (current version)
    config.load_defaults 7.0

    # Enable new connection handling (fixes deprecation warning)
    config.active_record.legacy_connection_handling = false

    # Mimicking rails-4 behavior (keeping for backward compatibility)
    # https://blog.bigbinary.com/2016/02/15/rails-5-makes-belong-to-association-required-by-default.html
    config.active_record.belongs_to_required_by_default = false

    # Settings in config/environments/* take precedence over those specified here.
    # Application configuration should go into files in config/initializers
    # -- all .rb files in that directory are automatically loaded.

    # Only loads a smaller set of middleware suitable for API only apps.
    # Middleware like session, flash, cookies can be added back manually.
    # Skip views, helpers and assets when generating a new resource.
    config.api_only = true

    # Do not use the plural form of table names per NBCU IT conventions
    config.active_record.pluralize_table_names = false

    # Use <table_name>_id for primary keys as well
    config.active_record.primary_key_prefix_type = :table_name_with_underscore

    # Use structure.sql over schema.rb
    config.active_record.schema_format = :sql

    # Autoload lib directory
    lib = root.join('lib')
    config.autoload_paths << lib
    config.eager_load_paths << lib

    config.middleware.use Rack::Deflater

    config.active_job.queue_adapter = :sidekiq
  end
end
