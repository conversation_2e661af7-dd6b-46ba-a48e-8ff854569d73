# frozen_string_literal: true

Rails.application.config.to_prepare do
  VersionTagger.tag if Rails.env.development? || Rails.env.test?

  HealthCheck.setup do |config|
    config.success =
      "Success (SHA: #{PamApiVersion::SHA}) - https://github.com/NBCUniversal/pam-api/commit/#{PamApiVersion::SHA}"
    config.smtp_timeout = 60.0
    config.http_status_for_error_text = 550
    config.http_status_for_error_object = 555
    config.standard_checks = ['database']
  end
end

module HealthCheck
  module AddingShaToProcessCheckErrors
    def process_checks(checks, called_from_middleware = false)
      errors = super
      if errors.present?
        sha_message_with_link =
          "(SHA: #{PamApiVersion::SHA}) - https://github.com/NBCUniversal/pam-api/commit/#{PamApiVersion::SHA}"
        errors << sha_message_with_link unless errors.include?(sha_message_with_link)
      end
      errors
    end
  end

  Utils.singleton_class.prepend(AddingShaToProcessCheckErrors)
end
