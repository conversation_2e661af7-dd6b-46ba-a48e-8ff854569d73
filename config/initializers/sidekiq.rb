# frozen_string_literal: true

unless Rails.env.development? || Rails.env.test?
  Sidekiq.configure_server do |config|
    config.redis = {
      url: "rediss://#{ENV['REDIS_HOST']}:#{ENV['REDIS_PORT']}",
      namespace: "pam_api_#{Rails.env}",
      password: ENV['REDIS_PASSWORD']
    }
  end

  Sidekiq.configure_client do |config|
    config.redis = {
      url: "rediss://#{ENV['REDIS_HOST']}:#{ENV['REDIS_PORT']}",
      namespace: "pam_api_#{Rails.env}",
      password: <PERSON>NV['REDIS_PASSWORD']
    }
  end
end
