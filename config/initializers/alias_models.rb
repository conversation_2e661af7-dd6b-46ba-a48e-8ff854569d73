# frozen_string_literal: true

# alphabetical order please;
# comment out those that will be overwritten in pam-api
AboveTheLine = PamClient::AboveTheLine
Advertiser = PamClient::Advertiser
AdvertiserBrand = PamClient::AdvertiserBrand
AdvertiserClassification = PamClient::AdvertiserClassification
AdvertiserClassificationAssociation = PamClient::AdvertiserClassificationAssociation
AdvertiserClassificationType = PamClient::AdvertiserClassificationType
AdvertiserSummary = PamClient::AdvertiserSummary
AdvertiserSummaryArchiveHeader = PamClient::AdvertiserSummaryArchiveHeader
AdvertiserSummaryArchiveDetail = PamClient::AdvertiserSummaryArchiveDetail
AdvertiserSummaryComment = PamClient::AdvertiserSummaryComment
Agency = PamClient::Agency
AgencyDeal = PamClient::AgencyGateway::AgencyDeal
AgencyDealComment = PamClient::AgencyDealComment
AgencyDealShift = PamClient::AgencyDealShift
AgencyGatewayView = PamClient::AgencyGatewayView
AgencyMarketplaceYear = PamClient::AgencyMarketplaceYear
AgencyPropertyLock = PamClient::AgencyPropertyLock
AgencyOfRecord = PamClient::AgencyOfRecord
AgChangeHistory = PamClient::AgencyGateway::AgChangeHistory
AgPortalNotification = PamClient::AgencyGateway::AgPortalNotification
Allocation = PamClient::Allocation
AorAeAssignment = PamClient::AorAeAssignment
ApiBudget = PamClient::ApiBudget
ApiDeal = PamClient::ApiDeal
ApiParentDeal = PamClient::ApiParentDeal
AppUserTitle = PamClient::AppUserTitle
AssignedAccountExecutive = PamClient::AssignedAccountExecutive
Assignment = PamClient::Assignment
BaseAdvertiser = PamClient::BaseAdvertiser
BaseAgency = PamClient::BaseAgency
BatchHeader = PamClient::BatchHeader
Budget = PamClient::Budget
BudgetSalesSystem = PamClient::BudgetSalesSystem
BudgetYear = PamClient::BudgetYear
BusinessType = PamClient::BusinessType
BuyingAeDealShift = PamClient::BuyingAeDealShift
BuyingClientAeDealShift = PamClient::BuyingClientAeDealShift
CalendarYear = PamClient::CalendarYear
Category = PamClient::Category
CdwFinanceRevenueVw = PamClient::CdwFinanceRevenueVw
ClientAeDealShift = PamClient::ClientAeDealShift
Comment = PamClient::Comment
CommentType = PamClient::CommentType
ConfidenceLevel = PamClient::ConfidenceLevel
Currency = PamClient::Currency
CurrencyDemographic = PamClient::CurrencyDemographic
CurrencyRatingStream = PamClient::CurrencyRatingStream
DataSource = PamClient::DataSource
Daypart = PamClient::Daypart
Deal = PamClient::Deal
DealChanged = PamClient::AgencyGateway::DealChanged
DealLinkAssociation = PamClient::DealLinkAssociation
DealLink = PamClient::DealLink
DealLinkType = PamClient::DealLinkType
DealPlaceholder = PamClient::DealPlaceholder
DealComment = PamClient::DealComment
DealShift = PamClient::DealShift
DealShiftComment = PamClient::DealShiftComment
DealShiftGuid = PamClient::DealShiftGuid
DealShiftOption = PamClient::DealShiftOption
DealTag = PamClient::DealTag
Department = PamClient::Department
DemoDaypartDefault = PamClient::DemoDaypartDefault
Demographic = PamClient::Demographic
Division = PamClient::Division
DropdownResource = PamClient::DropdownResource
Entitlement = PamClient::Entitlement
ExternalDeal = PamClient::ExternalDeal
ExternalDealDetail = PamClient::ExternalDealDetail
ExternalKeyType = PamClient::ExternalKeyType
ExternalSystem = PamClient::ExternalSystem
FeedType = PamClient::FeedType
FileHeader = PamClient::FileHeader
FinanceComment = PamClient::FinanceComment
FinanceCpm = PamClient::FinanceCpm
FinanceCpmView = PamClient::FinanceCpmView
FinanceHeader = PamClient::FinanceHeader
FinanceHeaderView = PamClient::FinanceHeaderView
FinanceMakeGood = PamClient::FinanceMakeGood
FinanceMakeGoodView = PamClient::FinanceMakeGoodView
FinanceMetricType = PamClient::FinanceMetricType
FinanceModel = PamClient::FinanceModel
FinanceModelType = PamClient::FinanceModelType
FinanceMonth = PamClient::FinanceMonth
FinanceQuarter = PamClient::FinanceQuarter
FinanceQuarterMappingView = PamClient::FinanceQuarterMappingView
FinanceRatingImpression = PamClient::FinanceRatingImpression
FinanceRatingImpressionView = PamClient::FinanceRatingImpressionView
FinanceRevenue = PamClient::FinanceRevenue
FinanceRevenueView = PamClient::FinanceRevenueView
FinanceRevenueType = PamClient::FinanceRevenueType
FinanceRiskOpportunity = PamClient::FinanceRiskOpportunity
FinanceRiskOpportunityView = PamClient::FinanceRiskOpportunityView
FinanceUnit = PamClient::FinanceUnit
FinanceUnitView = PamClient::FinanceUnitView
FinanceUpload = PamClient::FinanceUpload
FinanceVarianceDetail = PamClient::FinanceVarianceDetail
FinanceVarianceDetailView = PamClient::FinanceVarianceDetailView
FinanceVarianceHeader = PamClient::FinanceVarianceHeader
FinanceVarianceHeaderView = PamClient::FinanceVarianceHeaderView
GeoState = PamClient::GeoState
GrandParentAgency = PamClient::GrandParentAgency
Location = PamClient::Location
Marketplace = PamClient::Marketplace
MarketplaceDealShift = PamClient::MarketplaceDealShift
MeasurementType = PamClient::MeasurementType
MicrostrategyForm = PamClient::Reports::MicrostrategyForm
MicrostrategyReport = PamClient::MicrostrategyReport
MicrostrategyReportRole = PamClient::MicrostrategyReportRole
ModelAllocation = PamClient::ModelAllocation
NewParentDealDropdownValues = PamClient::NewParentDealDropdownValues
NewRegistrationDropdownValues = PamClient::NewRegistrationDropdownValues
NotReturningReason = PamClient::NotReturningReason
Opportunity = PamClient::Opportunity
OpportunityComment = PamClient::OpportunityComment
OpportunityDetail = PamClient::OpportunityDetail
OpportunityPacingDetailView = PamClient::OpportunityPacingDetailView
OpportunityStatus = PamClient::OpportunityStatus
OptimizationType = PamClient::OptimizationType
PacingBudget = PamClient::PacingBudget
PacingBudgetComment = PamClient::PacingBudgetComment
PacingBudgetDetail = PamClient::PacingBudgetDetail
PacingDropdownValues = PamClient::PacingDropdownValues
PamProcessingStatus = PamClient::PamProcessingStatus
ParentAdvertiser = PamClient::ParentAdvertiser
ParentAgency = PamClient::ParentAgency
ParentDeal = PamClient::ParentDeal
ParentDealType = PamClient::ParentDealType
Partnership = PamClient::Partnership
PatSponsorship = PamClient::PatSponsorship
Pillar = PamClient::Pillar
Placeholder2 = PamClient::Placeholder2
PlannerDealShift = PamClient::PlannerDealShift
# PortalTeam = PamClient::PortalTeam
PortalNotification = PamClient::PortalNotification
PortalType = PamClient::PortalType
PortfolioMonetization = PamClient::PortfolioMonetization
PortfolioQuintile = PamClient::PortfolioQuintile
PortfolioQuintileMview = PamClient::PortfolioQuintileMview
ProcessStatus = PamClient::ProcessStatus
ProductCategory = PamClient::ProductCategory
Property = PamClient::Property
PropertyGroup = PamClient::PropertyGroup
PropertyType = PamClient::PropertyType
Quarter = PamClient::Quarter
QuarterDate = PamClient::QuarterDate
QuarterDateOverride = PamClient::QuarterDateOverride
RatingStream = PamClient::RatingStream
RegistrationAorAssignment = PamClient::RegistrationAorAssignment
RegistrationDropdownValues = PamClient::RegistrationDropdownValues
RegistrationType = PamClient::RegistrationType
Request = PamClient::Reports::Microstrategy::Request
RevenueVarianceMapping = PamClient::RevenueVarianceMapping
Role = PamClient::Role
RoleEntitlement = PamClient::RoleEntitlement
SalesType = PamClient::SalesType
SavedTemplate = PamClient::Reports::SavedTemplate
Season = PamClient::Season
SellingVertical = PamClient::SellingVertical
SfdcAorAeAssignment = PamClient::SfdcAorAeAssignment
SfdcIntgVertical = PamClient::SfdcIntgVertical
ShiftRequest = PamClient::ShiftRequest
ShiftRequestBudget = PamClient::ShiftRequestBudget
ShiftRequestBudgetView = PamClient::ShiftRequestBudgetView
ShiftRequestDetail = PamClient::ShiftRequestDetail
ShiftRequestEvent = PamClient::ShiftRequestEvent
ShiftRequestEventSource = PamClient::ShiftRequestEventSource
ShiftRequestEventState = PamClient::ShiftRequestEventState
ShiftRequestHistory = PamClient::ShiftRequestHistory
ShiftRequestStatus = PamClient::ShiftRequestStatus
ShiftRequestType = PamClient::ShiftRequestType
SpecialEventsDropdownValues = PamClient::SpecialEventsDropdownValues
SponsorshipType = PamClient::SponsorshipType
Status = PamClient::Status
StealthModeBudget = PamClient::StealthModeBudget
Strategy = PamClient::Strategy
Subdivision = PamClient::Subdivision
SystemKeyAssociation = PamClient::SystemKeyAssociation
SystemKeyAssociationType = PamClient::SystemKeyAssociationType
TargetClassification = PamClient::TargetClassification
TrackPulledReport = PamClient::Reports::TrackPulledReport
User = PamClient::User
UserDeal = PamClient::UserDeal
UserEntitlement = PamClient::UserEntitlement
UserFinanceProperty = PamClient::UserFinanceProperty
UserPortalAgency = PamClient::UserPortalAgency
UserPreference = PamClient::UserPreference
UserPreferenceType = PamClient::UserPreferenceType
UserRole = PamClient::UserRole
UserSavedTemplatesView = PamClient::Reports::UserSavedTemplatesView
VarianceType = PamClient::VarianceType
VarianceTypeAllocation = PamClient::VarianceTypeAllocation
Vertical = PamClient::Vertical

ALIAS_MODELS = [
  AboveTheLine,
  Advertiser,
  AdvertiserBrand,
  AdvertiserClassification,
  AdvertiserClassificationAssociation,
  AdvertiserClassificationType,
  AdvertiserSummary,
  AdvertiserSummaryArchiveHeader,
  AdvertiserSummaryArchiveDetail,
  AdvertiserSummaryComment,
  Agency,
  AgencyDeal,
  AgencyDealComment,
  AgencyDealShift,
  AgencyGatewayView,
  AgencyMarketplaceYear,
  AgencyPropertyLock,
  AgencyOfRecord,
  AgChangeHistory,
  AgPortalNotification,
  Allocation,
  AorAeAssignment,
  ApiBudget,
  ApiDeal,
  ApiParentDeal,
  AppUserTitle,
  AssignedAccountExecutive,
  Assignment,
  BaseAdvertiser,
  BaseAgency,
  BatchHeader,
  Budget,
  BudgetSalesSystem,
  BudgetYear,
  BusinessType,
  BuyingAeDealShift,
  BuyingClientAeDealShift,
  CalendarYear,
  Category,
  CdwFinanceRevenueVw,
  ClientAeDealShift,
  Comment,
  CommentType,
  ConfidenceLevel,
  Currency,
  CurrencyDemographic,
  CurrencyRatingStream,
  DataSource,
  Daypart,
  Deal,
  DealChanged,
  DealComment,
  DealLinkAssociation,
  DealLink,
  DealLinkType,
  DealPlaceholder,
  DealShift,
  DealShiftComment,
  DealShiftGuid,
  DealShiftOption,
  DealTag,
  DemoDaypartDefault,
  Demographic,
  Department,
  Division,
  DropdownResource,
  Entitlement,
  ExternalDeal,
  ExternalDealDetail,
  ExternalKeyType,
  ExternalSystem,
  FeedType,
  FileHeader,
  FinanceComment,
  FinanceCpm,
  FinanceCpmView,
  FinanceHeader,
  FinanceHeaderView,
  FinanceMakeGood,
  FinanceMakeGoodView,
  FinanceMetricType,
  FinanceModel,
  FinanceModelType,
  FinanceMonth,
  FinanceQuarter,
  FinanceQuarterMappingView,
  FinanceRatingImpression,
  FinanceRatingImpressionView,
  FinanceRevenue,
  FinanceRevenueView,
  FinanceRevenueType,
  FinanceRiskOpportunity,
  FinanceRiskOpportunityView,
  FinanceUnit,
  FinanceUnitView,
  FinanceUpload,
  FinanceVarianceDetail,
  FinanceVarianceDetailView,
  FinanceVarianceHeader,
  FinanceVarianceHeaderView,
  GeoState,
  GrandParentAgency,
  Location,
  Marketplace,
  MarketplaceDealShift,
  MeasurementType,
  MicrostrategyForm,
  MicrostrategyReport,
  MicrostrategyReportRole,
  ModelAllocation,
  NewParentDealDropdownValues,
  NewRegistrationDropdownValues,
  NotReturningReason,
  Opportunity,
  OpportunityComment,
  OpportunityDetail,
  OpportunityPacingDetailView,
  OpportunityStatus,
  OptimizationType,
  PacingBudget,
  PacingBudgetComment,
  PacingBudgetDetail,
  PacingDropdownValues,
  PamProcessingStatus,
  ParentAdvertiser,
  ParentAgency,
  ParentDeal,
  ParentDealType,
  Partnership,
  PatSponsorship,
  Pillar,
  Placeholder2,
  PlannerDealShift,
  # PortalTeam,
  PortalNotification,
  PortalType,
  PortfolioMonetization,
  PortfolioQuintile,
  PortfolioQuintileMview,
  ProcessStatus,
  ProductCategory,
  Property,
  PropertyGroup,
  PropertyType,
  Quarter,
  QuarterDate,
  QuarterDateOverride,
  RatingStream,
  RegistrationAorAssignment,
  RegistrationDropdownValues,
  RegistrationType,
  Request,
  RevenueVarianceMapping,
  Role,
  RoleEntitlement,
  SalesType,
  SavedTemplate,
  Season,
  SellingVertical,
  SfdcAorAeAssignment,
  SfdcIntgVertical,
  ShiftRequest,
  ShiftRequestBudget,
  ShiftRequestBudgetView,
  ShiftRequestDetail,
  ShiftRequestEvent,
  ShiftRequestEventSource,
  ShiftRequestEventState,
  ShiftRequestHistory,
  ShiftRequestStatus,
  ShiftRequestType,
  SpecialEventsDropdownValues,
  SponsorshipType,
  Status,
  StealthModeBudget,
  Strategy,
  Subdivision,
  SystemKeyAssociation,
  SystemKeyAssociationType,
  TargetClassification,
  TrackPulledReport,
  User,
  UserDeal,
  UserEntitlement,
  UserFinanceProperty,
  UserPortalAgency,
  UserPreference,
  UserPreferenceType,
  UserRole,
  UserSavedTemplatesView,
  VarianceType,
  VarianceTypeAllocation,
  Vertical
].freeze

ALIAS_MODELS.each do |klass|
  klass.instance_eval do
    def to_s
      super.demodulize
    end

    def name
      super.demodulize
    end
  end
end
