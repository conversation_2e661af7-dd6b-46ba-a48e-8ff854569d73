# frozen_string_literal: true

require 'march_hare'
require 'logger'

# Initializes the connection to RabbitMQ and sets global
# `$rabbit_mq_connection` for client use. Connection is closed on application
# shutdown.
class RabbitConnectionInitializer
  include Singleton

  def open?
    @open == true
  end

  def open
    Result.new do
      raise 'Cannot open an already opened connnection' if @open

      $rabbit_mq_connection = connection
      @open = true
      Rails.logger.info('RabbitMQ connection opened')
    end
  end

  def close
    Result.new do
      raise 'Cannot close an unopened connnection' unless @open

      connection&.close
      @open = false
      Rails.logger.info('RabbitMQ connection closed')
    end
  end

  def init_listeners
    dml = Pubsub::DealsMessageListener.new(ENV['INCOMING_FANOUT_NAME'], ENV['INCOMING_DEALS_QUEUE_NAME'])
    fsml = Pubsub::FinalSpendMessageListener.new(ENV['INCOMING_FINAL_SPEND_EX'])
    ndml = Pubsub::NewDealMessageListener.new(ENV['INCOMING_NEW_DEAL_FANOUT_NAME'],
                                              <PERSON><PERSON><PERSON>['INCOMING_NEW_DEALS_QUEUE_NAME'])
    [dml, fsml, ndml].each(&:listen)
    Rails.logger.info('RabbitMQ initialized listeners')
  end

  def connection
    @connection ||= MarchHare.connect(uri: ENV['AMQP_URL'])
  rescue StandardError => e
    Rails.logger.error "Error opening connection to RabbitMQ: '#{e}'"
    Rails.logger.error("Error: #{e.message}")
    Rails.logger.debug(e.backtrace.join('\n'))
  end
end

Rails.application.config.to_prepare do
  unless Rails.env.test?
    rabbit = RabbitConnectionInitializer.instance

    begin
      rabbit.open
      rabbit.init_listeners if Rails.env.development? || Platform.ecs?
    rescue StandardError => e
      Rails.logger.error "Error opening connection to RabbitMQ: '#{e}'"
      Rails.logger.error("Error: #{e.message}")
      Rails.logger.debug(e.backtrace.join('\n'))
    end

    at_exit do
      rabbit.close
    end
  end
end
