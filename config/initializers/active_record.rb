# Updating ActiveRecord::Base to include updating current_user info
# on every update/insert if last_updated_by_id attribute exists
module ActiveRecord
  class Base
    before_save do
      if has_attribute?(:last_updated_by_id)
        current_user_id = PamClient::User.current&.id || -1 # set in application_controller after authentication
        self.last_updated_by_id = current_user_id
      end
    end
  end
end
