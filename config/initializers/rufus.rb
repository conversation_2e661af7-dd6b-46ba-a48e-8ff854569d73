# frozen_string_literal: true

if File.split($PROGRAM_NAME).last == 'sidekiq'
  require 'rufus-scheduler'

  scheduler = Rufus::Scheduler.new

  scheduler.cron('0 0 * * *') do
    LocksAndSplitsRefreshJob.perform_later
    CaeAssignmentsJob.perform_later
  end

  scheduler.every "#{ENV['MVIEW_REFRESH_INTERVAL'] || 1}h" do
    PamClient.constants.select { |klass| klass.to_s[-5..] == 'Mview' }.each do |mview|
      MaterializedViewRefreshJob.perform_later(mview)
    end
  end

  # 8AM ET + adjusting for daylight savings
  et_8am_in_utc = (8 - (Time.now.in_time_zone('Eastern Time (US & Canada)').utc_offset / 60 / 60))
  scheduler.cron("0 #{et_8am_in_utc} * * *") do
    today = Time.now.in_time_zone('Eastern Time (US & Canada)')
    return if today.saturday? || today.sunday?

    DealShiftJanitorJob.perform_later
  end

  # for syncing salesforce industry, agency, advertiser
  # SF_SYNC_FREQUENCY: 5m - 5 minutes, or disabled
  sf_sync_frequency = ENV.fetch('SF_SYNC_FREQUENCY', '5m')

  if sf_sync_frequency == 'disabled'
    puts 'SalesforceSyncJob scheduling disabled'
  else
    scheduler.every sf_sync_frequency do
      SalesforceSyncJob.perform_later
    end
  end
end
