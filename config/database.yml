default: &default
  adapter: oracle_enhanced
  pool: <%= ENV.fetch("RAILS_MAX_THREADS") { 20 } %>
  statement_limit: <%= ENV.fetch('STATEMENT_LIMIT') { 1000 } %>
  open_cursors: <%= ENV.fetch('OPEN_CURSORS') { 1200 } %>
  timeout: 5000

deployed: &deployed
  <<: *default
  host: <%= ENV['SMS_DB_HOST'] %>
  username: <%= ENV['SMS_DB_USERNAME'] %>
  password: <%= ENV['SMS_DB_PASSWORD'] %>
  port: <%= ENV['SMS_DB_PORT'] %>
  database: <%= ENV['SMS_DB_DATABASE'] %>
  structure_dump: db_stored_code

development:
  <<: *default
  host: <%= ENV['SMS_DB_HOST'] || 'localhost' %>
  username: <%= ENV['SMS_DB_USERNAME'] || 'sms_user' %>
  password: <%= ENV['SMS_DB_PASSWORD'] || 'password' %>
  port: <%= ENV['SMS_DB_PORT'] || 1521 %>
  database: <%= ENV['SMS_DB_DATABASE'] || '/ORCLCDB.localdomain' %>
  structure_dump: db_stored_code

# Warning: The database defined as "test" will be erased and
# re-generated from your development database when you run "rake".
# Do not set this db to the same as development or production.
test:
  <<: *default
  host: <%= ENV['SMS_DB_HOST'] || 'localhost' %>
  username: <%= ENV['SMS_DB_USERNAME'] || 'sms_test' %>
  password: <%= ENV['SMS_DB_PASSWORD'] || 'password' %>
  port: <%= ENV['SMS_DB_PORT'] || 1521 %>
  database: <%= ENV['SMS_DB_DATABASE'] || '/ORCLCDB.localdomain' %>
  structure_dump: db_stored_code
  statement_limit: 250

# NOTE: OVERRIDING FOR VM DEV DEPLOY
dev:
  <<: *deployed

qa:
  <<: *deployed

stage:
  <<: *deployed

production:
  <<: *deployed

proddr:
  <<: *deployed