# Be sure to restart your server when you modify this file.

# Your secret key is used for verifying the integrity of signed cookies.
# If you change this key, all old signed cookies will become invalid!

# Make sure the secret is at least 30 characters and all random,
# no regular words or you'll be exposed to dictionary attacks.
# You can use `rails secret` to generate a secure secret key.

# Make sure the secrets in this file are kept private
# if you're sharing your code publicly.

# Shared secrets are available across all environments.

# shared:
#   api_key: a1B2c3D4e5F6

# Environmental secrets are only available for that specific environment.

development:
  secret_key_base: ca02f0829e527e7a0aa6fb52ed10ca8a351e07e18457cd9bc66b0499fa743aaac8444d1091171d9d1927051e41960eb0e5b8976149cea6b43240cf7bf9801c0d

test:
  secret_key_base: 151ac96924f17270ed02fc6691062e5da770783a21c08acbe8bf262453d2dd599b71eb1436590a9d0b4584e1ad173993ad3b9b0c1a6848d9f5b02398b25af23c

# Do not keep production secrets in the unencrypted secrets file.
# Instead, either read values from the environment.
# Or, use `bin/rails secrets:setup` to configure encrypted secrets
# and move the `production:` environment over there.

dev:
  secret_key_base: <%= ENV["SECRET_KEY_BASE"] %>

qa:
  secret_key_base: <%= ENV["SECRET_KEY_BASE"] %>

stage:
  secret_key_base: <%= ENV["SECRET_KEY_BASE"] %>

production:
  secret_key_base: <%= ENV["SECRET_KEY_BASE"] %>
