version: '3'
services:
  web:
    image: pamapi-ci:${JOB_ID}
    environment:
      - RAILS_ENV=test
      - SMS_DB_HOST=test_db
      - SMS_DB_DATABASE=/ORCLCDB.localdomain
      - SMS_API_HOST=host.docker.internal
      - ORACLE_SYSTEM_PASSWORD=oracle
      - PAM_ASSETS_S3_BUCKET_NAME=adsales-pam-assets-private
      - AWS_ACCESS_KEY_ID=TEST # AWS key values needed for specs
      - AWS_SECRET_ACCESS_KEY=TEST
    depends_on:
      - test_db
  test_db:
    container_name: oracledb-pamapi-${JOB_ID}
    image: 904541710863.dkr.ecr.us-east-1.amazonaws.com/nbcu_oracle:latest