name: Build and Deploy

on:
  push:
    branches:
      - release-*
  workflow_dispatch:
    inputs:
      ecr-deployment-account:
        type: string
        description: Account ID of account to deploy images to
        required: false
        default: '************'
      deploy-env:
        type: string
        description: Environment to deploy to
        required: true
        default: dev

env:
  repository-name: pamapi
  deployment-account: ************
  deployment-role: customapps-github-actions-nonprod-role
  prod-deployment-account: ************
  prod-deployment-role: customapps-github-actions-prod-role
  deployment-region: us-east-1
  GH_TOKEN: ${{ secrets.GH_TOKEN }}
  GH_USER: ${{ secrets.GH_USER }}
  BUNDLE_GITHUB__COM: ${{ secrets.GH_TOKEN }}:x-oauth-basic

jobs:
  build:
    runs-on: ubuntu-latest
    permissions:
      id-token: write
      contents: read
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v3
        with:
          persist-credentials: false

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          role-to-assume: arn:aws:iam::${{ env.deployment-account }}:role/${{ env.deployment-role }}
          role-session-name: samplerolesession
          aws-region: ${{ env.deployment-region }}

      - name: Log in to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      - name: Tag Image Dev
        if: startsWith( github.ref_name, 'release' ) || inputs.deploy-env == 'dev' || inputs.deploy-env == 'qa'
        id: tag-image-dev
        uses: ./.github/actions/tag-image
        with:
          sha: ${{ github.sha }}
          environment: dev

      - name: Build Image and Push to ECR
        if: startsWith( github.ref_name, 'release' ) || inputs.deploy-env == 'dev' || inputs.deploy-env == 'qa'
        shell: bash
        run: ./cicd/build-image.sh ${{ env.release-tag }}
      
      - name: Deploy Dev Web
        if: inputs.deploy-env == 'dev'
        uses: ./.github/actions/deploy-container-to-ecs
        with:
          environment: 'dev'
          cluster: 'ecs-custom-apps-pam-dev'
          region: 'us-east-1'
          repository_name: ${{ env.repository-name }}
          ecr_image: ${{ env.ecr-image }}
          ecs_service: pamapidev-service
          task_definition: pamapidev-task-family
          container_command: './deploy/cmd_rails.sh'

      - name: Deploy Dev Worker
        if: inputs.deploy-env == 'dev'
        uses: ./.github/actions/deploy-container-to-ecs
        with:
          environment: 'dev'
          cluster: 'ecs-custom-apps-pam-dev'
          region: ${{ env.deployment-region }}
          repository_name: ${{ env.repository-name }}
          ecr_image: ${{ env.ecr-image }}
          ecs_service: pamapidev-worker-service
          task_definition: pamapidev-worker-task-family
          container_command: './deploy/cmd_worker.sh'

      - name: Tag Image QA
        if: startsWith( github.ref_name, 'release' ) || inputs.deploy-env == 'qa'
        id: tag-image-qa
        uses: ./.github/actions/tag-image
        with:
          sha: ${{ github.sha }}
          environment: qa
          previous-environment: dev

      - name: Deploy QA Web
        if: startsWith( github.ref_name, 'release' ) || inputs.deploy-env == 'qa'
        uses: ./.github/actions/deploy-container-to-ecs
        with:
          environment: 'qa'
          cluster: 'ecs-custom-apps-pam-qa'
          region: ${{ env.deployment-region }}
          repository_name: ${{ env.repository-name }}
          ecr_image: ${{ env.ecr-image }}
          ecs_service: pamapiqa-service
          task_definition: pamapiqa-task-family
          container_command: './deploy/cmd_rails.sh'

      - name: Deploy QA Worker
        if: inputs.deploy-env == 'qa'
        uses: ./.github/actions/deploy-container-to-ecs
        with:
          environment: 'qa'
          cluster: 'ecs-custom-apps-pam-qa'
          region: ${{ env.deployment-region }}
          repository_name: ${{ env.repository-name }}
          ecr_image: ${{ env.ecr-image }}
          ecs_service: pamapiqa-worker-service
          task_definition: pamapiqa-worker-task-family
          container_command: './deploy/cmd_worker.sh'

  build-prod:
    if: startsWith( github.ref_name, 'release' )
    environment: prod
    runs-on: ubuntu-latest
    needs: [build]
    permissions:
      id-token: write
      contents: read
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v3
        with:
          persist-credentials: false

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          role-to-assume: arn:aws:iam::${{ env.deployment-account}}:role/${{ env.deployment-role}}
          role-session-name: samplerolesession
          aws-region: ${{ env.deployment-region}}

      - name: Tag Image Prod
        id: tag-image-prod
        uses: ./.github/actions/tag-image
        with:
          sha: ${{ github.sha }}
          environment: prod
          previous-environment: qa

      - name: Deploy Prod Web
        uses: ./.github/actions/deploy-container-to-ecs
        with:
          environment: 'prod'
          cluster: 'ecs-custom-apps-pam-prod'
          region: ${{ env.deployment-region }}
          repository_name: ${{ env.repository-name }}
          ecr_image: ${{ env.ecr-image }}
          ecs_service: pamapiprod-service
          task_definition: pamapiprod-task-family
          role: ${{ env.prod-deployment-account}}:role/${{ env.prod-deployment-role}}
          container_command: './deploy/cmd_rails.sh'

      - name: Deploy Prod Worker
        uses: ./.github/actions/deploy-container-to-ecs
        with:
          environment: 'prod'
          cluster: 'ecs-custom-apps-pam-prod'
          region: ${{ env.deployment-region }}
          repository_name: ${{ env.repository-name }}
          ecr_image: ${{ env.ecr-image }}
          ecs_service: pamapiprod-worker-service
          task_definition: pamapiprod-worker-task-family
          role: ${{ env.prod-deployment-account}}:role/${{ env.prod-deployment-role}}
          container_command: './deploy/cmd_worker.sh'
