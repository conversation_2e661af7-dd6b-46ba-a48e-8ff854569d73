name: Tests and Linting

on:
  pull_request:
    types: [opened, reopened, synchronize, edited, ready_for_review]

env:
  GH_TOKEN: ${{ secrets.GH_TOKEN }}
  GH_USER: ${{ secrets.GH_USER }}
  BUNDLE_GITHUB__COM: ${{ secrets.GH_TOKEN }}:x-oauth-basic

jobs:
  be_tests:
    runs-on: ubuntu-latest
    permissions:
      id-token: write
      contents: read
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v3
        with:
          persist-credentials: false

      - name: Create additional GitHub variables
        uses: rlespinasse/github-slug-action@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          role-to-assume: arn:aws:iam::904541710863:role/customapps-github-actions-nonprod-role
          role-session-name: samplerolesession
          aws-region: us-east-1

      - name: Log in to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      - name: Run Tests
        shell: bash
        run: ./cicd/docker_be.sh ${{ github.job }}

  checkstyle:
    runs-on: ubuntu-22.04
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Run Rubocop
        uses: ruby/setup-ruby@v1
      - run: |
          sed -i "s/github.com/$GH_TOKEN:<EMAIL>/" Gemfile
          sed -i "s/github.com/$GH_TOKEN:<EMAIL>/" Gemfile.lock
          git config --global url.https://$GH_TOKEN:<EMAIL>/.insteadOf https://github.com/
          git config --global http.sslverify false
          bundle install
          bundle exec rubocop
