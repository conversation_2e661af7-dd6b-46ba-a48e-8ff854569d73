name: Deploy to DR Environment

on:
  workflow_dispatch:
    inputs:
      ecr-deployment-account:
        type: string
        description: Account ID of account to deploy images to
        required: false
        default: '************'
      deploy-env:
        type: string
        description: Environment to deploy to
        required: true
        default: proddr
      image-tag:
        type: string
        description: Tag of the image to deploy 
        required: true

env:
  repository-name: pamapi
  prod-deployment-account: ************
  prod-deployment-role: customapps-github-actions-prod-role
  deployment-region: us-east-1
  GH_TOKEN: ${{ secrets.GH_TOKEN }}
  GH_USER: ${{ secrets.GH_USER }}
  BUNDLE_GITHUB__COM: ${{ secrets.GH_TOKEN }}:x-oauth-basic

jobs:
  update-config:
    runs-on: ubuntu-latest
    permissions:
      id-token: write
      contents: read
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v3
        with:
          persist-credentials: false

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          role-to-assume: arn:aws:iam::${{ env.prod-deployment-account }}:role/${{ env.prod-deployment-role }}
          role-session-name: ${{ github.event.repository.name }}-${{ github.run_id }}-${{ github.job }}
          aws-region: ${{ env.deployment-region }}

      - name: Push config to s3
        shell: bash
        run: |
          aws s3 cp deploy/env/proddr.env s3://adsales-appdev-config/pamapi/env/proddr.env 
  deploy-dr:
    runs-on: ubuntu-latest
    needs: update-config
    permissions:
      id-token: write
      contents: read
    environment:
      name: prod
    steps:
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          role-to-assume: arn:aws:iam::${{ env.prod-deployment-account }}:role/${{ env.prod-deployment-role }}
          role-session-name:  ${{ github.event.repository.name }}-${{ github.run_id }}-${{ github.job }}
          aws-region: ${{ env.deployment-region }}

      - name: Checkout Repository
        uses: actions/checkout@v3
        with:
          persist-credentials: false

      - name: Deploy Prod
        uses: NBCUniversal/dna-github-actions/.github/actions/deploy-container-to-ecs@main
        with:
          environment: prod
          cluster: ecs-custom-apps-pam-prod
          region: ${{ env.deployment-region }}
          container-name: pamapidr
          ecr-image: ${{ inputs.ecr-deployment-account }}.dkr.ecr.${{ env.deployment-region }}.amazonaws.com/${{ env.repository-name }}:${{ inputs.image-tag }}
          task-definition: pamapidr-task-family
          environment-files: 'arn:aws:s3:::adsales-appdev-config/pamapi/env/proddr.env'