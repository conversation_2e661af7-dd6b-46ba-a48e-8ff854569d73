### RELATED STORY ###

__<story_type_all_caps(BUG | FEATURE | CHORE | TECH)>__ - *<story_title>*

\[[#<story_number>](<story_url>)\]

### DESCRIPTION ###

*Add description here - should describe the problem*

### STEPS TO REPRODUCE ISSUE ###

*Only if this PR is to address a bug, should be an ordered (numbered) list*

  1.

  2.

  3.

### CHANGELIST ###

*Changes you made - should be an unordered list, not to deep on details*

### RESEARCH AND RESOURCES ###

*This should be refs to patterns and libraries utilized, to books refereneced, links to blog posts, stack overflow articles, etc..., so knowlege can be shared amongst everyone in the development team, and perhaps more importantly, so that we can document concrete reasons for changes made.*