#!/bin/bash
environment=$1
git_commit_id=$2

# Variable to hold the version of the app
base_version="PAM-API"

function version_generator() {
  if [ $environment = 'qa' ]; then
    image_tag="$base_version-RC-${git_commit_id:0:7}"
  elif [ $environment = 'prod' ]; then
    image_tag="$base_version-${git_commit_id:0:7}"
  else
    image_tag="$base_version-SNAPSHOT-${git_commit_id:0:7}"
  fi
}

#Invoke version generator
version_generator
echo $image_tag
