#!/bin/bash

job_id=$1

DIR="$(cd "$(dirname "$0")" && pwd)"

$DIR/install_libs.sh

JOB_ID=$job_id docker compose -f docker-compose.ci.yml up -d test_db # Bring up test db first so it prepares during build
docker build -t pamapi-ci:$job_id -f Dockerfile .

# Wait for test db to be healthy
echo "Waiting for oracle test db"
while [ "`/usr/bin/docker inspect -f {{.State.Health.Status}} oracledb-pamapi-$job_id`" = "starting" ]; do
    sleep 1;
done;

sleep 60
docker ps -a
JOB_ID=$job_id docker compose -f docker-compose.ci.yml run --rm web bundle exec rake db:migrate || exit 1
JOB_ID=$job_id docker compose -f docker-compose.ci.yml run --rm web bundle exec rspec || exit 1
