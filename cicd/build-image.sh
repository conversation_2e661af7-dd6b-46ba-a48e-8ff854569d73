#!/bin/bash
DIR="$(cd "$(dirname "$0")" && pwd)"
commit_tag=$1

echo "Building PAM API Image...:::$commit_tag"
echo "Inside PAM API Build Scripts with params image:${commit_tag}"

tags_array=(${commit_tag//,/ })

for tag in "${tags_array[@]}"
do
  $DIR/install_libs.sh
  docker build -t pamapi .
  docker tag pamapi:latest 904541710863.dkr.ecr.us-east-1.amazonaws.com/pamapi:$commit_tag
  docker push 904541710863.dkr.ecr.us-east-1.amazonaws.com/pamapi:$commit_tag
done
