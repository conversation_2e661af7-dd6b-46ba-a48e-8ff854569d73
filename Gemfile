# frozen_string_literal: true

source 'https://rubygems.org'

ruby '3.1.4', engine: 'jruby', engine_version: '9.4.3.0'

gem 'activerecord-oracle_enhanced-adapter', github: 'rsim/oracle-enhanced', tag: 'v7.0.3'
gem 'athens'
gem 'aws-sdk-s3'
gem 'caxlsx'
gem 'caxlsx_rails'
gem 'health_check'
gem 'httpclient'
gem 'jbuilder'
gem 'kaminari'
gem 'logging'
gem 'march_hare'
gem 'pam_client', git: 'https://github.com/NBCUniversal/pam_client.git', branch: 'develop'
# gem 'pam_client', path: '../pam_client' # Use this instead of the line above when developing with local pam_client
gem 'faraday', '~> 1.10.0' # Use an older version that's more compatible with JRuby
gem 'faraday-net_http'
gem 'pam_secret_manager', git: 'https://github.com/NBCUniversal/pam_secret_manager.git'
gem 'puma', '~> 3.7'
gem 'rack-cors'
gem 'rails', '~> 7.0.8'
gem 'redis-namespace'
gem 'redis-rails'
gem 'restforce', '~> 7.4.0'
gem 'roo'
gem 'rubyzip', '>= 1.2.1'
gem 'rufus-scheduler'
gem 'sidekiq', '~> 6.5' # sidekiq 7+ removes namespace support
gem 'splitter', git: 'https://github.com/NBCUniversal/splitter.git'
# yanked v0.3.5
gem 'mimemagic', github: 'mimemagicrb/mimemagic', ref: '01f92d86d15d85cfd0f20dabd025dcbd36a8a60f'

group :development, :test do
  gem 'byebug', platforms: %i[mri mingw x64_mingw]
  gem 'dotenv-rails'
  gem 'factory_bot_rails'
  gem 'ffaker'
  gem 'nbc_conventions', git: 'https://github.com/NBCUniversal/nbc_conventions.git'
  gem 'pry'
  gem 'rspec-rails', '~> 4.0'
  gem 'rubocop', '1.57.2', require: false
  gem 'rubocop-performance', require: false
  gem 'webmock'
end

group :development do
  gem 'letter_opener'
  gem 'listen'
end

group :test do
  gem 'bunny-mock'
  gem 'database_cleaner'
  gem 'rack-utf8_sanitizer'
end

gem 'tzinfo-data', platforms: %i[mingw mswin x64_mingw jruby]
