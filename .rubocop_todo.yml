# This configuration was generated by
# `rubocop --auto-gen-config`
# on 2021-01-26 17:51:53 UTC using RuboCop version 1.8.1.
# The point is for the user to remove these configuration records
# one by one as the offenses are removed from the code base.
# Note that changes in the inspected code, or installation of new
# versions of RuboCop, may require this file to be generated again.

# Offense count: 2
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyleAlignWith, Severity.
# SupportedStylesAlignWith: start_of_line, begin
Layout/BeginEndAlignment:
  Exclude:
    - 'lib/error/error_handler.rb'
    - 'spec/support/rules_spec_helper.rb'

# Offense count: 9
# Cop supports --auto-correct.
# Configuration parameters: EmptyLineBetweenMethodDefs, EmptyLineBetweenClassDefs, EmptyLineBetweenModuleDefs, AllowAdjacentOneLineDefs, NumberOfEmptyLines.
Layout/EmptyLineBetweenDefs:
  Exclude:
    - 'app/services/api/finance/upload_services/errors.rb'

# Offense count: 2
# Cop supports --auto-correct.
Layout/RescueEnsureAlignment:
  Exclude:
    - 'lib/error/error_handler.rb'
    - 'spec/support/rules_spec_helper.rb'

# Offense count: 11
# Configuration parameters: AllowedMethods.
# AllowedMethods: enums
Lint/ConstantDefinitionInBlock:
  Exclude:
    - 'spec/api/services/finance/cpm_services/getter_spec.rb'
    - 'spec/api/services/finance/make_good_services/getter_spec.rb'
    - 'spec/api/services/finance/rating_impression_services/getter_spec.rb'
    - 'spec/api/services/finance/revenue_services/digital_getter_spec.rb'
    - 'spec/api/services/finance/revenue_services/linear_getter_spec.rb'
    - 'spec/api/services/finance/revenue_services/updater_spec.rb'
    - 'spec/api/services/finance/revenue_variance_services/getter_spec.rb'
    - 'spec/api/services/finance/risk_opportunity_services/getter_spec.rb'
    - 'spec/api/services/finance/unit_services/getter_spec.rb'

# Offense count: 1
# Configuration parameters: IgnoreLiteralBranches, IgnoreConstantBranches.
Lint/DuplicateBranch:
  Exclude:
    - 'lib/stealth_upload/file_upload.rb'

# Offense count: 1
Lint/MissingSuper:
  Exclude:
    - 'lib/error/errors.rb'

# Offense count: 3
# Configuration parameters: IgnoredPatterns.
# IgnoredPatterns: (?-mix:(exactly|at_least|at_most)\(\d+\)\.times)
Lint/UnreachableLoop:
  Exclude:
    - 'lib/stealth_upload/file_upload.rb'

# Offense count: 7
# Cop supports --auto-correct.
# Configuration parameters: AllowComments.
Lint/UselessMethodDefinition:
  Exclude:
    - 'app/services/api/finance/cpm_services/updater.rb'
    - 'app/services/api/finance/make_good_services/updater.rb'
    - 'app/services/api/finance/rating_impression_services/updater.rb'
    - 'app/services/api/finance/revenue_variance_services/detail_getter.rb'
    - 'app/services/api/finance/revenue_variance_services/summary_getter.rb'
    - 'app/services/api/finance/risk_opportunity_services/updater.rb'
    - 'app/services/api/finance/unit_services/updater.rb'

# Offense count: 42
# Configuration parameters: IgnoredMethods, CountRepeatedAttributes.
Metrics/AbcSize:
  Max: 45

# Offense count: 5
# Configuration parameters: CountComments, CountAsOne, ExcludedMethods, IgnoredMethods.
# IgnoredMethods: refine
Metrics/BlockLength:
  Max: 159

# Offense count: 7
# Configuration parameters: CountComments, CountAsOne.
Metrics/ClassLength:
  Max: 281

# Offense count: 9
# Configuration parameters: IgnoredMethods.
Metrics/CyclomaticComplexity:
  Max: 12

# Offense count: 88
# Configuration parameters: CountComments, CountAsOne, ExcludedMethods, IgnoredMethods.
Metrics/MethodLength:
  Max: 45

# Offense count: 7
# Configuration parameters: CountComments, CountAsOne.
Metrics/ModuleLength:
  Max: 327

# Offense count: 8
# Configuration parameters: IgnoredMethods.
Metrics/PerceivedComplexity:
  Max: 13

# Offense count: 4
# Configuration parameters: EnforcedStyleForLeadingUnderscores.
# SupportedStylesForLeadingUnderscores: disallowed, required, optional
Naming/MemoizedInstanceVariableName:
  Exclude:
    - 'lib/stealth_upload/mapping/rule.rb'
    - 'lib/stealth_upload/mapping/rule_set.rb'

# Offense count: 2
# Configuration parameters: NamePrefix, ForbiddenPrefixes, AllowedMethods, MethodDefinitionMacros.
# NamePrefix: is_, has_, have_
# ForbiddenPrefixes: is_, has_, have_
# AllowedMethods: is_a?
# MethodDefinitionMacros: define_method, define_singleton_method
Naming/PredicateName:
  Exclude:
    - 'spec/**/*'
    - 'lib/stealth_upload/mapping/rule_set.rb'

# Offense count: 60
# Configuration parameters: EnforcedStyle, CheckMethodNames, CheckSymbols, AllowedIdentifiers.
# SupportedStyles: snake_case, normalcase, non_integer
# AllowedIdentifiers: capture3, iso8601, rfc1123_date, rfc822, rfc2822, rfc3339
Naming/VariableNumber:
  Exclude:
    - 'spec/api/deals_spec.rb'
    - 'spec/api/external_deals_spec.rb'
    - 'spec/api/finance/properties_spec.rb'
    - 'spec/api/finance/upload_spec.rb'
    - 'spec/controllers/concerns/sortable_examples.rb'
    - 'spec/support/shared_context/ag_new_deal_data.rb'

# Offense count: 1
# Cop supports --auto-correct.
Performance/BlockGivenWithExplicitBlock:
  Exclude:
    - 'app/services/pubsub/rpc_client.rb'

# Offense count: 3
# Configuration parameters: MinSize.
Performance/CollectionLiteralInLoop:
  Exclude:
    - 'app/helpers/ag_email_notification_helper.rb'
    - 'app/services/api/finance/revenue_services/digital_getter.rb'
    - 'app/services/pubsub/agency_deals_parser.rb'

# Offense count: 5
# Cop supports --auto-correct.
Performance/Sum:
  Exclude:
    - 'lib/stealth_upload/file_upload.rb'
    - 'lib/stealth_upload/upload/mapped_row.rb'
    - 'spec/api/deals_spec.rb'

# Offense count: 14
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle.
# SupportedStyles: separated, grouped
Style/AccessorGrouping:
  Exclude:
    - 'lib/stealth_upload/file_upload.rb'
    - 'lib/stealth_upload/mapping/registration_column.rb'
    - 'lib/stealth_upload/upload/mapped_cell.rb'
    - 'lib/stealth_upload/upload/mapped_row.rb'

# Offense count: 117
Style/Documentation:
  Enabled: false

# Offense count: 1
# Cop supports --auto-correct.
Style/GlobalStdStream:
  Exclude:
    - 'config/environments/production.rb'

# Offense count: 3
# Configuration parameters: AllowedVariables.
Style/GlobalVars:
  Exclude:
    - 'app/services/pubsub/messenger.rb'
    - 'config/initializers/rabbit_connection.rb'
    - 'spec/support/shared_context/direct_message_listener.rb'

# Offense count: 3
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle.
# SupportedStyles: braces, no_braces
Style/HashAsLastArrayItem:
  Exclude:
    - 'app/controllers/api/deals_controller.rb'
    - 'app/services/pubsub/fanout_message_listener.rb'

# Offense count: 1
# Cop supports --auto-correct.
# Configuration parameters: AllowIfModifier.
Style/IfInsideElse:
  Exclude:
    - 'app/services/api/portal_teams_service.rb'

# Offense count: 1
Style/MultilineBlockChain:
  Exclude:
    - 'app/services/api/finance/revenue_variance_services/summary_getter.rb'

# Offense count: 1
# Configuration parameters: AllowedMethods.
# AllowedMethods: respond_to_missing?
Style/OptionalBooleanParameter:
  Exclude:
    - 'config/initializers/health_check.rb'

# Offense count: 1
# Cop supports --auto-correct.
Style/RedundantFileExtensionInRequire:
  Exclude:
    - 'app/controllers/application_controller.rb'

# Offense count: 1
# Cop supports --auto-correct.
Style/RedundantParentheses:
  Exclude:
    - 'app/services/api/finance/updater.rb'

# Offense count: 2
# Cop supports --auto-correct.
Style/StringConcatenation:
  Exclude:
    - 'spec/api/finance/upload_spec.rb'
    - 'spec/api/portfolio_monetizations_spec.rb'

# Offense count: 1
Lint/MissingSuper:
  Exclude:
    - 'lib/error/api_error.rb'
